# IMSON (Improvised Social Network)

- _Welcome the futuristic social network platform that mimics official platforms like facebook._

## Table of Contents

- [IMSON (Improvised Social Network)](#imson-improvised-social-network)
  - [Table of Contents](#table-of-contents)
  - [Project Structure](#project-structure)
    - [File System](#file-system)
    - [Frontend](#frontend)
    - [Dockerization](#dockerization)
    - [Automation](#automation)
    - [Run](#run)
  - [Authentication](#authentication)
    - [Registration](#registration)
      - [Logging in](#logging-in)

## Project Structure

### File System

- _The project is structured into the following folders and files:_

```sh
.
│
├── backend
│   ├── pkg
│   │   ├── db
│   │   │   ├── migrations
│   │   │   │   └── sqlite
│   │   │   │       ├── 000001_create_users_table.down.sql
│   │   │   │       ├── 000001_create_users_table.up.sql
│   │   │   │       ├── 000002_create_posts_table.down.sql
│   │   │   │       └── 000002_create_posts_table.up.sql
│   │   │   └── sqlite
│   │   │       └── sqlite.go
│   │   │
│   │   └── ...other_pkgs.go
│   │
│   ├── Dockerfile
│   │
│   └── server.go
│
│
├── docs
│   ├── LICENSE.md
│   ├── CODE_OF_CONDUCT.md
│   ├── SECURITY.md
│   ├── Instructions.md
│   └── CONTRIBUTING.md
│
├── frontend/
│   ├── Dockerfile
│   ├── package.json
│   ├── tailwind.config.js
│   ├── postcss.config.js
│   └── src
│       ├── pages
│       │   ├── _app.tsx
│       │   ├── index.tsx
│       │   └── login.tsx
│       ├── components
│       │   └── Navbar.tsx
│       └── styles
│           └── globals.css
│
├── .gitignore
├── .gitkeep
├── docker-compose.yml
├── Makefile
└── README.md
```

### Frontend

- _Imson will use a react-based frontend framework, `Next.js` to model the frontend components and features_
- _On downloading the repository, make sure you have nextjs, if not follow the instructions below:_

```sh
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
source ~/.bashrc  # Or ~/.zshrc if you use Zsh
```

then verify the installation of npm

```sh
command -v nvm
nvm install node
nvm use node
nvm alias default node
```

then verify the versions

```sh
node -v
npm -v
```

you might want to restart your code editor to see the changes take effect.

- _On downloading the repository run the following commands to install next.js and the expected output follows:_

```sh
(base) ┌──(anxiel㉿anxiel)-[~/…/gitea/scl/imson/backend]
└─$ mkdir frontend; cd ../frontend 
                                                                                                                                                                                                       
(base) ┌──(anxiel㉿anxiel)-[~/…/gitea/scl/imson/frontend]
└─$ npx create-next-app@latest . --js

Need to install the following packages:
create-next-app@15.3.3
Ok to proceed? (y) y

# (the ones in brackets  should be selected)
✔ Would you like to use ESLint? … No / (Yes)
✔ Would you like to use Tailwind CSS? … No / (Yes)
✔ Would you like your code inside a `src/` directory? … No / (Yes)
✔ Would you like to use App Router? (recommended) … No / (Yes)
✔ Would you like to use Turbopack for `next dev`? … (No) / Yes
✔ Would you like to customize the import alias (`@/*` by default)? … (No) / Yes
Creating a new Next.js app in /home/<USER>/projects/gitea/scl/imson/frontend.

Using npm.

Initializing project with template: app-tw 


Installing dependencies:
- react
- react-dom
- next

Installing devDependencies:
- @tailwindcss/postcss
- tailwindcss
- eslint
- eslint-config-next
- @eslint/eslintrc


added 336 packages, and audited 337 packages in 1m

137 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
Success! Created frontend at /home/<USER>/projects/gitea/scl/imson/frontend
```

### Dockerization

- _Every service(frontend and the backend) will be dockerized independently and running the whole project appliation will use docker compose to run both services synchronously._

### Automation

- _The application will be automated by the use of a makefile_

### Run

- _Run the application to see the current implementation and see what needs to be added and/or modified_

```sh
make run-dev
```

- _Open the application on port `:3000`_

## Authentication

- Imson Application uses JWT for authentication in the fronted and encryption for the backend(using the google-uuid) package for the encryption.
- The sessions are stored in the database and are encrypted using the google-uuid package.
- The sessions are persistent: meaning that the users session in one client persiste in the tabs of that browser. Meanwhile maintaining the session in one client and deleting any other session in another client.

### Registration

- Users are able to connect on imson after some validation checkpoint(the applciation's authentication platform).
- The following are some of the things required to register to the platform: names, email, password, and the user's profile picture.

#### Logging in

- Logging in to the application is pretty easy as a password combined with either an email or a username.
