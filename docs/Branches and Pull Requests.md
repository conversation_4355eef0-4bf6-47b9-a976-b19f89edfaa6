# Branches and Pull Requests according to the, Child-Collapse-Workflow

- Too many branches where do I PR to?
- Where do I work?
- When I move to another branch what am I supposed to do?
- In this document I will inform all the developers in this project about the branches and the pull requests.

## Branches

- As the application grows, so does the number of branches. This is because we have different-paced developers, people workingin their own paces.
- However, the number of branches should not be an issue because you have a PM! And well due to circumstances, we might not be able to meet everyday, so instead I will write this documentation to guide you on how to work with your branches.
- First things first, _"Do not push any other branch to the remote repository"_
- When assigned an issue, a branch will be accompanied with the issue you are issued and all you have to do is fetch the branch and do some little more actions and you will be ready to start your tasks specified to the branch.

### What should you do when assigned an Issue?

- When you are assigned an issue, the first thing is to fetch the latest branches from the remote repo:

```sh
git fetch origin
```

after fetching the branches, the next thing you should do is refer to the next section of the documentation which branch should be parenting the current branch you are into.
- What does that mean? Well this is what I mean. Each and every `Milestone` has a branch, notably `feature/...` these form the children branches of the main  branch.
- Further into, we have sub-issues distinctively `frontend/...` and `backend/...` these form the parental sub-issues to the final sub-issues of the final issues that are distinct to the tasks you were assigned.
- You should check the parenting  branch, to know what was implemented.
here is a photometric workflow of the idea these sentences are saying:
![ChatGPT Image Jun 25, 2025, 04_55_41 PM](https://github.com/user-attachments/assets/51230b3f-456c-4e52-ac85-58a9b7ccc281)

- Due to the differing paces of the team members, there will be confusions in the contents of the branches, and so, be ware!
- After fetching the required branch, checkout to it and pull the remote changes. After which you should pull the changes of its parenting branch(changes that might have been merged but you do not have in this secluded branch) if you find conflicts from this point, please find the PM and seek clarification, if you do not understand what should stay and what should'nt  in your branch at this point.


## Pull Request

- For Pull requests, the parent branches matter a lot
- Before issuing a Pull Request to the parent branch of your issue, and if you are the last person to complete your task from the grouped tasks then you should issue another Pull Request, after that is mered to the parent branch, of the parent branch to its preceeding parent branch as well. What do I mean? Say for instance you were working on an issue called `accepting or declining friend requests` then it is evident that this issue is linked to a parent branch `requests` which also had another child branch besides yours called `send-request` where the developer implemented a handler for sending a request to a user, whereas you inmplemented accepting or declining that request. The 2 sub-issues are child issues to another issue that mentioned friend requests in general, which also had a branch, `friend-request`, this is how the flow is expected to go, devA will checkout into the `accept/decline` branch fetch changes from the brach `friend-request`, implement the logic then issue a PR to the branch `friend-request` after which, when merged, devB does work pretty slowly, so he will finish late, devB goes to the branch `send-request`, pulls the changes that have just been merged from the `accept/decline` branch into theirs to avoid conflicts.
- After this is done, devB implements their tasks well and issues a PR to the `friend-request` branch which will, by then, have no conflicts. After this is merged reviewed and merged successfully, there will be no other implementation waiting to be done in the branch `friend-request` branch and so, this will need to be merged to its parent branch, in this case maybe `feature/requests-(something-else)` and who makes the PR you ask? **devB** ofcourse. Why? devB was last to finish the tasks for the sub issue.
- This will help us maintain a good workflow and it saves on time as no developer, who works slowly is waited upon to finish so that the project can move forward.

- Again in this `Child-Collapse` workflow, the PM has the most complicated duties. Not complex, I said the most _complicated!_

