# Frontend - Next.js Application

This is the frontend of the project built using [Next.js](https://nextjs.org/).

##  Getting Started

Follow the steps below to run the application locally.

### Prerequisites

- [Node.js](https://nodejs.org/) (v18 or above recommended)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)

### 🛠️ Installation

1. **Navigate to the frontend directory**:
   ```bash
   cd frontend
````

2. **Install dependencies**:

   ```bash
   npm install
   ```

   or if using yarn:

   ```bash
   yarn install
   ```

###  Running the Development Server

Start the development server with:

```bash
npm run dev
```

or

```bash
yarn dev
```

By default, the app will run at: [http://localhost:3000](http://localhost:3000)

### Project Structure

```bash
frontend/
├── components/        # Reusable UI components
├── pages/             # Application routes
├── public/            # Static assets
├── styles/            # Global and modular CSS
├── utils/             # Helper functions
├── package.json       # Project metadata and scripts
└── next.config.js     # Next.js configuration
```

### Scripts

* `npm run dev` - Start development server
* `npm run build` - Build the application for production
* `npm start` - Start the production server after building
