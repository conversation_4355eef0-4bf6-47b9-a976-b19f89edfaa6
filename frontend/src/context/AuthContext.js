'use client'
import { createContext, useEffect, useState, useContext } from 'react'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      // Fetch complete user information
      fetchCurrentUser()
    } else {
      setLoading(false)
    }
  }, [])

  const fetchCurrentUser = async () => {
    try {
      const res = await fetch('http://localhost:33233/api/user/current', {
        method: 'GET',
        credentials: 'include',
      })

      if (res.ok) {
        const userData = await res.json()
        setUser(userData)
      } else {
        // Token might be invalid, clear it
        localStorage.removeItem('token')
        setUser(null)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      localStorage.removeItem('token')
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email, password) => {
    setLoading(true)
    const res = await fetch('http://localhost:33233/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ email, password }),
    })

    if (res.ok) {
      const data = await res.json();
      if (data.token) {
        localStorage.setItem('token', data.token);
        // Fetch complete user information
        await fetchCurrentUser();
        return true;
      }
    }
    setLoading(false)
    return false;
  }

  const signup = async (formData) => {
    setLoading(true)
    try {
      const res = await fetch('http://localhost:33233/api/signup', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      })

      setLoading(false)
      if (!res.ok) return false

      const data = await res.json()
      setUser(data.user)
      return true
    } catch (error) {
      setLoading(false)
      return false
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    setUser(null)
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        signup,
        fetchCurrentUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  return useContext(AuthContext)
}
