const WebSocketManager = {
  socket: null,
  url: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  reconnectDelay: 1000,
  enableReconnection: true,
  handlers: {},

  init(url, options = {}) {
    this.url = url;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectDelay = options.reconnectDelay || 1000;
    this.enableReconnection = options.enableReconnection !== false;
    this.handlers = options.handlers || {};
    this.connect();
  },

  connect() {
    if (!this.enableReconnection) return;

    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      return;
    }

    this.socket = new WebSocket(this.url);

    this.socket.onopen = () => {
      console.log("[WS] Connected to", this.url);
      this.reconnectAttempts = 0;
    };

    this.socket.onclose = (event) => {
      console.warn("[WS] Disconnected", event.code, event.reason);
      if (this.enableReconnection && this.reconnectAttempts < this.maxReconnectAttempts) {
        const delay = Math.min(this.reconnectDelay * 2 ** this.reconnectAttempts, 30000);
        setTimeout(() => {
          this.reconnectAttempts++;
          this.connect();
        }, delay);
      }
    };

    this.socket.onerror = (error) => {
      console.error("[WS] Error:", error);
      this.socket.close();
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.type && this.handlers[message.type]) {
          this.handlers[message.type](message.data);
        }
      } catch (e) {
        console.error("[WS] Invalid JSON message", e);
      }
    };
  },

  send(type, data) {
    const message = JSON.stringify({ type, data });

    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(message);
    } else {
      console.warn("[WS] Cannot send. Socket not open.");
    }
  },

  close() {
    this.enableReconnection = false;
    if (this.socket) this.socket.close();
  },
};

export default WebSocketManager;
