'use client'
import React, { createContext, useEffect } from "react";
import WebSocketManager from "./WebSocketManager";

// Create a Context to provide WebSocketManager
export const WebSocketContext = createContext(null);

export const WebSocketProvider = ({ children }) => {
  useEffect(() => {
    // Temporarily disable WebSocket to focus on profile functionality
    console.log("WebSocket temporarily disabled for debugging");

    // TODO: Re-enable WebSocket after user authentication is working
    // const wsUrl = "ws://localhost:33233/api/ws";
    // WebSocketManager.init(wsUrl, {
    //   maxReconnectAttempts: 10,
    //   reconnectDelay: 1000,
    //   handlers: {
    //     new_post: (data) => {
    //       console.log("New post received:", data);
    //     },
    //   },
    // });

    return () => {
      // WebSocketManager.close();
    };
  }, []);

  return (
    <WebSocketContext.Provider value={WebSocketManager}>
      {children}
    </WebSocketContext.Provider>
  );
};
