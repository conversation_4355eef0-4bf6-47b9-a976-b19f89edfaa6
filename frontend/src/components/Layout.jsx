'use client'
import { usePathname } from 'next/navigation'
import Navbar from './Navbar'

export default function Layout({ children }) {
  const pathname = usePathname()
  
  // Pages that should not show the navbar
  const noNavbarPages = ['/login', '/register', '/']
  
  const shouldShowNavbar = !noNavbarPages.includes(pathname)

  return (
    <div className="min-h-screen bg-gray-50">
      {shouldShowNavbar && <Navbar />}
      <main className={shouldShowNavbar ? 'pt-0' : ''}>
        {children}
      </main>
    </div>
  )
}
