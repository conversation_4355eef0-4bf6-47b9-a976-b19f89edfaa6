'use client'

import { useState } from 'react'

export default function FriendRequests() {
  const [requests, setRequests] = useState([
    { id: 1, name: '<PERSON>', avatar: '/img/default-avatar.png' },
    { id: 2, name: '<PERSON>', avatar: '/img/default-avatar.png' },
  ])

  const handleResponse = (id, accepted) => {
    setRequests(requests.filter((r) => r.id !== id))
    alert(`${accepted ? 'Accepted' : 'Declined'} friend request from ${id}`)
  }

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-xl p-4 space-y-4">
      <h2 className="text-lg font-semibold text-white">Friend Requests</h2>
      {requests.length === 0 ? (
        <p className="text-gray-400 text-sm">No new requests.</p>
      ) : (
        <div className="space-y-3">
          {requests.map((req) => (
            <div
              key={req.id}
              className="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-gray-900 rounded-md p-3 gap-3"
            >
              <div className="flex items-center gap-3">
                <img
                  src={req.avatar}
                  alt={req.name}
                  className="w-8 h-8 rounded-full border border-gray-600"
                />
                <span className="text-sm font-medium">{req.name}</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleResponse(req.id, true)}
                  className="bg-green-600 hover:bg-green-700 px-3 py-1 text-xs rounded text-white"
                >
                  Accept
                </button>
                <button
                  onClick={() => handleResponse(req.id, false)}
                  className="bg-red-600 hover:bg-red-700 px-3 py-1 text-xs rounded text-white"
                >
                  Decline
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
