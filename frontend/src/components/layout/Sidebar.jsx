'use client'
import { useState, useEffect } from 'react'
import { FaBars } from 'react-icons/fa'
import Link from 'next/link'

export default function Sidebar() {
  const [isOpen, setIsOpen] = useState(false)
  const [user, setUser] = useState(null)

  useEffect(() => {
    const handleResize = () => {
      setIsOpen(window.innerWidth >= 768)
    }
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      console.log('Sidebar: Fetching user data...')
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })

      console.log('Sidebar: Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Sidebar: User data received:', data)
        setUser(data)
      } else {
        console.log('Sidebar: API call failed with status:', response.status)
        const errorText = await response.text()
        console.log('Sidebar: Error response:', errorText)

        // Use fallback data if API fails
        setUser({
          firstName: 'Guest',
          lastName: 'User',
          nickname: 'guest',
          avatarUrl: ''
        })
      }
    } catch (error) {
      console.error('Sidebar: Error fetching user data:', error)
      // Use fallback data if API fails
      setUser({
        firstName: 'Guest',
        lastName: 'User',
        nickname: 'guest',
        avatarUrl: ''
      })
    }
  }

  if (!user) {
    return (
      <aside className="bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 w-full md:w-80 hidden md:block">
        <div className="p-4">
          <div className="animate-pulse">
            <div className="flex items-center space-x-3 p-2">
              <div className="w-9 h-9 rounded-full bg-gray-300"></div>
              <div className="h-4 bg-gray-300 rounded w-24"></div>
            </div>
          </div>
        </div>
      </aside>
    )
  }

  return (
    <>
      {/* Mobile hamburger toggle */}
      <div className="md:hidden p-4 bg-gray-900">
        <button onClick={() => setIsOpen(!isOpen)} className="text-white text-xl">
          <FaBars />
        </button>
      </div>

      {/* Facebook-style Sidebar */}
      <aside
        className={`bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 w-full md:w-80 ${
          isOpen ? 'block' : 'hidden'
        } md:block`}
      >
        <div className="p-4">

          {/* User Profile Link - Facebook style */}
          <Link
            href="/account"
            className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group mb-4"
          >
            {user.avatarUrl ? (
              <img
                src={user.avatarUrl}
                alt={`${user.firstName} ${user.lastName}`}
                className="w-9 h-9 rounded-full object-cover"
              />
            ) : (
              <div className="w-9 h-9 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
                {user.firstName?.charAt(0) || 'U'}{user.lastName?.charAt(0) || ''}
              </div>
            )}
            <span className="text-gray-900 dark:text-white font-medium text-[15px]">
              {user.firstName} {user.lastName}
            </span>
          </Link>

          {/* Navigation Links */}
          <nav className="space-y-2">
            <Link
              href="/home"
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="w-9 h-9 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 text-lg">🏠</span>
              </div>
              <span className="text-gray-900 dark:text-white font-medium text-[15px]">Home</span>
            </Link>
          </nav>

        </div>
      </aside>
    </>
  )
}
