'use client'
import Link from 'next/link'
import { useAuth } from '@/context/AuthContext'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export default function Navbar() {
  const { user, logout } = useAuth()
  const router = useRouter()
  const [menuOpen, setMenuOpen] = useState(false)

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  const getInitial = () => {
    if (user?.firstName) return user.firstName.charAt(0).toUpperCase()
    return 'A'
  }

  return (
    <nav className="w-full bg-gray-900 text-white px-4 py-3 shadow-md flex justify-between items-center">
      {/* Logo */}
      <Link
        href="/"
        className="text-2xl font-bold tracking-wider"
        style={{ fontFamily: "'Orbitron', sans-serif" }}
      >
        IMSON
      </Link>

      {/* Avatar / Account */}
      <div className="relative">
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg transition"
        >
          {user ? getInitial() : 'A'}
        </button>

        {menuOpen && (
          <div className="absolute right-0 mt-2 w-40 bg-gray-800 shadow-lg rounded-md overflow-hidden z-50">
            {user ? (
              <>
                <Link
                  href="/dashboard"
                  className="block px-4 py-2 hover:bg-gray-700"
                  onClick={() => setMenuOpen(false)}
                >
                  Dashboard
                </Link>
                <Link
                  href="/dashboard/profile"
                  className="block px-4 py-2 hover:bg-gray-700"
                  onClick={() => setMenuOpen(false)}
                >
                  Profile
                </Link>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 hover:bg-gray-700"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <Link
                  href="/login"
                  className="block px-4 py-2 hover:bg-gray-700"
                  onClick={() => setMenuOpen(false)}
                >
                  Login
                </Link>
                <Link
                  href="/signup"
                  className="block px-4 py-2 hover:bg-gray-700"
                  onClick={() => setMenuOpen(false)}
                >
                  Signup
                </Link>
              </>
            )}
          </div>
        )}
      </div>
    </nav>
  )
}
