'use client'
import { useState, useEffect, useRef } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { FaHome, FaUsers, FaBell, FaEnvelope, FaSearch, FaPlus, FaUser, FaCog, FaSignOutAlt, FaChevronDown } from 'react-icons/fa'

export default function Navbar() {
  const router = useRouter()
  const pathname = usePathname()
  const [userData, setUserData] = useState(null)
  const [showProfileMenu, setShowProfileMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [notifications, setNotifications] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const profileMenuRef = useRef(null)
  const notificationsRef = useRef(null)

  useEffect(() => {
    fetchUserData()
    fetchNotifications()
  }, [])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        setShowProfileMenu(false)
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setShowNotifications(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const fetchUserData = async () => {
    try {
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setUserData(data)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  const fetchNotifications = async () => {
    try {
      const response = await fetch('http://localhost:33233/api/notifications', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.slice(0, 5)) // Show only 5 recent notifications
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('http://localhost:33233/api/logout', {
        method: 'POST',
        credentials: 'include'
      })
      router.push('/login')
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const isActive = (path) => pathname === path

  const navItems = [
    { icon: FaHome, path: '/home', label: 'Home' },
    { icon: FaUsers, path: '/groups', label: 'Groups' },
    { icon: FaEnvelope, path: '/messages', label: 'Messages' },
  ]

  return (
    <nav className="bg-white shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 lg:px-6">
        <div className="flex items-center justify-between h-16">
          {/* Left Section - Logo & Search */}
          <div className="flex items-center space-x-6">
            {/* Logo */}
            <div
              onClick={() => router.push('/home')}
              className="flex items-center space-x-2 cursor-pointer group"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-200">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent hidden sm:block">
                Social
              </span>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="hidden md:block">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search Social..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-80 pl-12 pr-4 py-3 border border-gray-200 rounded-full bg-gray-50 text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 shadow-sm"
                />
              </div>
            </form>

            {/* Mobile Search Button */}
            <button className="md:hidden flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 shadow-sm">
              <FaSearch className="w-4 h-4 text-gray-600" />
            </button>
          </div>

          {/* Center Section - Navigation */}
          <div className="hidden lg:flex items-center">
            <div className="flex items-center bg-gray-50 rounded-full p-1 shadow-inner">
              {navItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => router.push(item.path)}
                  className={`relative flex items-center justify-center w-14 h-12 rounded-full transition-all duration-200 group ${
                    isActive(item.path)
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-gray-600 hover:bg-white hover:shadow-md'
                  }`}
                  title={item.label}
                >
                  <item.icon className={`w-5 h-5 transition-transform duration-200 ${
                    isActive(item.path) ? 'scale-110' : 'group-hover:scale-105'
                  }`} />

                  {/* Tooltip */}
                  <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    {item.label}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex lg:hidden items-center space-x-1">
            {navItems.map((item) => (
              <button
                key={item.path}
                onClick={() => router.push(item.path)}
                className={`flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 ${
                  isActive(item.path)
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
                title={item.label}
              >
                <item.icon className="w-4 h-4" />
              </button>
            ))}
          </div>

          {/* Right Section - Actions & Profile */}
          <div className="flex items-center space-x-3">
            {/* Create Post Button */}
            <button
              onClick={() => router.push('/home')}
              className="hidden md:flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
            >
              <FaPlus className="w-4 h-4" />
              <span className="text-sm">Create</span>
            </button>

            {/* Mobile Create Button */}
            <button
              onClick={() => router.push('/home')}
              className="md:hidden flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg"
            >
              <FaPlus className="w-4 h-4" />
            </button>

            {/* Notifications */}
            <div className="relative" ref={notificationsRef}>
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 shadow-sm group"
              >
                <FaBell className="w-5 h-5 text-gray-600 group-hover:text-gray-700" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-lg animate-pulse">
                    {notifications.length > 9 ? '9+' : notifications.length}
                  </span>
                )}
              </button>

              {showNotifications && (
                <div className="absolute right-0 top-full mt-3 w-96 bg-white rounded-xl shadow-2xl border border-gray-100 overflow-hidden">
                  <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900 text-lg">Notifications</h3>
                      <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        Mark all read
                      </button>
                    </div>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.map((notification, index) => (
                        <div key={index} className="px-6 py-4 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150">
                          <div className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                              <p className="text-xs text-gray-600 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-400 mt-2">2 minutes ago</p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="px-6 py-12 text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <FaBell className="w-8 h-8 text-gray-400" />
                        </div>
                        <h4 className="text-gray-900 font-medium mb-1">No notifications yet</h4>
                        <p className="text-gray-500 text-sm">When you get notifications, they'll show up here</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Profile Menu */}
            <div className="relative" ref={profileMenuRef}>
              <button
                onClick={() => setShowProfileMenu(!showProfileMenu)}
                className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-all duration-200 group"
              >
                <div className="relative">
                  {userData?.avatarUrl ? (
                    <img
                      src={`http://localhost:33233${userData.avatarUrl}`}
                      alt="Profile"
                      className="w-10 h-10 rounded-full object-cover border-2 border-gray-200 group-hover:border-blue-300 transition-colors duration-200"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold border-2 border-gray-200 group-hover:border-blue-300 transition-colors duration-200 shadow-sm">
                      {userData?.firstName?.charAt(0)}{userData?.lastName?.charAt(0)}
                    </div>
                  )}
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                </div>
                <FaChevronDown className={`w-3 h-3 text-gray-500 transition-transform duration-200 ${showProfileMenu ? 'rotate-180' : ''}`} />
              </button>

              {showProfileMenu && (
                <div className="absolute right-0 top-full mt-3 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 overflow-hidden">
                  <div className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        {userData?.avatarUrl ? (
                          <img
                            src={`http://localhost:33233${userData.avatarUrl}`}
                            alt="Profile"
                            className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                          />
                        ) : (
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold border-2 border-white shadow-sm">
                            {userData?.firstName?.charAt(0)}{userData?.lastName?.charAt(0)}
                          </div>
                        )}
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-gray-900 text-lg">
                          {userData?.firstName} {userData?.lastName}
                        </p>
                        <p className="text-sm text-gray-600">@{userData?.nickname}</p>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                    <button
                      onClick={() => {
                        router.push('/account')
                        setShowProfileMenu(false)
                      }}
                      className="w-full px-6 py-3 text-left text-gray-700 hover:bg-blue-50 flex items-center space-x-4 transition-colors duration-150 group"
                    >
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-150">
                        <FaUser className="w-4 h-4 text-gray-600 group-hover:text-blue-600" />
                      </div>
                      <div>
                        <span className="font-medium">Your Profile</span>
                        <p className="text-xs text-gray-500">View and edit your profile</p>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        router.push('/posts')
                        setShowProfileMenu(false)
                      }}
                      className="w-full px-6 py-3 text-left text-gray-700 hover:bg-blue-50 flex items-center space-x-4 transition-colors duration-150 group"
                    >
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-150">
                        <svg className="w-4 h-4 text-gray-600 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div>
                        <span className="font-medium">Manage Posts</span>
                        <p className="text-xs text-gray-500">View analytics and manage content</p>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        router.push('/settings')
                        setShowProfileMenu(false)
                      }}
                      className="w-full px-6 py-3 text-left text-gray-700 hover:bg-blue-50 flex items-center space-x-4 transition-colors duration-150 group"
                    >
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-150">
                        <FaCog className="w-4 h-4 text-gray-600 group-hover:text-blue-600" />
                      </div>
                      <div>
                        <span className="font-medium">Settings & Privacy</span>
                        <p className="text-xs text-gray-500">Manage your account settings</p>
                      </div>
                    </button>
                  </div>

                  <div className="border-t border-gray-200 py-2">
                    <button
                      onClick={handleLogout}
                      className="w-full px-6 py-3 text-left text-red-600 hover:bg-red-50 flex items-center space-x-4 transition-colors duration-150 group"
                    >
                      <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors duration-150">
                        <FaSignOutAlt className="w-4 h-4 text-red-600" />
                      </div>
                      <div>
                        <span className="font-medium">Log Out</span>
                        <p className="text-xs text-red-500">Sign out of your account</p>
                      </div>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}
