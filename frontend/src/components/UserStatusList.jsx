const users = [
  { id: 1, name: '<PERSON>', online: true },
  { id: 2, name: '<PERSON>', online: false },
  { id: 3, name: '<PERSON>', online: true },
]

export default function UserStatusList() {
  return (
    <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-md space-y-4">
      <h2 className="text-xl font-semibold mb-2">Online Users</h2>
      <ul className="space-y-2">
        {users.map((user) => (
          <li
            key={user.id}
            className="flex justify-between items-center bg-gray-800 px-4 py-2 rounded-md"
          >
            <span>{user.name}</span>
            <span
              className={`h-3 w-3 rounded-full ${
                user.online ? 'bg-green-400' : 'bg-red-400'
              }`}
            />
          </li>
        ))}
      </ul>
    </div>
  )
}
