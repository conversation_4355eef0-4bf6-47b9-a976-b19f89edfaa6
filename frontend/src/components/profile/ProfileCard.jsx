'use client'
import { useState } from 'react'
import { FaEdit, FaUser, FaEnvelope, FaCalendarAlt, FaGlobe, FaLock, FaUnlock } from 'react-icons/fa'

export default function ProfileCard({ user, onEdit, isEditable = true }) {
  const [isExpanded, setIsExpanded] = useState(false)

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase()
    }
    return user?.nickname?.charAt(0)?.toUpperCase() || 'U'
  }

  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  if (!user) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 shadow-lg animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-gray-700 rounded-full"></div>
          <div className="flex-1">
            <div className="h-6 bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center space-x-4">
          {/* Avatar */}
          <div className="relative">
            {user.avatarUrl ? (
              <img
                src={user.avatarUrl}
                alt={`${user.firstName} ${user.lastName}`}
                className="w-20 h-20 rounded-full object-cover border-2 border-gray-600"
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold border-2 border-gray-600">
                {getInitials()}
              </div>
            )}
            {/* Privacy indicator */}
            <div className="absolute -bottom-1 -right-1 bg-gray-800 rounded-full p-1">
              {user.private ? (
                <FaLock className="w-3 h-3 text-red-400" />
              ) : (
                <FaUnlock className="w-3 h-3 text-green-400" />
              )}
            </div>
          </div>

          {/* Basic Info */}
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-white mb-1">
              {user.firstName} {user.lastName}
            </h2>
            <p className="text-gray-400 mb-2">@{user.nickname}</p>
            <div className="flex items-center text-sm text-gray-500">
              <FaCalendarAlt className="w-3 h-3 mr-1" />
              <span>Joined {formatDate(user.createdAt)}</span>
            </div>
          </div>
        </div>

        {/* Edit Button */}
        {isEditable && (
          <button
            onClick={onEdit}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <FaEdit className="w-4 h-4" />
            <span>Edit Profile</span>
          </button>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-blue-400">
            {calculateAge(user.dateOfBirth) || '--'}
          </div>
          <div className="text-xs text-gray-400">Years Old</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-green-400">
            {user.private ? 'Private' : 'Public'}
          </div>
          <div className="text-xs text-gray-400">Profile</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-purple-400">0</div>
          <div className="text-xs text-gray-400">Posts</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-yellow-400">0</div>
          <div className="text-xs text-gray-400">Friends</div>
        </div>
      </div>

      {/* Detailed Information */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3 text-gray-300">
          <FaEnvelope className="w-4 h-4 text-gray-500" />
          <span className="text-sm">{user.email}</span>
        </div>

        <div className="flex items-center space-x-3 text-gray-300">
          <FaCalendarAlt className="w-4 h-4 text-gray-500" />
          <span className="text-sm">Born {formatDate(user.dateOfBirth)}</span>
        </div>

        {user.aboutMe && (
          <div className="mt-4">
            <h3 className="text-sm font-semibold text-gray-400 mb-2">About Me</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              {isExpanded ? user.aboutMe : `${user.aboutMe.slice(0, 150)}${user.aboutMe.length > 150 ? '...' : ''}`}
            </p>
            {user.aboutMe.length > 150 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-400 hover:text-blue-300 text-sm mt-2"
              >
                {isExpanded ? 'Show less' : 'Show more'}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
