'use client'
import { useState, useEffect } from 'react'
import { FaUsers, FaComments, FaHeart, FaEye } from 'react-icons/fa'

export default function AccountStats({ userId }) {
  const [stats, setStats] = useState({
    posts: 0,
    followers: 0,
    following: 0,
    likes: 0,
    loading: true
  })

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch user's posts count from the API
        const postsResponse = await fetch('http://localhost:33233/api/posts/my', {
          credentials: 'include'
        })

        let postsCount = 0
        if (postsResponse.ok) {
          const posts = await postsResponse.json()
          postsCount = Array.isArray(posts) ? posts.length : 0
        }

        // For now, simulate other stats (in a real app, you'd have endpoints for these)
        await new Promise(resolve => setTimeout(resolve, 500))

        setStats({
          posts: postsCount,
          followers: Math.floor(Math.random() * 50) + 5, // Simulated
          following: Math.floor(Math.random() * 30) + 2, // Simulated
          likes: Math.floor(Math.random() * 200) + 10, // Simulated
          loading: false
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
        setStats({
          posts: 0,
          followers: 0,
          following: 0,
          likes: 0,
          loading: false
        })
      }
    }

    if (userId) {
      fetchStats()
    }
  }, [userId])

  if (stats.loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
            <div className="h-8 bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-700 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  const statItems = [
    {
      label: 'Posts',
      value: stats.posts,
      icon: FaComments,
      color: 'text-blue-400',
      bgColor: 'bg-blue-600'
    },
    {
      label: 'Followers',
      value: stats.followers,
      icon: FaUsers,
      color: 'text-green-400',
      bgColor: 'bg-green-600'
    },
    {
      label: 'Following',
      value: stats.following,
      icon: FaEye,
      color: 'text-purple-400',
      bgColor: 'bg-purple-600'
    },
    {
      label: 'Likes',
      value: stats.likes,
      icon: FaHeart,
      color: 'text-red-400',
      bgColor: 'bg-red-600'
    }
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {statItems.map((item, index) => (
        <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors">
          <div className="flex items-center justify-between mb-2">
            <div className={`${item.bgColor} rounded-lg p-2`}>
              <item.icon className="w-4 h-4 text-white" />
            </div>
          </div>
          <div className={`text-2xl font-bold ${item.color} mb-1`}>
            {item.value.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">{item.label}</div>
        </div>
      ))}
    </div>
  )
}
