'use client'
import { useState } from 'react'
import Link from 'next/link'

export default function UserProfileCard({ user }) {
  const [followed, setFollowed] = useState(false)
  const [requested, setRequested] = useState(false)

  const handleFollow = () => setFollowed(true)
  const handleRequest = () => setRequested(true)

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-md space-y-4 max-w-md">
      {/* User avatar */}
      <div className="flex items-center space-x-4">
        <img
          src={user.avatar || '/img/default-avatar.png'}
          alt={`${user.name}'s avatar`}
          className="w-16 h-16 rounded-full border border-gray-600"
        />
        <div>
          <h2 className="text-xl font-semibold">{user.name}</h2>
          <p className="text-gray-400 text-sm">{user.email}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex flex-wrap gap-4">
        <button
          onClick={handleFollow}
          disabled={followed}
          className={`btn-primary ${
            followed ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {followed ? 'Following' : 'Follow'}
        </button>

        <button
          onClick={handleRequest}
          disabled={requested}
          className={`btn-success ${
            requested ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {requested ? 'Request Sent' : 'Send Friend Request'}
        </button>

        <Link
          href={`/profile/${user.id}`}
          className="text-blue-400 hover:underline text-sm mt-2"
        >
          View Full Profile →
        </Link>
      </div>
    </div>
  )
}
