'use client'
import { useState } from 'react'
import { FaHeart, FaComment, FaShare, FaEdit, FaTrash, FaEye, FaGlobe, FaLock, FaUsers } from 'react-icons/fa'

export default function PostManagementCard({ post }) {
  const [showActions, setShowActions] = useState(false)

  // Add safety checks for post object
  if (!post) {
    return null;
  }

  const getVisibilityIcon = (visibility) => {
    switch (visibility) {
      case 'public':
        return <FaGlobe className="w-4 h-4 text-green-600" />
      case 'private':
        return <FaLock className="w-4 h-4 text-red-600" />
      case 'friends':
        return <FaUsers className="w-4 h-4 text-blue-600" />
      default:
        return <FaGlobe className="w-4 h-4 text-green-600" />
    }
  }

  const getVisibilityText = (visibility) => {
    switch (visibility) {
      case 'public':
        return 'Public'
      case 'private':
        return 'Private'
      case 'friends':
        return 'Friends Only'
      default:
        return 'Public'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {(post.authorAvatar) ? (
              <img
                src={`http://localhost:33233${post.authorAvatar}`}
                alt={`${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'User'}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
                {`${post.authorFirstName?.charAt(0) || ''}${post.authorLastName?.charAt(0) || ''}` || 'U'}
              </div>
            )}
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">
                {`${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'Unknown User'}
              </h3>
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <span>
                  {post.createdAt ? new Date(post.createdAt).toLocaleDateString() : 'Unknown date'}
                </span>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  {getVisibilityIcon(post.visibility)}
                  <span>{getVisibilityText(post.visibility)}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
              </svg>
            </button>
            {showActions && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border z-10">
                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                  <FaEdit className="w-4 h-4" />
                  <span>Edit Post</span>
                </button>
                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                  <FaEye className="w-4 h-4" />
                  <span>View Post</span>
                </button>
                <button className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                  <FaTrash className="w-4 h-4" />
                  <span>Delete Post</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-gray-900 text-sm leading-relaxed mb-3">
          {post.content || 'No content'}
        </p>

        {/* Post Image */}
        {post.image && (
          <div className="mb-3">
            <img
              src={`http://localhost:33233${post.image}`}
              alt="Post content"
              className="w-full rounded-lg object-cover max-h-80"
            />
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="px-4 py-3 border-t bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <FaHeart className="w-4 h-4 text-red-500" />
              <span>{post.likes || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <FaComment className="w-4 h-4 text-blue-500" />
              <span>{post.commentCount || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <FaShare className="w-4 h-4 text-green-500" />
              <span>{post.shares || 0}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <FaEye className="w-3 h-3" />
            <span>{post.views || 0} views</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 py-3 border-t">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
              Boost Post
            </button>
            <button className="px-3 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">
              View Analytics
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors">
              <FaEdit className="w-4 h-4" />
            </button>
            <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors">
              <FaTrash className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
