export default function CommentsSection({ comments }) {
  return (
    <div className="mt-4 space-y-3">
      {comments.map((comment, idx) => (
        <div key={idx} className="bg-gray-700 p-2 rounded">
          <p className="text-sm font-medium">{comment.user}</p>
          <p className="text-sm text-gray-300">{comment.text}</p>
          <div className="flex gap-2 text-xs mt-1">
            <button className="text-blue-400 hover:underline">Like</button>
            <button className="text-red-400 hover:underline">Dislike</button>
          </div>
        </div>
      ))}

      <form className="mt-2 flex gap-2">
        <input
          type="text"
          placeholder="Add a comment..."
          className="flex-1 p-2 rounded bg-gray-800 text-white"
        />
        <button type="submit" className="bg-blue-600 px-3 py-1 rounded text-white">
          Send
        </button>
      </form>
    </div>
  )
}
