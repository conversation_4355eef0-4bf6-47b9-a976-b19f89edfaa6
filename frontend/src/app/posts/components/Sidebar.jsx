'use client'
import { useState, useEffect } from 'react'
import { <PERSON>aBars, FaList, FaHeart, FaUser, FaPlus, FaTimes } from 'react-icons/fa'

export default function Sidebar({ onAddPost }) {
  const [isOpen, setIsOpen] = useState(false)
  const categories = ['Category 1', 'Category 2', 'Category 3', 'Category 4', 'Category 5']

  // Automatically show sidebar on medium+ screens
  useEffect(() => {
    const handleResize = () => {
      setIsOpen(window.innerWidth >= 768)
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <>
      {/* Hamburger for small screens */}
      <div className="md:hidden p-4">
        <button onClick={() => setIsOpen(!isOpen)} className="text-white text-2xl">
          {isOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Sidebar content */}
      {isOpen && (
        <aside className="w-full md:w-64 bg-gray-800 text-white p-4 rounded-lg space-y-6 shadow-lg md:block">
          {/* Filters Section */}
          <div>
            <h2 className="text-xl font-bold mb-4">Filters</h2>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center gap-2 cursor-pointer hover:text-blue-400">
                <FaHeart className="text-pink-400" />
                <span>Liked Posts</span>
              </li>
              <li className="flex items-center gap-2 cursor-pointer hover:text-blue-400">
                <FaUser className="text-green-400" />
                <span>My Posts</span>
              </li>
            </ul>
          </div>

          {/* Categories Section */}
          <div>
            <h2 className="text-xl font-bold mb-4">Categories</h2>
            <ul className="space-y-2 text-sm">
              {categories.map((cat, idx) => (
                <li
                  key={idx}
                  className="flex items-center gap-2 cursor-pointer hover:text-blue-400"
                >
                  <FaList className="text-yellow-400" />
                  {cat}
                </li>
              ))}
            </ul>
          </div>

          {/* Add Post Button */}
          <div className="pt-4">
            <button
              onClick={onAddPost}
              className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded w-full"
            >
              <FaPlus />
              Add Post
            </button>
          </div>
        </aside>
      )}
    </>
  )
}
