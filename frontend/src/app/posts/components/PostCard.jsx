'use client'
import { useState } from 'react'
import { FaThumbsUp, FaThumbsDown, FaCommentDots } from 'react-icons/fa'

export default function PostCard({ post }) {
  const [showComments, setShowComments] = useState(false)

  // Add safety checks for post object
  if (!post) {
    return null;
  }

  return (
    <div className="bg-gray-800 rounded-xl p-6 shadow-lg mb-6 text-white transition hover:shadow-xl">
      {/* Header: Author Info */}
      <div className="flex items-center gap-4 mb-4">
        {(post.author?.avatar || post.authorAvatar) ? (
          <img
            src={post.author?.avatar || post.authorAvatar}
            alt={post.author?.name || `${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'User'}
            className="w-12 h-12 rounded-full object-cover border-2 border-blue-500"
          />
        ) : (
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold border-2 border-blue-500">
            {post.author?.name ?
              post.author.name.split(' ').map(n => n[0]).join('') :
              `${post.authorFirstName?.charAt(0) || ''}${post.authorLastName?.charAt(0) || ''}` || 'U'
            }
          </div>
        )}
        <div>
          <a href={`/profile/${post.author?.username || 'unknown'}`} className="font-semibold hover:underline text-lg">
            {post.author?.name || `${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'Unknown User'}
          </a>
          <p className="text-sm text-gray-400">
            {post.createdAt ? new Date(post.createdAt).toLocaleString() :
             post.created_at ? new Date(post.created_at).toLocaleString() :
             'Unknown date'}
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="mb-4">
        {post.title && (
          <h3 className="text-2xl font-bold mb-2 text-blue-300">{post.title}</h3>
        )}
        <p className="text-gray-300 mb-2">{post.content || 'No content'}</p>
        {post.image && (
          <img
            src={`http://localhost:33233${post.image}`}
            alt="Post"
            className="w-full rounded-lg mt-3 max-h-[300px] object-cover border border-gray-700"
          />
        )}
        {post.category && (
          <span className="inline-block mt-3 text-sm px-3 py-1 rounded-full bg-blue-700/20 text-blue-400">
            {post.category}
          </span>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center gap-6 mt-4 text-lg text-gray-400">
        <button className="hover:text-blue-500 flex items-center gap-1 transition">
          <FaThumbsUp /> <span className="text-sm">Like</span>
        </button>
        <button className="hover:text-red-500 flex items-center gap-1 transition">
          <FaThumbsDown /> <span className="text-sm">Dislike</span>
        </button>
        <button
          onClick={() => setShowComments(!showComments)}
          className="hover:text-green-400 flex items-center gap-1 transition"
        >
          <FaCommentDots /> <span className="text-sm">Comments</span>
        </button>
      </div>

      {/* Comments Section (Toggle) */}
      {showComments && (
        <div className="mt-6 bg-gray-700 p-4 rounded-md space-y-3">
          <p className="text-sm text-gray-300">No comments yet. Be the first to say something!</p>
          <textarea
            placeholder="Write a comment..."
            className="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-sm resize-none text-white"
            rows={3}
          />
          <button className="mt-2 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white text-sm">
            Add Comment
          </button>
        </div>
      )}
    </div>
  )
}
