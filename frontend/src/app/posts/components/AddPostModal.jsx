import { useState } from 'react'

export default function AddPostModal({ onClose, onSubmit }) {
  const categories = ['Category 1', 'Category 2', 'Category 3', 'Category 4', 'Category 5']

  const [form, setForm] = useState({
    title: '',
    content: '',
    category: categories[0],
    image: null,
  })

  const handleChange = (e) => {
    const { name, value, files } = e.target
    setForm(prev => ({
      ...prev,
      [name]: files ? files[0] : value,
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    const newPost = {
      ...form,
      author: {
        name: 'You',
        avatar: '/img/you.jpg',
        username: 'your_username',
      },
      createdAt: new Date(),
      comments: [],
    }
    onSubmit(newPost)
    onClose() // close modal after submission
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 p-6 rounded-lg w-full max-w-md">
        <h2 className="text-xl font-bold text-white mb-4">Create New Post</h2>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <input
            type="text"
            name="title"
            placeholder="Title"
            className="w-full p-2 rounded bg-gray-800 text-white"
            onChange={handleChange}
            required
          />
          <textarea
            name="content"
            placeholder="Content"
            className="w-full p-2 rounded bg-gray-800 text-white"
            onChange={handleChange}
            required
          />
          <select
            name="category"
            className="w-full p-2 rounded bg-gray-800 text-white"
            value={form.category}
            onChange={handleChange}
          >
            {categories.map((cat, index) => (
              <option key={index} value={cat}>{cat}</option>
            ))}
          </select>
          <input
            type="file"
            name="image"
            accept="image/*"
            className="w-full p-2 rounded bg-gray-800 text-white"
            onChange={handleChange}
          />

          <div className="flex justify-end gap-2">
            <button type="button" onClick={onClose} className="px-4 py-2 bg-gray-700 rounded text-white">
              Cancel
            </button>
            <button type="submit" className="px-4 py-2 bg-blue-600 rounded text-white">
              Post
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
