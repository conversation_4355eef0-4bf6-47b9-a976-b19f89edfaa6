@import "tailwindcss";

:root {
  --color-bg: #ffffff;
  --color-text: #171717;
  --font-body: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'Fira Code', monospace;
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-bg: #0a0a0a;
    --color-text: #ededed;
  }
}


/* === Base Styles === */
body {
  @apply min-h-screen antialiased tracking-tight leading-relaxed;
  background-color: var(--color-bg);
  color: var(--color-text);
  font-family: var(--font-body);
}

/* === Global Elements === */
a {
  @apply text-blue-600 dark:text-blue-400 hover:underline transition duration-150;
}

input,
textarea,
select {
  @apply rounded-md bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-2 border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* === Utility Classes (Optional) === */
.container {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
}

.input {
  @apply w-full rounded-md bg-gray-900 text-white px-4 py-2 border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn {
  @apply w-full rounded-md px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold transition duration-150;
}

@import "tailwindcss";
@tailwind utilities;


body {
  @apply bg-gray-950 text-white font-sans;
}

input,
select,
textarea {
  @apply bg-gray-800 text-white border border-gray-700 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

button {
  @apply font-medium rounded-md px-4 py-2;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white;
  --background: #0a0a0a;
  --foreground: #ededed;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}