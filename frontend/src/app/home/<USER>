'use client'
import { useState, useEffect } from 'react'
import Sidebar from '@/components/layout/Sidebar'
import { FaHeart, FaComment, FaShare, FaEllipsisH, FaImage, FaVideo, FaSmile } from 'react-icons/fa'

export default function HomePage() {
  const [posts, setPosts] = useState([
    {
      id: 1,
      author: {
        name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        avatar: ''
      },
      content: 'Just had an amazing day at the beach! 🏖️ The weather was perfect and the sunset was incredible.',
      image: null,
      timestamp: '2 hours ago',
      likes: 24,
      comments: 8,
      shares: 3
    },
    {
      id: 2,
      author: {
        name: '<PERSON>',
        username: 'jane<PERSON>',
        avatar: ''
      },
      content: 'Working on some exciting new projects! Can\'t wait to share what we\'ve been building. 💻✨',
      image: null,
      timestamp: '4 hours ago',
      likes: 15,
      comments: 5,
      shares: 2
    },
    {
      id: 3,
      author: {
        name: '<PERSON>',
        username: 'mikej',
        avatar: ''
      },
      content: 'Beautiful morning run through the park. Nothing beats starting the day with some fresh air and exercise! 🏃‍♂️',
      image: null,
      timestamp: '6 hours ago',
      likes: 31,
      comments: 12,
      shares: 5
    }
  ])

  const [showCreateModal, setShowCreateModal] = useState(false)
  const [userData, setUserData] = useState(null)
  const [loading, setLoading] = useState(true)

  // Fetch user data and posts on component mount
  useEffect(() => {
    fetchUserData()
    fetchPosts()
  }, [])

  const fetchUserData = async () => {
    try {
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setUserData(data)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchPosts = async () => {
    try {
      const response = await fetch('http://localhost:33233/api/posts', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setPosts(data || [])
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
    }
  }

  const handleCreatePost = async (postData) => {
    try {
      const formData = new FormData()
      formData.append('content', postData.content)
      formData.append('visibility', postData.visibility || 'public')
      if (postData.image) {
        formData.append('image', postData.image)
      }

      const response = await fetch('http://localhost:33233/api/posts', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      if (response.ok) {
        const newPost = await response.json()
        // Add the new post to the beginning of the posts array
        setPosts(prevPosts => [newPost, ...prevPosts])
        setShowCreateModal(false)
        alert('Post created successfully!')
      } else {
        alert('Failed to create post')
      }
    } catch (error) {
      console.error('Error creating post:', error)
      alert('Error creating post')
    }
  }

  const CreatePostModal = ({ onClose, onSubmit }) => {
    const [postContent, setPostContent] = useState('')
    const [postImage, setPostImage] = useState(null)
    const [visibility, setVisibility] = useState('public')

    const handleSubmit = (e) => {
      e.preventDefault()
      if (!postContent.trim()) return

      onSubmit({
        content: postContent,
        image: postImage,
        visibility: visibility
      })
    }

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-lg mx-4">
          {/* Modal Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Create Post</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>

          {/* Modal Body */}
          <form onSubmit={handleSubmit} className="p-4">
            {/* User Info */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
                {userData ? `${userData.firstName?.charAt(0)}${userData.lastName?.charAt(0)}` : 'U'}
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {userData ? `${userData.firstName} ${userData.lastName}` : 'User'}
                </p>
                <select
                  value={visibility}
                  onChange={(e) => setVisibility(e.target.value)}
                  className="text-sm text-gray-500 dark:text-gray-400 bg-transparent border-none focus:outline-none"
                >
                  <option value="public">🌍 Public</option>
                  <option value="friends">👥 Friends</option>
                  <option value="private">🔒 Only me</option>
                </select>
              </div>
            </div>

            {/* Post Content */}
            <textarea
              value={postContent}
              onChange={(e) => setPostContent(e.target.value)}
              placeholder={`What's on your mind, ${userData?.firstName || 'User'}?`}
              className="w-full h-32 p-3 text-gray-900 dark:text-white bg-transparent resize-none focus:outline-none placeholder-gray-500 dark:placeholder-gray-400"
              autoFocus
            />

            {/* Image Preview */}
            {postImage && (
              <div className="mt-4 relative">
                <img
                  src={URL.createObjectURL(postImage)}
                  alt="Preview"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <button
                  type="button"
                  onClick={() => setPostImage(null)}
                  className="absolute top-2 right-2 bg-gray-800 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-700"
                >
                  ✕
                </button>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between mt-4 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
              <span className="text-sm font-medium text-gray-900 dark:text-white">Add to your post</span>
              <div className="flex space-x-2">
                <label className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full">
                  <FaImage className="w-5 h-5 text-green-500" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setPostImage(e.target.files[0])}
                    className="hidden"
                  />
                </label>
                <button type="button" className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full">
                  <FaVideo className="w-5 h-5 text-red-500" />
                </button>
                <button type="button" className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full">
                  <FaSmile className="w-5 h-5 text-yellow-500" />
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={!postContent.trim()}
              className="w-full mt-4 py-2 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              Post
            </button>
          </form>
        </div>
      </div>
    )
  }

  const PostCard = ({ post }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
      {/* Post Header */}
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {(post.author?.avatar || post.authorAvatar) ? (
            <img
              src={post.author?.avatar || post.authorAvatar}
              alt={post.author?.name || `${post.authorFirstName} ${post.authorLastName}`}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
              {post.author?.name ?
                post.author.name.split(' ').map(n => n[0]).join('') :
                `${post.authorFirstName?.charAt(0) || ''}${post.authorLastName?.charAt(0) || ''}` || 'U'
              }
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-[15px]">
              {post.author?.name || `${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'Unknown User'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-[13px]">
              {post.timestamp || new Date(post.created_at || post.createdAt).toLocaleString()}
            </p>
          </div>
        </div>
        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <FaEllipsisH className="w-4 h-4" />
        </button>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-gray-900 dark:text-white text-[15px] leading-relaxed">
          {post.content}
        </p>
      </div>

      {/* Post Image (if any) */}
      {post.image && (
        <div className="px-4 pb-3">
          <img
            src={`http://localhost:33233${post.image}`}
            alt="Post content"
            className="w-full rounded-lg object-cover max-h-96"
          />
        </div>
      )}

      {/* Post Stats */}
      <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-gray-500 dark:text-gray-400 text-[13px]">
          <span>{post.likes} likes</span>
          <div className="flex space-x-4">
            <span>{post.comments} comments</span>
            <span>{post.shares} shares</span>
          </div>
        </div>
      </div>

      {/* Post Actions */}
      <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-around">
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaHeart className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Like</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaComment className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Comment</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaShare className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="flex min-h-screen">
        <Sidebar />
        <main className="flex-1 max-w-2xl mx-auto py-6 px-4">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen">
      <Sidebar />

      {/* Main Content - Facebook-style Feed */}
      <main className="flex-1 max-w-2xl mx-auto py-6 px-4">
        {/* Create Post Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
                {userData ? `${userData.firstName?.charAt(0)}${userData.lastName?.charAt(0)}` : 'U'}
              </div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-full px-4 py-2 text-left text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {userData ? `What's on your mind, ${userData.firstName}?` : "What's on your mind?"}
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-around mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaVideo className="w-5 h-5 text-red-500" />
                <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">Live video</span>
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaImage className="w-5 h-5 text-green-500" />
                <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">Photo/video</span>
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaSmile className="w-5 h-5 text-yellow-500" />
                <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">Feeling/activity</span>
              </button>
            </div>
          </div>
        </div>

        {/* Posts Feed */}
        <div>
          {posts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center py-8">
          <button className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
            Load more posts
          </button>
        </div>
      </main>

      {/* Right Sidebar (empty for now, like Facebook) */}
      <div className="hidden lg:block w-80">
        {/* This could contain ads, suggestions, etc. like Facebook */}
      </div>

      {/* Create Post Modal */}
      {showCreateModal && (
        <CreatePostModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreatePost}
        />
      )}
    </div>
  )
}
