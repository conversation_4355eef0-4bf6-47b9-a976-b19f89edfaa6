'use client'
import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { FaCog, FaEllipsisH, FaEdit, FaPlus, FaCamera, FaMapMarkerAlt, FaBriefcase, FaGraduationCap, FaCalendarAlt, FaCheckCircle, FaHeart, FaComment, FaShare } from 'react-icons/fa'

// PostCard component for displaying posts
const PostCard = ({ post }) => {
  // Add safety checks for post object
  if (!post) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
      {/* Post Header */}
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {(post.author?.avatar || post.authorAvatar) ? (
            <img
              src={post.author?.avatar || post.authorAvatar}
              alt={post.author?.name || `${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'User'}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
              {post.author?.name ?
                post.author.name.split(' ').map(n => n[0]).join('') :
                `${post.authorFirstName?.charAt(0) || ''}${post.authorLastName?.charAt(0) || ''}` || 'U'
              }
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-900 text-[15px]">
              {post.author?.name || `${post.authorFirstName || ''} ${post.authorLastName || ''}`.trim() || 'Unknown User'}
            </h3>
            <p className="text-gray-500 text-[13px]">
              {post.timestamp || (post.created_at || post.createdAt ? new Date(post.created_at || post.createdAt).toLocaleString() : 'Unknown date')}
            </p>
          </div>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <FaEllipsisH className="w-4 h-4" />
        </button>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-gray-900 text-[15px] leading-relaxed">
          {post.content || 'No content'}
        </p>
      </div>

      {/* Post Image (if any) */}
      {post.image && (
        <div className="px-4 pb-3">
          <img
            src={`http://localhost:33233${post.image}`}
            alt="Post content"
            className="w-full rounded-lg object-cover max-h-96"
          />
        </div>
      )}

      {/* Post Stats */}
      <div className="px-4 py-2 border-t border-gray-200">
        <div className="flex items-center justify-between text-gray-500 text-[13px]">
          <span>{post.likes || 0} likes</span>
          <div className="flex space-x-4">
            <span>{post.commentCount || 0} comments</span>
            <span>{post.shares || 0} shares</span>
          </div>
        </div>
      </div>

      {/* Post Actions */}
      <div className="px-4 py-2 border-t border-gray-200">
        <div className="flex items-center justify-around">
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
            <FaHeart className="w-4 h-4 text-gray-600" />
            <span className="text-gray-600 text-[15px] font-medium">Like</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
            <FaComment className="w-4 h-4 text-gray-600" />
            <span className="text-gray-600 text-[15px] font-medium">Comment</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
            <FaShare className="w-4 h-4 text-gray-600" />
            <span className="text-gray-600 text-[15px] font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default function AccountProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('Posts')
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [userData, setUserData] = useState(null)
  const [formData, setFormData] = useState({
    nickname: '',
    firstName: '',
    lastName: '',
    email: '',
    aboutMe: '',
    private: false
  })
  const [photos, setPhotos] = useState([])
  const [friends, setFriends] = useState([])
  const [userPosts, setUserPosts] = useState([])
  const [uploading, setUploading] = useState(false)

  const coverInputRef = useRef(null)
  const avatarInputRef = useRef(null)

  const tabs = ['Posts', 'About', 'Friends', 'Photos']

  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  useEffect(() => {
    fetchUserData()
    if (activeTab === 'Photos') {
      fetchPhotos()
    } else if (activeTab === 'Friends') {
      fetchFriends()
    } else if (activeTab === 'Posts') {
      fetchUserPosts()
    }
  }, [activeTab])

  const fetchUserData = async () => {
    try {
      console.log('Fetching user data...')
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })
      
      console.log('Response status:', response.status)
      
      if (response.ok) {
        const data = await response.json()
        console.log('User data received:', data)
        setUserData(data)
        setFormData({
          nickname: data.nickname || '',
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          email: data.email || '',
          aboutMe: data.aboutMe || '',
          private: data.private || false
        })
      } else if (response.status === 401) {
        console.log('Unauthorized - redirecting to login')
        router.push('/login')
        return
      } else {
        console.log('Unexpected response status:', response.status)
        const errorText = await response.text()
        console.log('Error response:', errorText)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      router.push('/login')
      return
    } finally {
      setLoading(false)
    }
  }

  const fetchPhotos = async () => {
    if (!userData?.id) return

    try {
      const response = await fetch(`http://localhost:33233/api/user/${userData.id}/photos`, {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setPhotos(data.photos || [])
      }
    } catch (error) {
      console.error('Error fetching photos:', error)
    }
  }

  const fetchFriends = async () => {
    if (!userData?.id) return

    try {
      const response = await fetch(`http://localhost:33233/api/user/${userData.id}/friends`, {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setFriends(data.friends || [])
      }
    } catch (error) {
      console.error('Error fetching friends:', error)
    }
  }

  const fetchUserPosts = async () => {
    try {
      const response = await fetch('http://localhost:33233/api/posts/my', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setUserPosts(data || [])
      }
    } catch (error) {
      console.error('Error fetching user posts:', error)
    }
  }

  const handleCoverUpload = () => {
    coverInputRef.current?.click()
  }

  const handleCoverChange = async (e) => {
    const file = e.target.files[0]
    if (!file) return

    setUploading(true)
    const formData = new FormData()
    formData.append('cover', file)

    try {
      const response = await fetch('http://localhost:33233/api/user/upload/cover', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setUserData(prev => ({ ...prev, coverUrl: data.cover_url }))
        alert('Cover photo updated successfully!')
      } else {
        alert('Failed to upload cover photo')
      }
    } catch (error) {
      console.error('Error uploading cover:', error)
      alert('Error uploading cover photo')
    } finally {
      setUploading(false)
    }
  }

  const handleAvatarUpload = () => {
    avatarInputRef.current?.click()
  }

  const handleAvatarChange = async (e) => {
    const file = e.target.files[0]
    if (!file) return

    setUploading(true)
    const formData = new FormData()
    formData.append('avatar', file)

    try {
      const response = await fetch('http://localhost:33233/api/user/upload/avatar', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setUserData(prev => ({ ...prev, avatarUrl: data.avatar_url }))
        alert('Profile picture updated successfully!')
      } else {
        alert('Failed to upload profile picture')
      }
    } catch (error) {
      console.error('Error uploading avatar:', error)
      alert('Error uploading profile picture')
    } finally {
      setUploading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSave = async () => {
    setUpdating(true)
    try {
      const formDataToSend = new FormData()
      formDataToSend.append('nickname', formData.nickname)
      formDataToSend.append('firstName', formData.firstName)
      formDataToSend.append('lastName', formData.lastName)
      formDataToSend.append('email', formData.email)
      formDataToSend.append('aboutMe', formData.aboutMe)
      formDataToSend.append('private', formData.private.toString())

      const response = await fetch('http://localhost:33233/api/user/current', {
        method: 'PUT',
        credentials: 'include',
        body: formDataToSend
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const updatedUser = await response.json()
      setUserData(updatedUser)
      setIsEditing(false)
      alert('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile')
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Failed to load profile data</p>
          <button
            onClick={() => router.push('/login')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  const AboutSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">Intro</h3>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="text-blue-600 hover:text-blue-700 text-sm"
          >
            Edit
          </button>
        )}
      </div>
      
      {isEditing ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">About Me</label>
            <textarea
              name="aboutMe"
              value={formData.aboutMe}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Tell people about yourself..."
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nickname</label>
            <input
              type="text"
              name="nickname"
              value={formData.nickname}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              name="private"
              checked={formData.private}
              onChange={handleInputChange}
              className="mr-2"
            />
            <label className="text-sm text-gray-700">Private account</label>
          </div>
          
          <div className="flex space-x-3 pt-2">
            <button
              onClick={handleSave}
              disabled={updating}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg text-sm"
            >
              {updating ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={() => {
                setIsEditing(false)
                setFormData({
                  nickname: userData.nickname || '',
                  firstName: userData.firstName || '',
                  lastName: userData.lastName || '',
                  email: userData.email || '',
                  aboutMe: userData.aboutMe || '',
                  private: userData.private || false
                })
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {userData.aboutMe && (
            <p className="text-gray-700">{userData.aboutMe}</p>
          )}
          
          <div className="flex items-center text-gray-600">
            <FaBriefcase className="w-4 h-4 mr-3" />
            <span>@{userData.nickname}</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            <FaCalendarAlt className="w-4 h-4 mr-3" />
            <span>Joined {formatDate(userData.createdAt)}</span>
          </div>
          
          {userData.private && (
            <div className="flex items-center text-gray-600">
              <span className="text-sm bg-gray-100 px-2 py-1 rounded">Private Account</span>
            </div>
          )}
        </div>
      )}
    </div>
  )

  const PhotosSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">Photos</h3>
        <button
          onClick={() => setActiveTab('Photos')}
          className="text-blue-600 hover:underline text-sm"
        >
          See all
        </button>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {photos.slice(0, 6).map((photo, i) => (
          <div key={photo.id || i} className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
            <img
              src={photo.image_url}
              alt={photo.caption || 'Photo'}
              className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
            />
          </div>
        ))}
        {photos.length === 0 && (
          <div className="col-span-3 text-center text-gray-500 py-4">
            No photos yet
          </div>
        )}
      </div>
    </div>
  )

  const FriendsSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">Friends</h3>
        <button
          onClick={() => setActiveTab('Friends')}
          className="text-blue-600 hover:underline text-sm"
        >
          See all
        </button>
      </div>
      <div className="space-y-3">
        {friends.slice(0, 5).map((friend) => (
          <div key={friend.id} className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold">
              {friend.avatar_url ? (
                <img
                  src={friend.avatar_url}
                  alt={friend.nickname}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                `${friend.first_name?.charAt(0)}${friend.last_name?.charAt(0)}`
              )}
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-900">{friend.first_name} {friend.last_name}</p>
              <p className="text-sm text-gray-500">@{friend.nickname}</p>
            </div>
          </div>
        ))}
        {friends.length === 0 && (
          <div className="text-center text-gray-500 py-4">
            No friends yet
          </div>
        )}
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Failed to load profile data</p>
          <button
            onClick={() => router.push('/login')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
          </div>
        </div>
      </div>

      {/* Cover Photo */}
      <div className="max-w-4xl mx-auto">
        <div className="relative">
          <div className="h-80 bg-gray-300 rounded-t-lg relative overflow-hidden">
            {userData.coverUrl ? (
              <img
                src={`http://localhost:33233${userData.coverUrl}`}
                alt="Cover"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500"></div>
            )}
            <button
              onClick={handleCoverUpload}
              className="absolute bottom-4 right-4 bg-white bg-opacity-90 hover:bg-opacity-100 px-3 py-2 rounded-lg flex items-center space-x-2 transition-all"
            >
              <FaCamera className="w-4 h-4 text-gray-700" />
              <span className="text-gray-700 text-sm font-medium">
                {userData.coverUrl ? 'Change cover' : 'Add cover'}
              </span>
            </button>
            <input
              ref={coverInputRef}
              type="file"
              accept="image/*"
              onChange={handleCoverChange}
              className="hidden"
            />
          </div>
          
          {/* Profile Picture */}
          <div className="absolute -bottom-6 left-6">
            <div className="relative">
              {userData.avatarUrl ? (
                <img
                  src={`http://localhost:33233${userData.avatarUrl}`}
                  alt="Profile"
                  className="w-32 h-32 rounded-full object-cover border-4 border-white"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl font-bold border-4 border-white">
                  {userData.firstName?.charAt(0)}{userData.lastName?.charAt(0)}
                </div>
              )}
              <button
                onClick={handleAvatarUpload}
                disabled={uploading}
                className="absolute bottom-2 right-2 bg-gray-200 hover:bg-gray-300 rounded-full p-2 transition-colors disabled:opacity-50"
              >
                <FaCamera className="w-4 h-4 text-gray-600" />
              </button>
              <input
                ref={avatarInputRef}
                type="file"
                accept="image/*"
                onChange={handleAvatarChange}
                className="hidden"
              />
            </div>
          </div>
        </div>
        
        {/* Profile Info */}
        <div className="bg-white rounded-b-lg border-t-0 pt-10 pb-4 px-6">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  {userData.firstName} {userData.lastName}
                </h1>
                <FaCheckCircle className="w-6 h-6 text-blue-500" />
              </div>
              <p className="text-gray-600 mt-1">{userData.aboutMe || 'No bio available'}</p>
              <p className="text-gray-500 text-sm mt-2">
                @{userData.nickname}
              </p>
            </div>
            <div className="flex space-x-2">
              <button 
                onClick={() => setIsEditing(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <FaEdit className="w-4 h-4" />
                <span>Edit profile</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-white border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Left Column */}
          <div className="lg:col-span-1 space-y-4">
            <AboutSection />
            <PhotosSection />
            <FriendsSection />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-2">
            {activeTab === 'Posts' && (
              <div className="space-y-4">
                {/* Header with Manage Posts Button */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">My Posts</h3>
                    <button
                      onClick={() => router.push('/posts')}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span>Manage Posts</span>
                    </button>
                  </div>
                  <p className="text-gray-500 text-sm mt-1">
                    {userPosts.length === 0
                      ? "You haven't created any posts yet."
                      : `Showing ${Math.min(userPosts.length, 3)} of ${userPosts.length} posts`
                    }
                  </p>
                </div>

                {/* Posts Display */}
                {userPosts.length === 0 ? (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FaEdit className="w-8 h-8 text-gray-400" />
                      </div>
                      <p className="text-gray-500 mb-4">No posts yet.</p>
                      <button
                        onClick={() => router.push('/home')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Create Your First Post
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Show only first 3 posts on profile */}
                    {userPosts.slice(0, 3).map(post => (
                      <PostCard key={post.id} post={post} />
                    ))}

                    {/* Show "View All" button if there are more than 3 posts */}
                    {userPosts.length > 3 && (
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                        <div className="text-center">
                          <button
                            onClick={() => router.push('/posts')}
                            className="text-blue-600 hover:text-blue-700 font-medium"
                          >
                            View All {userPosts.length} Posts →
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
            
            {activeTab === 'About' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">About</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Basic Information</h4>
                    <div className="space-y-2">
                      <p className="text-gray-700"><span className="font-medium">Name:</span> {userData.firstName} {userData.lastName}</p>
                      <p className="text-gray-700"><span className="font-medium">Username:</span> @{userData.nickname}</p>
                      <p className="text-gray-700"><span className="font-medium">Email:</span> {userData.email}</p>
                      {userData.dateOfBirth && (
                        <p className="text-gray-700"><span className="font-medium">Date of Birth:</span> {formatDate(userData.dateOfBirth)}</p>
                      )}
                    </div>
                  </div>
                  {userData.aboutMe && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">About Me</h4>
                      <p className="text-gray-700">{userData.aboutMe}</p>
                    </div>
                  )}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Account Details</h4>
                    <div className="space-y-2">
                      <p className="text-gray-700"><span className="font-medium">Joined:</span> {formatDate(userData.createdAt)}</p>
                      <p className="text-gray-700"><span className="font-medium">Account Type:</span> {userData.private ? 'Private' : 'Public'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'Friends' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Friends ({friends.length})</h3>
                {friends.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {friends.map((friend) => (
                      <div key={friend.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                          {friend.avatar_url ? (
                            <img
                              src={friend.avatar_url}
                              alt={friend.nickname}
                              className="w-12 h-12 rounded-full object-cover"
                            />
                          ) : (
                            `${friend.first_name?.charAt(0)}${friend.last_name?.charAt(0)}`
                          )}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{friend.first_name} {friend.last_name}</h4>
                          <p className="text-sm text-gray-500">@{friend.nickname}</p>
                          <p className="text-xs text-gray-400">Friends since {formatDate(friend.since)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No friends yet</p>
                )}
              </div>
            )}

            {activeTab === 'Photos' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Photos ({photos.length})</h3>
                {photos.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {photos.map((photo, i) => (
                      <div key={photo.id || i} className="aspect-square bg-gray-200 rounded-lg overflow-hidden group cursor-pointer">
                        <img
                          src={photo.image_url}
                          alt={photo.caption || 'Photo'}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity flex items-end p-2">
                          {photo.caption && (
                            <p className="text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                              {photo.caption.slice(0, 50)}...
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No photos yet</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
