'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '../../context/AuthContext'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const router = useRouter()
  const { login, user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleLogin = async (e) => {
    e.preventDefault()
    setLoading(true)
    const success = await login(email, password)
    setLoading(false)

    if (success) {
      router.push('/home')
    } else {
      setError('Invalid credentials, please try again.')
    }
  }

  useEffect(() => {
    if (user) {
      router.push('/home')
    }
  }, [user])

  return (
    <main className="min-h-screen flex bg-gray-950 text-white font-sans">
      {/* Login Form Section - Left */}
      <div className="w-full md:w-1/2 p-6 flex items-center justify-center">
        <div className="max-h-[95vh] overflow-y-auto scrollbar-hide">
          <form className="space-y-6" onSubmit={handleLogin}>
            <h2
              className="text-3xl font-bold text-blue-400 text-center"
              style={{ fontFamily: "'Orbitron', sans-serif" }}
            >
              Log in to IMSON
            </h2>

            {error && <p className="text-red-500 text-sm">{error}</p>}

            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Password <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>

            {/* Submit */}
            <button
              disabled={loading}
              type="submit"
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-purple-600 hover:to-blue-500 text-white font-semibold py-3 px-4 rounded-md shadow-lg transition-all duration-300"
            >
              {loading ? 'Logging in...' : 'Log In'}
            </button>

            {/* Link to signup */}
            <p className="text-sm text-center text-gray-400">
              Don’t have an account?{' '}
              <a href="/signup" className="text-blue-500 hover:underline">
                Sign up
              </a>
            </p>
          </form>
        </div>
      </div>

      {/* Right Sidebar Branding */}
      <div className="hidden md:flex w-1/2 bg-gradient-to-br from-blue-900 via-gray-900 to-black items-center justify-center p-10">
        <h1
          className="text-6xl font-extrabold text-blue-500 tracking-widest text-center"
          style={{ fontFamily: "'Orbitron', sans-serif" }}
        >
          IMSON
        </h1>
      </div>
    </main>
  )
}
