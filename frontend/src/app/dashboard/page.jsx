'use client'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to home page
    router.push('/dashboard/home')
  }, [router])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    </div>
  )

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      <Sidebar onSelectUser={setSelectedUser} />

      <main className="flex-1 p-4">
        <h1 className="text-2xl font-bold mb-4">Dashboard</h1>

        {selectedUser && (
          <div className="bg-gray-800 p-4 rounded-lg shadow-md text-white max-w-sm space-y-4">
            <div className="flex items-center gap-4">
              <img
                src={selectedUser.avatar}
                alt={selectedUser.name}
                className="w-16 h-16 rounded-full object-cover"
              />
              <div>
                <h2 className="text-xl font-bold">{selectedUser.name}</h2>
                <p className="text-sm text-gray-400">@{selectedUser.username}</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">
                Send Friend Request
              </button>
              <a
                href={`/profile/${selectedUser.username}`}
                className="text-blue-400 hover:underline text-sm"
              >
                View Profile
              </a>
            </div>
            <button
              onClick={() => setSelectedUser(null)}
              className="text-xs text-red-400 underline"
            >
              Close
            </button>
          </div>
        )}

        {/* Messaging Section */}
        {selectedUser && (
          <div className="mt-6 space-y-4">
            <h3 className="text-xl font-semibold">Messages</h3>
            <div className="bg-gray-700 p-4 rounded-lg space-y-3">
              {messages.map((msg, index) => (
                <div key={index} className="text-white">
                  <strong>{msg.from}:</strong> {msg.content}
                </div>
              ))}
            </div>
            <textarea
              className="w-full p-2 mt-4 text-black"
              placeholder="Type a message..."
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.target.value.trim()) {
                  sendPrivateMessage(e.target.value)
                  e.target.value = ''
                }
              }}
            />
          </div>
        )}
      </main>
    </div>
  )
}
