'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { FaEdit, FaSave, FaTimes, FaUser, FaEnvelope, FaPhone, FaCalendarAlt, FaClock, FaCamera, FaKey, FaShield, FaArrowLeft } from 'react-icons/fa'

export default function ProfilePage() {
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [userData, setUserData] = useState(null)
  const [updating, setUpdating] = useState(false)
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    nickname: '',
    email: '',
    dateOfBirth: '',
    aboutMe: '',
    private: false
  })

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      console.log('Fetching user data...')
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })

      console.log('Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('User data received:', data)
        setUserData(data)
        setFormData({
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          nickname: data.nickname || '',
          email: data.email || '',
          dateOfBirth: data.dateOfBirth || '',
          aboutMe: data.aboutMe || '',
          private: data.private || false
        })
      } else if (response.status === 401) {
        console.log('Unauthorized - redirecting to login')
        // Not authenticated, redirect to login
        router.push('/login')
        return
      } else {
        console.log('Unexpected response status:', response.status)
        const errorText = await response.text()
        console.log('Error response:', errorText)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      // On error, redirect to login
      router.push('/login')
      return
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSaveProfile = async () => {
    setUpdating(true)
    try {
      const formDataToSend = new FormData()
      Object.keys(formData).forEach(key => {
        formDataToSend.append(key, formData[key])
      })

      const response = await fetch('http://localhost:33233/api/user/current', {
        method: 'PUT',
        credentials: 'include',
        body: formDataToSend
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const updatedUser = await response.json()
      setUserData(updatedUser)
      setIsEditing(false)

      // Refresh the user data
      await fetchUserData()

      alert('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile. Please try again.')
    } finally {
      setUpdating(false)
    }
  }

  const handleBackToDashboard = () => {
    router.push('/dashboard')
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateMemberDays = (createdAt) => {
    if (!createdAt) return 0
    const created = new Date(createdAt)
    const now = new Date()
    const diffTime = Math.abs(now - created)
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  if (loading || !userData) {
    return (
      <div className="min-h-screen bg-gray-950">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-700 rounded w-1/4 mb-6"></div>
            <div className="bg-gray-800 rounded-lg p-6 h-96"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950">
        <Navbar />

        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToDashboard}
                className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
              >
                <FaArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-white">Account Profile</h1>
                <p className="text-gray-400">Manage your personal information and account settings</p>
              </div>
            </div>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <FaEdit className="w-4 h-4" />
              <span>{isEditing ? 'Cancel' : 'Edit Profile'}</span>
            </button>
          </div>

          {/* Account Information Card */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
            {/* Profile Header */}
            <div className="flex items-center space-x-6 mb-6">
              <div className="relative">
                {userData.avatarUrl ? (
                  <img
                    src={userData.avatarUrl}
                    alt="Profile"
                    className="w-24 h-24 rounded-full object-cover border-4 border-gray-600"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold border-4 border-gray-600">
                    {userData.firstName?.charAt(0)}{userData.lastName?.charAt(0)}
                  </div>
                )}
                <button className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full p-2 hover:bg-blue-700 transition-colors">
                  <FaCamera className="w-3 h-3 text-white" />
                </button>
              </div>

              <div className="flex-1">
                <h2 className="text-2xl font-bold text-white mb-2">
                  {userData.firstName} {userData.lastName}
                </h2>
                <p className="text-gray-400 mb-2">@{userData.nickname}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>Member for {calculateMemberDays(userData.createdAt)} days</span>
                  <span>•</span>
                  <span>{userData.private ? 'Private Account' : 'Public Account'}</span>
                </div>
              </div>
            </div>

            {/* Account Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-4">Personal Information</h3>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <FaUser className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">First Name</label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-white">{userData.firstName || 'Not provided'}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <FaUser className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">Last Name</label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-white">{userData.lastName || 'Not provided'}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <FaUser className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">Username</label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="nickname"
                          value={formData.nickname}
                          onChange={handleInputChange}
                          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-white">{userData.nickname || 'Not provided'}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <FaEnvelope className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">Email</label>
                      {isEditing ? (
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-white">{userData.email || 'Not provided'}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-4">Account Information</h3>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <FaCalendarAlt className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">Date of Birth</label>
                      {isEditing ? (
                        <input
                          type="date"
                          name="dateOfBirth"
                          value={formData.dateOfBirth}
                          onChange={handleInputChange}
                          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <p className="text-white">{formatDate(userData.dateOfBirth)}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <FaClock className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">Account Created</label>
                      <p className="text-white">{formatDate(userData.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <FaClock className="w-4 h-4 text-gray-400" />
                    <div className="flex-1">
                      <label className="block text-sm text-gray-400">User ID</label>
                      <p className="text-white font-mono text-sm">{userData.id}</p>
                    </div>
                  </div>

                  {isEditing && (
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            name="private"
                            checked={formData.private}
                            onChange={handleInputChange}
                            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <span className="text-white">Private Account</span>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* About Me Section */}
            <div className="mt-6 pt-6 border-t border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">About Me</h3>
              {isEditing ? (
                <textarea
                  name="aboutMe"
                  value={formData.aboutMe}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Tell us about yourself..."
                />
              ) : (
                <p className="text-gray-300">{userData.aboutMe || 'No description provided.'}</p>
              )}
            </div>

            {/* Save/Cancel Buttons */}
            {isEditing && (
              <div className="mt-6 pt-6 border-t border-gray-700 flex justify-end space-x-4">
                <button
                  onClick={() => setIsEditing(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <FaTimes className="w-4 h-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSaveProfile}
                  disabled={updating}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <FaSave className="w-4 h-4" />
                  <span>{updating ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </div>
            )}
          </div>

          {/* Security Section */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <FaKey className="w-5 h-5 text-yellow-400" />
              <span>Security & Password</span>
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <h4 className="text-white font-medium">Password</h4>
                  <p className="text-gray-400 text-sm">Last changed 1 week ago</p>
                </div>
                <button className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                  Reset Password
                </button>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <h4 className="text-white font-medium">Two-Factor Authentication</h4>
                  <p className="text-gray-400 text-sm">Add an extra layer of security</p>
                </div>
                <span className="bg-red-600/20 text-red-400 px-3 py-1 rounded-lg text-sm">
                  Disabled
                </span>
              </div>
            </div>
          </div>

        </div>
      </div>
  )
}
