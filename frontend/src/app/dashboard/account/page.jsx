'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { FaCog, FaEllipsisH, FaEdit, FaPlus, FaCamera, FaMapMarkerAlt, FaBriefcase, FaGraduationCap, FaCalendarAlt, FaCheckCircle } from 'react-icons/fa'

export default function AccountProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('About')
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [userData, setUserData] = useState(null)
  const [formData, setFormData] = useState({
    nickname: '',
    firstName: '',
    lastName: '',
    email: '',
    aboutMe: '',
    private: false
  })

  const tabs = ['Posts', 'About', 'Friends', 'Photos']

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      console.log('Fetching user data...')
      const response = await fetch('http://localhost:33233/api/user/current', {
        credentials: 'include'
      })

      console.log('Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('User data received:', data)
        setUserData(data)
        setFormData({
          nickname: data.nickname || '',
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          email: data.email || '',
          aboutMe: data.aboutMe || '',
          private: data.private || false
        })
      } else if (response.status === 401) {
        console.log('Unauthorized - redirecting to login')
        router.push('/login')
        return
      } else {
        console.log('Unexpected response status:', response.status)
        const errorText = await response.text()
        console.log('Error response:', errorText)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      router.push('/login')
      return
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSave = async () => {
    setUpdating(true)
    try {
      const formDataToSend = new FormData()
      formDataToSend.append('nickname', formData.nickname)
      formDataToSend.append('firstName', formData.firstName)
      formDataToSend.append('lastName', formData.lastName)
      formDataToSend.append('email', formData.email)
      formDataToSend.append('aboutMe', formData.aboutMe)
      formDataToSend.append('private', formData.private.toString())

      const response = await fetch('http://localhost:33233/api/user/current', {
        method: 'PUT',
        credentials: 'include',
        body: formDataToSend
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const updatedUser = await response.json()
      setUserData(updatedUser)
      setIsEditing(false)
      alert('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile')
    } finally {
      setUpdating(false)
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Failed to load profile data</p>
          <button
            onClick={() => router.push('/login')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  const AboutSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">Intro</h3>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="text-blue-600 hover:text-blue-700 text-sm"
          >
            Edit
          </button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">About Me</label>
            <textarea
              name="aboutMe"
              value={formData.aboutMe}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Tell people about yourself..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nickname</label>
            <input
              type="text"
              name="nickname"
              value={formData.nickname}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="private"
              checked={formData.private}
              onChange={handleInputChange}
              className="mr-2"
            />
            <label className="text-sm text-gray-700">Private account</label>
          </div>

          <div className="flex space-x-3 pt-2">
            <button
              onClick={handleSave}
              disabled={updating}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg text-sm"
            >
              {updating ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={() => {
                setIsEditing(false)
                setFormData({
                  nickname: userData.nickname || '',
                  firstName: userData.firstName || '',
                  lastName: userData.lastName || '',
                  email: userData.email || '',
                  aboutMe: userData.aboutMe || '',
                  private: userData.private || false
                })
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {userData.aboutMe && (
            <p className="text-gray-700">{userData.aboutMe}</p>
          )}

          <div className="flex items-center text-gray-600">
            <FaBriefcase className="w-4 h-4 mr-3" />
            <span>@{userData.nickname}</span>
          </div>

          <div className="flex items-center text-gray-600">
            <FaCalendarAlt className="w-4 h-4 mr-3" />
            <span>Joined {formatDate(userData.createdAt)}</span>
          </div>

          {userData.private && (
            <div className="flex items-center text-gray-600">
              <span className="text-sm bg-gray-100 px-2 py-1 rounded">Private Account</span>
            </div>
          )}
        </div>
      )}
    </div>
  )

  const PhotosSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">Photos</h3>
        <button className="text-blue-600 hover:underline text-sm">See all</button>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="aspect-square bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
            <div className="flex items-center space-x-2">
              <button className="p-2 rounded-full hover:bg-gray-100">
                <FaCog className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100">
                <FaEllipsisH className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Cover Photo */}
      <div className="max-w-4xl mx-auto">
        <div className="relative">
          <div className="h-80 bg-gray-300 rounded-t-lg relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500"></div>
            <button className="absolute bottom-4 right-4 bg-white bg-opacity-90 hover:bg-opacity-100 px-3 py-2 rounded-lg flex items-center space-x-2 transition-all">
              <FaCamera className="w-4 h-4 text-gray-700" />
              <span className="text-gray-700 text-sm font-medium">Edit cover</span>
            </button>
          </div>

          {/* Profile Picture */}
          <div className="absolute -bottom-6 left-6">
            <div className="relative">
              {userData.avatarUrl ? (
                <img
                  src={userData.avatarUrl}
                  alt="Profile"
                  className="w-32 h-32 rounded-full object-cover border-4 border-white"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl font-bold border-4 border-white">
                  {userData.firstName?.charAt(0)}{userData.lastName?.charAt(0)}
                </div>
              )}
              <button className="absolute bottom-2 right-2 bg-gray-200 hover:bg-gray-300 rounded-full p-2 transition-colors">
                <FaCamera className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Profile Info */}
        <div className="bg-white rounded-b-lg border-t-0 pt-10 pb-4 px-6">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  {userData.firstName} {userData.lastName}
                </h1>
                <FaCheckCircle className="w-6 h-6 text-blue-500" />
              </div>
              <p className="text-gray-600 mt-1">{userData.aboutMe || 'No bio available'}</p>
              <p className="text-gray-500 text-sm mt-2">
                @{userData.nickname}
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setIsEditing(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <FaEdit className="w-4 h-4" />
                <span>Edit profile</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-white border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Left Column */}
          <div className="lg:col-span-1 space-y-4">
            <AboutSection />
            <PhotosSection />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-2">
            {activeTab === 'Posts' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <p className="text-gray-500 text-center py-8">No posts yet.</p>
              </div>
            )}

            {activeTab === 'About' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">About</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Basic Information</h4>
                    <div className="space-y-2">
                      <p className="text-gray-700"><span className="font-medium">Name:</span> {userData.firstName} {userData.lastName}</p>
                      <p className="text-gray-700"><span className="font-medium">Username:</span> @{userData.nickname}</p>
                      <p className="text-gray-700"><span className="font-medium">Email:</span> {userData.email}</p>
                      {userData.dateOfBirth && (
                        <p className="text-gray-700"><span className="font-medium">Date of Birth:</span> {formatDate(userData.dateOfBirth)}</p>
                      )}
                    </div>
                  </div>
                  {userData.aboutMe && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">About Me</h4>
                      <p className="text-gray-700">{userData.aboutMe}</p>
                    </div>
                  )}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Account Details</h4>
                    <div className="space-y-2">
                      <p className="text-gray-700"><span className="font-medium">Joined:</span> {formatDate(userData.createdAt)}</p>
                      <p className="text-gray-700"><span className="font-medium">Account Type:</span> {userData.private ? 'Private' : 'Public'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'Friends' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Friends</h3>
                <p className="text-gray-500 text-center py-8">Friends feature coming soon!</p>
              </div>
            )}

            {activeTab === 'Photos' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Photos</h3>
                <div className="grid grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                    <div key={i} className="aspect-square bg-gray-200 rounded-lg"></div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
