'use client'
import { useState } from 'react'
import Sidebar from '@/components/layout/Sidebar'
import { FaHeart, FaComment, FaShare, FaEllipsisH } from 'react-icons/fa'

export default function HomePage() {
  const [posts] = useState([
    {
      id: 1,
      author: {
        name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        avatar: ''
      },
      content: 'Just had an amazing day at the beach! 🏖️ The weather was perfect and the sunset was incredible.',
      image: null,
      timestamp: '2 hours ago',
      likes: 24,
      comments: 8,
      shares: 3
    },
    {
      id: 2,
      author: {
        name: '<PERSON>',
        username: 'jane<PERSON>',
        avatar: ''
      },
      content: 'Working on some exciting new projects! Can\'t wait to share what we\'ve been building. 💻✨',
      image: null,
      timestamp: '4 hours ago',
      likes: 15,
      comments: 5,
      shares: 2
    },
    {
      id: 3,
      author: {
        name: '<PERSON>',
        username: 'mikej',
        avatar: ''
      },
      content: 'Beautiful morning run through the park. Nothing beats starting the day with some fresh air and exercise! 🏃‍♂️',
      image: null,
      timestamp: '6 hours ago',
      likes: 31,
      comments: 12,
      shares: 5
    }
  ])

  const PostCard = ({ post }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
      {/* Post Header */}
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {post.author.avatar ? (
            <img
              src={post.author.avatar}
              alt={post.author.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
              {post.author.name.split(' ').map(n => n[0]).join('')}
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-[15px]">
              {post.author.name}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-[13px]">
              {post.timestamp}
            </p>
          </div>
        </div>
        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <FaEllipsisH className="w-4 h-4" />
        </button>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-gray-900 dark:text-white text-[15px] leading-relaxed">
          {post.content}
        </p>
      </div>

      {/* Post Image (if any) */}
      {post.image && (
        <div className="px-4 pb-3">
          <img
            src={post.image}
            alt="Post content"
            className="w-full rounded-lg object-cover"
          />
        </div>
      )}

      {/* Post Stats */}
      <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-gray-500 dark:text-gray-400 text-[13px]">
          <span>{post.likes} likes</span>
          <div className="flex space-x-4">
            <span>{post.comments} comments</span>
            <span>{post.shares} shares</span>
          </div>
        </div>
      </div>

      {/* Post Actions */}
      <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-around">
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaHeart className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Like</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaComment className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Comment</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <FaShare className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400 text-[15px] font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-950">
      <Sidebar />

      {/* Main Content - Facebook-style Feed */}
      <main className="flex-1 max-w-2xl mx-auto py-6 px-4">
        {/* Create Post Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold">
                JD
              </div>
              <input
                type="text"
                placeholder="What's on your mind, John?"
                className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-full px-4 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Posts Feed */}
        <div>
          {posts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center py-8">
          <button className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
            Load more posts
          </button>
        </div>
      </main>

      {/* Right Sidebar (empty for now, like Facebook) */}
      <div className="hidden lg:block w-80">
        {/* This could contain ads, suggestions, etc. like Facebook */}
      </div>
    </div>
  )
}
