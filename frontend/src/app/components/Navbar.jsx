'use client'
import {useAuth} from '../../context/AuthContext'
import { useRouter } from 'next/navigation'

export default function Navbar() {
  const {user, logout} = useAuth()
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  return (
    <nav className="flex justify-between p-4 bg-gray-900 text-white">
    <h1>IMSON</h1>
    {user ? (
      <button onClick={handleLogout}>Logout</button>
    ) : (
      <>
        <a href="/login">Login</a>
        <a href="/signup" className="ml-4">Sign Up</a>
      </>
    )}
  </nav>
)
}
