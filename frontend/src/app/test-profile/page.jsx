'use client'
import { useState } from 'react'

export default function TestProfilePage() {
  const [profileData, setProfileData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [userId, setUserId] = useState('test-user-123')
  const [testResults, setTestResults] = useState({})
  const [allTestsRunning, setAllTestsRunning] = useState(false)

  const testProfileCard = async () => {
    setLoading(true)
    setError('')
    setProfileData(null)

    try {
      console.log(`Testing profile card for user: ${userId}`)
      console.log('Making request to:', `http://localhost:33233/api/profile/card/${userId}`)

      const response = await fetch(`http://localhost:33233/api/profile/card/${userId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      console.log('Response received!')
      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (response.ok) {
        const data = await response.json()
        console.log('Profile card data:', data)
        setProfileData(data)
      } else {
        const errorText = await response.text()
        console.error('Error response:', errorText)
        setError(`Error ${response.status}: ${errorText}`)
        setProfileData({ error: `${response.status}: ${errorText}` })
      }
    } catch (err) {
      console.error('Network error details:', err)
      console.error('Error name:', err.name)
      console.error('Error message:', err.message)
      console.error('Error stack:', err.stack)
      setError(`Network error: ${err.message}`)
      setProfileData({ error: `Network error: ${err.message}` })
    } finally {
      setLoading(false)
    }
  }

  const testCurrentUser = async () => {
    setLoading(true)
    setError('')
    setProfileData(null)

    try {
      console.log('Testing current user endpoint')
      const response = await fetch('http://localhost:33233/api/user/current', {
        method: 'GET',
        credentials: 'include'
      })

      console.log('Current user response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Current user data:', data)
        setProfileData(data)
      } else {
        const errorText = await response.text()
        console.error('Current user error:', errorText)
        setError(`Error ${response.status}: ${errorText}`)
      }
    } catch (err) {
      console.error('Network error:', err)
      setError(`Network error: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const runAllProfileTests = async () => {
    setAllTestsRunning(true)
    setTestResults({})

    const tests = [
      {
        name: 'Current User',
        endpoint: 'http://localhost:33233/api/user/current',
        method: 'GET'
      },
      {
        name: 'Profile Card',
        endpoint: `http://localhost:33233/api/profile/card/${userId}`,
        method: 'GET'
      },
      {
        name: 'Full Profile',
        endpoint: `http://localhost:33233/api/profile/${userId}`,
        method: 'GET'
      },
      {
        name: 'Profile Page',
        endpoint: `http://localhost:33233/api/profile/page/${userId}`,
        method: 'GET'
      }
    ]

    const results = {}

    for (const test of tests) {
      try {
        console.log(`Testing ${test.name}...`)
        const response = await fetch(test.endpoint, {
          method: test.method,
          credentials: 'include'
        })

        const responseText = await response.text()
        let responseData = null

        try {
          responseData = JSON.parse(responseText)
        } catch {
          responseData = responseText
        }

        results[test.name] = {
          status: response.status,
          ok: response.ok,
          data: responseData,
          endpoint: test.endpoint
        }
      } catch (err) {
        results[test.name] = {
          status: 'ERROR',
          ok: false,
          data: err.message,
          endpoint: test.endpoint
        }
      }
    }

    setTestResults(results)
    setAllTestsRunning(false)
  }

  return (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Profile API Testing</h1>
        
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Test Profile Card Summary</h2>
          
          <div className="mb-4">
            <label className="block text-gray-300 mb-2">User ID:</label>
            <input
              type="text"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              className="w-full p-2 bg-gray-700 text-white rounded border border-gray-600"
              placeholder="Enter user ID to test"
            />
          </div>

          <div className="space-x-4 mb-4">
            <button
              onClick={testProfileCard}
              disabled={loading || allTestsRunning}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Profile Card'}
            </button>

            <button
              onClick={testCurrentUser}
              disabled={loading || allTestsRunning}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Current User'}
            </button>

            <button
              onClick={runAllProfileTests}
              disabled={loading || allTestsRunning}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {allTestsRunning ? 'Running All Tests...' : 'Run All Profile Tests'}
            </button>
          </div>

          {error && (
            <div className="bg-red-900 border border-red-700 text-red-300 p-4 rounded mb-4">
              <strong>Error:</strong> {error}
            </div>
          )}

          {profileData && (
            <div className="bg-gray-700 p-4 rounded">
              <h3 className="text-lg font-semibold text-white mb-2">Response Data:</h3>
              <pre className="text-gray-300 text-sm overflow-auto">
                {JSON.stringify(profileData, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {Object.keys(testResults).length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">All Tests Results</h2>
            <div className="space-y-4">
              {Object.entries(testResults).map(([testName, result]) => (
                <div key={testName} className={`p-4 rounded border-l-4 ${
                  result.ok ? 'border-green-500 bg-green-900/20' : 'border-red-500 bg-red-900/20'
                }`}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-white">{testName}</h3>
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.ok ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  <div className="text-sm text-gray-400 mb-2">{result.endpoint}</div>
                  <pre className="text-xs text-gray-300 overflow-auto max-h-32">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">API Endpoints</h2>
          <div className="space-y-2 text-gray-300">
            <div><strong>Profile Card:</strong> GET /api/profile/card/{`{id}`}</div>
            <div><strong>Current User:</strong> GET /api/user/current</div>
            <div><strong>Update Current User:</strong> PUT /api/user/current</div>
            <div><strong>Full Profile:</strong> GET /api/profile/{`{id}`}</div>
            <div><strong>Profile Page:</strong> GET /api/profile/page/{`{id}`}</div>
            <div><strong>Update Profile:</strong> PUT /api/profile/{`{id}`}</div>
            <div><strong>Delete Profile:</strong> DELETE /api/profile/{`{id}`}</div>
          </div>
        </div>
      </div>
    </div>
  )
}
