'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '../../context/AuthContext'

export default function SignupPage() {
  const { signup, user } = useAuth()
  const router = useRouter()

  const [form, setForm] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    dob: '',
    avatar: '',
    nickname: '',
    aboutMe: '',
    status: ''
  })

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e) => {
    const { name, value, files } = e.target
    setForm({
      ...form,
      [name]: files ? files[0] : value,
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const formData = new FormData()
    Object.entries(form).forEach(([key, value]) => {
      if (value) formData.append(key, value)
    })

    const success = await signup(formData)
    setLoading(false)

    if (success) {
      router.push('/login')
    } else {
      setError('Signup failed. Please try again.')
    }
  }

  useEffect(() => {
    if (user) {
      router.push('/login')
    }
  }, [user])

  return (
    <main className="min-h-screen flex bg-gray-950 text-white font-sans">
      {/* Sidebar branding */}
      <div className="hidden md:flex w-1/2 bg-gradient-to-br from-blue-900 via-gray-900 to-black items-center justify-center p-10">
        <h1
          className="text-6xl font-extrabold text-blue-500 tracking-widest text-center"
          style={{ fontFamily: "'Orbitron', sans-serif" }}
        >
          IMSON
        </h1>
      </div>

      {/* Signup Form Section */}
      <div className="w-full md:w-1/2 p-6 flex items-center justify-center">
        <div className="max-h-[95vh] overflow-y-auto scrollbar-hide">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <h2
              className="text-3xl font-bold text-blue-400 text-center"
              style={{ fontFamily: "'Orbitron', sans-serif" }}
            >
              Create Your Account
            </h2>

            {error && <p className="text-red-500 text-sm">{error}</p>}

            {/* First & Last Name */}
            <div className="flex gap-4">
              <div className="w-1/2">
                <label className="block text-sm font-medium mb-1">First Name <span className="text-red-500">*</span></label>
                <input
                  type="text"
                  name="firstName"
                  required
                  className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  onChange={handleChange}
                />
              </div>
              <div className="w-1/2">
                <label className="block text-sm font-medium mb-1">Last Name <span className="text-red-500">*</span></label>
                <input
                  type="text"
                  name="lastName"
                  required
                  className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  onChange={handleChange}
                />
              </div>
            </div>

            {/* Email & Visibility */}
            <div className="flex gap-4">
              <div className="w-2/3">
                <label className="block text-sm font-medium mb-1">Email <span className="text-red-500">*</span></label>
                <input
                  type="email"
                  name="email"
                  required
                  className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  onChange={handleChange}
                />
              </div>
              <div className="w-1/3">
                <label className="block text-sm font-medium mb-1">Visibility <span className="text-red-500">*</span></label>
                <select
                  name="status"
                  required
                  defaultValue="public"
                  className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  onChange={handleChange}
                >
                  <option value="private">Private</option>
                  <option value="public">Public</option>
                </select>
              </div>
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium mb-1">Password <span className="text-red-500">*</span></label>
              <input
                type="password"
                name="password"
                required
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                onChange={handleChange}
              />
            </div>

            {/* Date of Birth */}
            <div>
              <label className="block text-sm font-medium mb-1">Date of Birth <span className="text-red-500">*</span></label>
              <input
                type="date"
                name="dob"
                required
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700"
                onChange={handleChange}
              />
            </div>

            {/* Nickname */}
            <div>
              <label className="block text-sm font-medium mb-1">Nickname <span className="text-gray-500">(optional)</span></label>
              <input
                type="text"
                name="nickname"
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700"
                onChange={handleChange}
              />
            </div>

            {/* About Me */}
            <div>
              <label className="block text-sm font-medium mb-1">About Me <span className="text-gray-500">(optional)</span></label>
              <textarea
                name="aboutMe"
                rows="3"
                className="w-full p-3 rounded-md bg-gray-800 border border-gray-700"
                onChange={handleChange}
              />
            </div>

            {/* Avatar Upload */}
            <div>
              <label className="block text-sm font-medium mb-1">Avatar <span className="text-gray-500">(optional)</span></label>
              <input
                type="file"
                name="avatar"
                accept="image/*"
                className="w-full p-2 rounded-md bg-gray-800 border border-gray-700"
                onChange={handleChange}
              />
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-purple-600 hover:to-blue-500 text-white font-semibold py-3 px-4 rounded-md shadow-lg transition-all duration-300"
            >
              {loading ? 'Creating account...' : 'Sign Up'}
            </button>

            {/* Login Link */}
            <p className="text-sm text-center text-gray-400">
              Already have an account?{' '}
              <a href="/login" className="text-blue-500 hover:underline">Log in</a>
            </p>
          </form>
        </div>
      </div>
    </main>
  )
}
