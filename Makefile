.PHONY: up down backend frontend fmt lint run-dev test clean clean-all help coverage-html
help:            ## Show this help
	@echo " up - Build & start all containers synchronouly"
	@echo "   down - Stop & remove containers"
	@echo "   backend - Rebuild / restart back‑end only"
	@echo "   frontend - Rebuild / restart front‑end only"
	@echo "   fmt - Go format"
	@echo "   lint - Example lint target (needs golangci‑lint installed)"
	@echo "   run-dev - Run the dev server"
	@echo "   help - Show this help"
	@echo "   clean - Remove all containers and volumes"
	@echo "   clean-all - Remove all containers, volumes, images and networks"
	@echo "   clean-images - Remove all images"
	@echo "   clean-volumes - Remove all 
	@echo "   clean-networks - Remove
	@ech0 "	  run-dev - Run the frontend and the backend servers"

up:              ## Build & start all containers synchronouly
	docker-compose up -d --build

down:            ## Stop & remove containers
	docker-compose down

backend:         ## Rebuild / restart back‑end only
	docker-compose up -d --build backend

frontend:        ## Rebuild / restart front‑end only
	docker-compose up -d --build frontend

fmt:             ## Go format
	go fmt ./...

lint:            ## Example lint target (needs golangci‑lint installed)
	golangci-lint run

run-dev:
	@cd frontend && npm install &&npm run dev
	@cd backend && go mod tidy && go run server.go

test:            ## Run tests
	go test ./...

coverage-html:  ## Generate HTML coverage report
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

clean:           ## Remove all containers and volumes
	docker-compose down --volumes coverage.out coverage.html

clean-all:       ## Remove all containers, volumes, images and networks
	docker-compose down --volumes --rmi all --remove-orphans