-- Create the groups table
CREATE TABLE IF NOT EXISTS groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    creator_id TEXT NOT NULL,
    cardinality INTEGER DEFAULT 1,
    administrator TEXT,
    about TEXT,
    topics TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREI<PERSON><PERSON> KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (administrator) REFERENCES users(id) ON DELETE SET NULL
);

-- Create the group messages table
CREATE TABLE IF NOT EXISTS groups_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    groupId INTEGER NOT NULL,
    sender_id TEXT NOT NULL,
    content TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_status INTEGER NOT NULL DEFAULT 0
);
