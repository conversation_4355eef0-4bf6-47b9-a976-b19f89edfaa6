-- Add cover photo and settings columns to users table
ALTER TABLE users ADD COLUMN cover_url TEXT NULL;
ALTER TABLE users ADD COLUMN email_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN push_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_online_status BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN allow_messages_from_all BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE;

-- Add image_url column to posts table if it doesn't exist
ALTER TABLE posts ADD COLUMN image_url TEXT NULL;

-- Create static directories for file uploads (this will be handled by the application)
-- The application will create these directories: static/covers, static/avatars

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_cover_url ON users(cover_url);
CREATE INDEX IF NOT EXISTS idx_posts_image_url ON posts(image_url);
