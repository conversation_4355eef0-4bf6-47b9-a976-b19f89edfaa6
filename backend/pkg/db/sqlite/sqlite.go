package db

import (
	"database/sql"
	"fmt"

	"github.com/golang-migrate/migrate/v4"
	sqlite3m "github.com/golang-migrate/migrate/v4/database/sqlite"
	_ "github.com/mattn/go-sqlite3"
)

var DBConn *sql.DB

// Init runs migrations and returns a ready-to-use DB.
func Init(path, migrationsDir string) (*sql.DB, error) {
	fmt.Println("The db is up and running")
	conn, err := sql.Open("sqlite3", path)
	if err != nil {
		return nil, fmt.Errorf("open db: %w", err)
	}
	// Optional: ensure real connection
	if err := conn.Ping(); err != nil {
		conn.Close()
		return nil, fmt.Errorf("ping db: %w", err)
	}

	driver, err := sqlite3m.WithInstance(conn, &sqlite3m.Config{})
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("sqlite migrate instance: %w", err)
	}

	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", migrationsDir),
		"sqlite3",
		driver,
	)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("migration setup: %w", err)
	}
	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		conn.Close()
		return nil, fmt.Errorf("run migrations: %w", err)
	}

	DBConn = conn
	return DBConn, nil
}

// GetDB returns the underlying *sql.DB for queries or Exec calls.
func GetDB() *sql.DB {
	return DBConn
}

// Close shuts down the database connection.
func Close() error {
	return DBConn.Close()
}
