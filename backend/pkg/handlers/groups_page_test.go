package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	db "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

func init() {
	// Setup mock database
	mockDB, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	db.DBConn = mockDB

	// Create users table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			nickname TEXT NOT NULL,
			first_name TEXT NOT NULL,
			last_name TEXT NOT NULL,
			email TEXT UNIQUE NOT NULL,
			avatar_url TEXT
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create groups table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS groups (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_private BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIG<PERSON> KEY (creator_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create group_members table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS group_members (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			role TEXT DEFAULT 'member',
			joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			nickname TEXT,
			first_name TEXT,
			last_name TEXT,
			avatar_url TEXT,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (user_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create group_messages table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS group_messages (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			sender_id TEXT NOT NULL,
			content TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			sender_nickname TEXT,
			sender_name TEXT,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (sender_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create events table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS events (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			creator_id TEXT NOT NULL,
			title TEXT NOT NULL,
			description TEXT,
			event_date TIMESTAMP NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (creator_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test users
	_, err = mockDB.Exec(`
		INSERT INTO users (id, nickname, first_name, last_name, email, avatar_url)
		VALUES
			('user123', 'john_doe', 'John', 'Doe', '<EMAIL>', 'avatar1.jpg'),
			('user456', 'jane_doe', 'Jane', 'Doe', '<EMAIL>', 'avatar2.jpg'),
			('user789', 'bob_smith', 'Bob', 'Smith', '<EMAIL>', 'avatar3.jpg'),
			('user999', 'alice_wonder', 'Alice', 'Wonder', '<EMAIL>', NULL)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test groups
	_, err = mockDB.Exec(`
		INSERT INTO groups (id, name, description, creator_id, is_private, created_at)
		VALUES
			(1, 'Tech Chat', 'Real-time tech discussions', 'user123', FALSE, '2024-01-01 10:00:00'),
			(2, 'Private Chat', 'Private group chat', 'user456', TRUE, '2024-01-02 11:00:00'),
			(3, 'Gaming Hub', 'Gaming community chat', 'user789', FALSE, '2024-01-03 12:00:00')
	`)
	if err != nil {
		panic(err)
	}

	// Insert group members with full details
	_, err = mockDB.Exec(`
		INSERT INTO group_members (group_id, user_id, role, nickname, first_name, last_name, avatar_url, joined_at)
		VALUES
			(1, 'user123', 'creator', 'john_doe', 'John', 'Doe', 'avatar1.jpg', '2024-01-01 10:00:00'),
			(1, 'user456', 'admin', 'jane_doe', 'Jane', 'Doe', 'avatar2.jpg', '2024-01-01 11:00:00'),
			(1, 'user789', 'member', 'bob_smith', 'Bob', 'Smith', 'avatar3.jpg', '2024-01-01 12:00:00'),
			(2, 'user456', 'creator', 'jane_doe', 'Jane', 'Doe', 'avatar2.jpg', '2024-01-02 11:00:00'),
			(2, 'user123', 'member', 'john_doe', 'John', 'Doe', 'avatar1.jpg', '2024-01-02 12:00:00'),
			(3, 'user789', 'creator', 'bob_smith', 'Bob', 'Smith', 'avatar3.jpg', '2024-01-03 12:00:00')
	`)
	if err != nil {
		panic(err)
	}

	// Insert test messages
	_, err = mockDB.Exec(`
		INSERT INTO group_messages (id, group_id, sender_id, content, sender_nickname, sender_name, created_at)
		VALUES
			(1, 1, 'user123', 'Welcome to the tech chat!', 'john_doe', 'John Doe', '2024-01-01 13:00:00'),
			(2, 1, 'user456', 'Thanks for creating this group!', 'jane_doe', 'Jane Doe', '2024-01-01 13:05:00'),
			(3, 1, 'user789', 'Looking forward to great discussions', 'bob_smith', 'Bob Smith', '2024-01-01 13:10:00'),
			(4, 2, 'user456', 'Private group message', 'jane_doe', 'Jane Doe', '2024-01-02 14:00:00'),
			(5, 2, 'user123', 'This is exclusive!', 'john_doe', 'John Doe', '2024-01-02 14:05:00')
	`)
	if err != nil {
		panic(err)
	}

	// Insert test events for activity feed
	_, err = mockDB.Exec(`
		INSERT INTO events (id, group_id, creator_id, title, description, event_date, created_at)
		VALUES
			(1, 1, 'user123', 'Tech Meetup', 'Monthly tech meetup', '2024-06-15 18:00:00', '2024-01-15 10:00:00'),
			(2, 1, 'user456', 'Code Review Session', 'Weekly code review', '2024-07-20 14:00:00', '2024-01-20 09:00:00')
	`)
	if err != nil {
		panic(err)
	}
}

func TestGetGroupPageHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		groupID        string
		expectedStatus int
		expectError    bool
	}{
		{
			name:           "Valid group page request",
			method:         "GET",
			groupID:        "1",
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "Invalid method",
			method:         "POST",
			groupID:        "1",
			expectedStatus: http.StatusMethodNotAllowed,
			expectError:    true,
		},
		{
			name:           "Missing group ID",
			method:         "GET",
			groupID:        "",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "Invalid group ID",
			method:         "GET",
			groupID:        "invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "Non-existent group",
			method:         "GET",
			groupID:        "99999",
			expectedStatus: http.StatusNotFound,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, "/api/groups/page?group_id="+tt.groupID, nil)
			if err != nil {
				t.Fatal(err)
			}

			// Add mock session cookie for authentication
			req.AddCookie(&http.Cookie{
				Name:  "session_token",
				Value: "mock_session_token",
			})

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(GetGroupPageHandler)
			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != tt.expectedStatus {
				t.Errorf("handler returned wrong status code: got %v want %v", status, tt.expectedStatus)
			}

			if !tt.expectError && rr.Code == http.StatusOK {
				var pageData GroupPageData
				err := json.Unmarshal(rr.Body.Bytes(), &pageData)
				if err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				// Verify response structure
				if pageData.Group.ID == 0 {
					t.Error("Expected group ID to be set")
				}
				if pageData.Group.Name == "" {
					t.Error("Expected group name to be set")
				}
				// Members slice should be initialized (even if empty)
				if pageData.Members == nil {
					t.Error("Expected members slice to be initialized")
				}
			}
		})
	}
}

func TestGetGroupMembersHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		groupID        string
		expectedStatus int
		expectError    bool
	}{
		{
			name:           "Valid group members request",
			method:         "GET",
			groupID:        "1",
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "Invalid method",
			method:         "POST",
			groupID:        "1",
			expectedStatus: http.StatusMethodNotAllowed,
			expectError:    true,
		},
		{
			name:           "Missing group ID",
			method:         "GET",
			groupID:        "",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "Invalid group ID",
			method:         "GET",
			groupID:        "invalid",
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "Non-existent group",
			method:         "GET",
			groupID:        "99999",
			expectedStatus: http.StatusNotFound,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, "/api/groups/members?group_id="+tt.groupID, nil)
			if err != nil {
				t.Fatal(err)
			}

			// Add mock session cookie for authentication
			req.AddCookie(&http.Cookie{
				Name:  "session_token",
				Value: "mock_session_token",
			})

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(GetGroupMembersHandler)
			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != tt.expectedStatus {
				t.Errorf("handler returned wrong status code: got %v want %v", status, tt.expectedStatus)
			}

			if !tt.expectError && rr.Code == http.StatusOK {
				var members []m.GroupMember
				err := json.Unmarshal(rr.Body.Bytes(), &members)
				if err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				// Members slice should be initialized (even if empty)
				if members == nil {
					t.Error("Expected members slice to be initialized")
				}
			}
		})
	}
}

func TestGroupPageDataStructure(t *testing.T) {
	pageData := GroupPageData{
		Group: m.Group{
			ID:          1,
			Name:        "Test Group",
			About:       "A test group",
			CreatorID:   "user123",
			IsPrivate:   false,
			Cardinality: 3,
		},
		Members: []map[string]interface{}{
			{
				"id":         1,
				"group_id":   1,
				"user_id":    "user123",
				"role":       "creator",
				"nickname":   "john_doe",
				"first_name": "John",
				"last_name":  "Doe",
				"avatar_url": "avatar1.jpg",
				"is_online":  true,
				"status":     "online",
			},
			{
				"id":         2,
				"group_id":   1,
				"user_id":    "user456",
				"role":       "admin",
				"nickname":   "jane_doe",
				"first_name": "Jane",
				"last_name":  "Doe",
				"avatar_url": "avatar2.jpg",
				"is_online":  false,
				"status":     "offline",
			},
			{
				"id":         3,
				"group_id":   1,
				"user_id":    "user789",
				"role":       "member",
				"nickname":   "bob_smith",
				"first_name": "Bob",
				"last_name":  "Smith",
				"avatar_url": "avatar3.jpg",
				"is_online":  false,
				"status":     "offline",
			},
		},
		IsUserMember: true,
		UserRole:     "creator",
	}

	// Test JSON marshaling
	data, err := json.Marshal(pageData)
	if err != nil {
		t.Errorf("Failed to marshal GroupPageData: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled GroupPageData
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Errorf("Failed to unmarshal GroupPageData: %v", err)
	}

	// Verify data integrity
	if unmarshaled.Group.ID != pageData.Group.ID {
		t.Errorf("Group ID mismatch: got %d, want %d", unmarshaled.Group.ID, pageData.Group.ID)
	}
	if len(unmarshaled.Members) != len(pageData.Members) {
		t.Errorf("Members count mismatch: got %d, want %d", len(unmarshaled.Members), len(pageData.Members))
	}
	if unmarshaled.IsUserMember != pageData.IsUserMember {
		t.Errorf("IsUserMember mismatch: got %t, want %t", unmarshaled.IsUserMember, pageData.IsUserMember)
	}
	if unmarshaled.UserRole != pageData.UserRole {
		t.Errorf("UserRole mismatch: got %s, want %s", unmarshaled.UserRole, pageData.UserRole)
	}

	// Verify member details
	if len(unmarshaled.Members) > 0 {
		firstMember := unmarshaled.Members[0]
		expectedMember := pageData.Members[0]

		if firstMember["user_id"] != expectedMember["user_id"] {
			t.Errorf("Member UserID mismatch: got %v, want %v", firstMember["user_id"], expectedMember["user_id"])
		}
		if firstMember["role"] != expectedMember["role"] {
			t.Errorf("Member Role mismatch: got %v, want %v", firstMember["role"], expectedMember["role"])
		}
		if firstMember["nickname"] != expectedMember["nickname"] {
			t.Errorf("Member Nickname mismatch: got %v, want %v", firstMember["nickname"], expectedMember["nickname"])
		}
	}
}

func TestPrivateGroupAccess(t *testing.T) {
	// This test would require a more complex setup with actual database
	// For now, we'll test the structure and basic validation

	req, err := http.NewRequest("GET", "/api/groups/page?group_id=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Test without authentication (should fail)
	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(GetGroupPageHandler)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusUnauthorized {
		t.Errorf("Expected unauthorized status for unauthenticated request: got %v want %v", status, http.StatusUnauthorized)
	}
}

func TestMemberRoleOrdering(t *testing.T) {
	// Test that the expected role ordering is maintained in the query
	// This is more of a documentation test for the expected behavior

	expectedRoleOrder := []string{"creator", "admin", "member"}

	// Verify that our role ordering logic is correct
	for i, role := range expectedRoleOrder {
		switch role {
		case "creator":
			if i != 0 {
				t.Errorf("Creator should be first in order, got position %d", i)
			}
		case "admin":
			if i != 1 {
				t.Errorf("Admin should be second in order, got position %d", i)
			}
		case "member":
			if i != 2 {
				t.Errorf("Member should be third in order, got position %d", i)
			}
		}
	}
}
