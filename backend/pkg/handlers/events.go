package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// CreateEventHandler creates a new event in a group
func CreateEventHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var req m.CreateEventRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	if req.Title == "" || req.EventDate.IsZero() {
		http.Error(w, "Title and event date are required", http.StatusBadRequest)
		return
	}

	// Check if user is a member of the group
	var memberExists bool
	err = sq.GetDB().QueryRow(`
		SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
		req.GroupID, userID).Scan(&memberExists)
	if err != nil || !memberExists {
		http.Error(w, "You are not a member of this group", http.StatusForbidden)
		return
	}

	// Create the event
	result, err := sq.GetDB().Exec(`
		INSERT INTO events (group_id, creator_id, title, description, event_date, created_at)
		VALUES (?, ?, ?, ?, ?, ?)`,
		req.GroupID, userID, req.Title, req.Description, req.EventDate, time.Now())
	if err != nil {
		log.Printf("Error creating event: %v", err)
		http.Error(w, "Failed to create event", http.StatusInternalServerError)
		return
	}

	eventID, err := result.LastInsertId()
	if err != nil {
		log.Printf("Error getting event ID: %v", err)
		http.Error(w, "Failed to create event", http.StatusInternalServerError)
		return
	}

	// Get creator name for notifications
	var creatorName string
	err = sq.GetDB().QueryRow(`
		SELECT first_name || ' ' || last_name FROM users WHERE id = ?`,
		userID).Scan(&creatorName)
	if err != nil {
		log.Printf("Error getting creator name: %v", err)
		creatorName = "Someone"
	}

	// Create notifications for all group members
	err = notificationService.CreateEventNotification(req.GroupID, int(eventID), req.Title, userID, creatorName)
	if err != nil {
		log.Printf("Error creating event notifications: %v", err)
	}

	// Broadcast real-time notifications to group members
	go func() {
		// Get group name
		var groupName string
		sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", req.GroupID).Scan(&groupName)

		// Get all group members except the creator
		rows, err := sq.GetDB().Query(`
			SELECT user_id FROM group_members 
			WHERE group_id = ? AND user_id != ?`,
			req.GroupID, userID)
		if err != nil {
			return
		}
		defer rows.Close()

		data := m.EventNotificationData{
			EventID:     int(eventID),
			EventTitle:  req.Title,
			GroupID:     req.GroupID,
			GroupName:   groupName,
			CreatorID:   userID,
			CreatorName: creatorName,
		}

		for rows.Next() {
			var memberID string
			if err := rows.Scan(&memberID); err != nil {
				continue
			}

			CreateAndBroadcastNotification(
				memberID,
				"event_created",
				"New Event",
				fmt.Sprintf("New event '%s' created in %s by %s", req.Title, groupName, creatorName),
				data,
			)
		}
	}()

	event := m.Event{
		ID:          int(eventID),
		GroupID:     req.GroupID,
		CreatorID:   userID,
		Title:       req.Title,
		Description: req.Description,
		EventDate:   req.EventDate,
		CreatedAt:   time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(event)
}

// GetGroupEventsHandler retrieves events for a specific group
func GetGroupEventsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupIDStr := r.URL.Query().Get("group_id")
	if groupIDStr == "" {
		http.Error(w, "Group ID is required", http.StatusBadRequest)
		return
	}

	groupID, err := strconv.Atoi(groupIDStr)
	if err != nil {
		http.Error(w, "Invalid group ID", http.StatusBadRequest)
		return
	}

	// Check if user is a member of the group
	var memberExists bool
	err = sq.GetDB().QueryRow(`
		SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
		groupID, userID).Scan(&memberExists)
	if err != nil || !memberExists {
		http.Error(w, "You are not a member of this group", http.StatusForbidden)
		return
	}

	// Get events with creator details and user's response
	rows, err := sq.GetDB().Query(`
		SELECT e.id, e.group_id, e.creator_id, e.title, e.description, 
			   e.event_date, e.created_at,
			   g.name as group_name,
			   u.first_name || ' ' || u.last_name as creator_name,
			   COALESCE(er.response, '') as user_response,
			   COUNT(er2.id) as response_count
		FROM events e
		JOIN groups g ON e.group_id = g.id
		JOIN users u ON e.creator_id = u.id
		LEFT JOIN event_responses er ON e.id = er.event_id AND er.user_id = ?
		LEFT JOIN event_responses er2 ON e.id = er2.event_id
		WHERE e.group_id = ?
		GROUP BY e.id
		ORDER BY e.event_date ASC`,
		userID, groupID)
	if err != nil {
		log.Printf("Error getting events: %v", err)
		http.Error(w, "Failed to get events", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var events []m.Event
	for rows.Next() {
		var event m.Event
		err := rows.Scan(
			&event.ID, &event.GroupID, &event.CreatorID,
			&event.Title, &event.Description, &event.EventDate,
			&event.CreatedAt, &event.GroupName, &event.CreatorName,
			&event.UserResponse, &event.ResponseCount,
		)
		if err != nil {
			log.Printf("Error scanning event: %v", err)
			continue
		}
		events = append(events, event)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(events)
}

// RespondToEventHandler handles user responses to events
func RespondToEventHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var req m.EventResponseRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	if req.Response != "going" && req.Response != "not_going" && req.Response != "maybe" {
		http.Error(w, "Invalid response. Must be 'going', 'not_going', or 'maybe'", http.StatusBadRequest)
		return
	}

	// Check if user is a member of the group that owns this event
	var memberExists bool
	err = sq.GetDB().QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM group_members gm
			JOIN events e ON gm.group_id = e.group_id
			WHERE e.id = ? AND gm.user_id = ?
		)`, req.EventID, userID).Scan(&memberExists)
	if err != nil || !memberExists {
		http.Error(w, "You are not a member of this group", http.StatusForbidden)
		return
	}

	// Insert or update the response
	_, err = sq.GetDB().Exec(`
		INSERT INTO event_responses (event_id, user_id, response, created_at)
		VALUES (?, ?, ?, ?)
		ON CONFLICT(event_id, user_id) 
		DO UPDATE SET response = ?, created_at = ?`,
		req.EventID, userID, req.Response, time.Now(),
		req.Response, time.Now())
	if err != nil {
		log.Printf("Error saving event response: %v", err)
		http.Error(w, "Failed to save response", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "response saved"})
}

// GetEventResponsesHandler gets responses for a specific event
func GetEventResponsesHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	eventIDStr := r.URL.Query().Get("event_id")
	if eventIDStr == "" {
		http.Error(w, "Event ID is required", http.StatusBadRequest)
		return
	}

	eventID, err := strconv.Atoi(eventIDStr)
	if err != nil {
		http.Error(w, "Invalid event ID", http.StatusBadRequest)
		return
	}

	// Check if user is a member of the group that owns this event
	var memberExists bool
	err = sq.GetDB().QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM group_members gm
			JOIN events e ON gm.group_id = e.group_id
			WHERE e.id = ? AND gm.user_id = ?
		)`, eventID, userID).Scan(&memberExists)
	if err != nil || !memberExists {
		http.Error(w, "You are not a member of this group", http.StatusForbidden)
		return
	}

	// Get all responses for the event
	rows, err := sq.GetDB().Query(`
		SELECT er.id, er.event_id, er.user_id, er.response, er.created_at,
			   u.first_name || ' ' || u.last_name as user_name,
			   u.nickname, u.avatar_url
		FROM event_responses er
		JOIN users u ON er.user_id = u.id
		WHERE er.event_id = ?
		ORDER BY er.created_at DESC`,
		eventID)
	if err != nil {
		log.Printf("Error getting event responses: %v", err)
		http.Error(w, "Failed to get responses", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	responses := make(map[string][]map[string]interface{})
	responses["going"] = []map[string]interface{}{}
	responses["not_going"] = []map[string]interface{}{}
	responses["maybe"] = []map[string]interface{}{}

	for rows.Next() {
		var response m.EventResponse
		var userName, nickname string
		var avatarURL sql.NullString

		err := rows.Scan(
			&response.ID, &response.EventID, &response.UserID,
			&response.Response, &response.CreatedAt,
			&userName, &nickname, &avatarURL,
		)
		if err != nil {
			log.Printf("Error scanning response: %v", err)
			continue
		}

		userInfo := map[string]interface{}{
			"user_id":    response.UserID,
			"user_name":  userName,
			"nickname":   nickname,
			"created_at": response.CreatedAt,
		}

		if avatarURL.Valid {
			userInfo["avatar_url"] = avatarURL.String
		}

		responses[response.Response] = append(responses[response.Response], userInfo)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(responses)
}