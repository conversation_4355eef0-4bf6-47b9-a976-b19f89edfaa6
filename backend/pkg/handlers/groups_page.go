package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	"imson/pkg/services"
)

// GroupPageData represents the data needed for the main group interaction page
type GroupPageData struct {
	Group          m.Group                  `json:"group"`
	Members        []map[string]interface{} `json:"members"`
	RecentMessages []m.GroupMessage         `json:"recent_messages"`
	ActivityFeed   []map[string]interface{} `json:"activity_feed"`
	IsUserMember   bool                     `json:"is_user_member"`
	UserRole       string                   `json:"user_role,omitempty"`
	CanPost        bool                     `json:"can_post"`
	OnlineCount    int                      `json:"online_count"`
}

// GroupMemberWithStatus extends GroupMember with online status
type GroupMemberWithStatus struct {
	m.GroupMember
	IsOnline bool   `json:"is_online"`
	LastSeen string `json:"last_seen,omitempty"`
	Status   string `json:"status,omitempty"` // "online", "away", "busy", "offline"
}

// ActivityItem represents an activity in the group feed
type ActivityItem struct {
	ID         int    `json:"id"`
	Type       string `json:"type"` // "message", "join", "leave", "event", "file_upload"
	UserID     string `json:"user_id"`
	UserName   string `json:"user_name"`
	UserAvatar string `json:"user_avatar,omitempty"`
	Content    string `json:"content"`
	Timestamp  string `json:"timestamp"`
	GroupID    int    `json:"group_id"`
	RelatedID  int    `json:"related_id,omitempty"` // ID of related message, event, etc.
}

// GetGroupPageHandler returns the main group interaction page with chat, members, and activity
// Purpose: Main group page /groups/123 where users participate in the group
// Features: Chat/activity feed (central) + member sidebar with online status
func GetGroupPageHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupID, err := validateGroupID(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Check access permissions
	canAccess, isUserMember, err := checkGroupAccess(groupID, userID)
	if handleGroupError(w, err, "check group access") {
		return
	}

	if !canAccess {
		http.Error(w, "Access denied: You must be a member to view this private group", http.StatusForbidden)
		return
	}

	// Get group information
	group, err := getGroupBasicInfo(groupID)
	if handleGroupError(w, err, "get group information") {
		return
	}

	// Build page data using service layer with WebSocket integration
	groupService := services.NewGroupService()
	userRole := checkUserMembership(groupID, userID)

	// Get members with real-time online status from service layer
	// The service layer now handles WebSocket integration internally
	members := groupService.GetGroupMembersWithStatus(groupID)

	// Get online count using the service layer
	onlineCount := groupService.GetGroupOnlineCount(groupID)

	pageData := GroupPageData{
		Group:          *group,
		IsUserMember:   isUserMember,
		UserRole:       userRole,
		CanPost:        isUserMember, // Members can post messages
		Members:        members,
		RecentMessages: groupService.GetRecentGroupMessages(groupID, 50),
		ActivityFeed:   groupService.GetGroupActivityFeed(groupID, 20),
		OnlineCount:    onlineCount, // Real-time online count from WebSocket
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(pageData)
}

// GetGroupMembersHandler returns only the members of a group (for dynamic updates)
func GetGroupMembersHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupID, err := validateGroupID(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Check access permissions using utility function
	canAccess, _, err := checkGroupAccess(groupID, userID)
	if handleGroupError(w, err, "check group access") {
		return
	}

	if !canAccess {
		http.Error(w, "Access denied: You must be a member to view this private group's members", http.StatusForbidden)
		return
	}

	// Get group members using utility function
	members := getGroupMembers(groupID, 0) // 0 = no limit

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(members)
}
