package handlers

import (
	"database/sql"
	m "imson/pkg/models"
	"reflect"
	"regexp"
	"testing"
	"time"
	"github.com/DATA-DOG/go-sqlmock"
)

func Test_finalMessages(t *testing.T) {
	type args struct {
		msgs   []m.Message
		limit  int
		offset int
	}
	tests := []struct {
		name string
		args args
		want []m.Message
	}{
		{
			name: "empty instance",
			args: args{
				msgs:   []m.Message{},
				limit:  0,
				offset: 0,
			},
			want: nil,
		},
		{
			name: "length longer ",
			args: args{
				msgs:   []m.Message{},
				limit:  0,
				offset: 0,
			},
			want: nil,
		},
		{
			name: "lenght longer 2",
			args: args{
				msgs: []m.Message{
					{ID: 2,
						SenderID:      "269872735010yug2i4oru",
						ReceiverID:    "872g3uf09j13-9u9h4t",
						Content:       "This is an example message of the Astrological mind of <PERSON>,but do not forget the numbers",
						CurrentUserId: "872g3uf09j13-9u9h4t",
						ReadStatus:    1},
				},
				limit:  2,
				offset: 0,
			},
			want: []m.Message{
				{ID: 2,
					SenderID:      "269872735010yug2i4oru",
					ReceiverID:    "872g3uf09j13-9u9h4t",
					Content:       "This is an example message of the Astrological mind of Raymond,but do not forget the numbers",
					CurrentUserId: "872g3uf09j13-9u9h4t",
					ReadStatus:    1},
			},
		},
		{
			name: "offset greater than limit",
			args: args{
				msgs: []m.Message{
					{ID: 2,
						SenderID:      "269872735010yug2i4oru",
						ReceiverID:    "872g3uf09j13-9u9h4t",
						Content:       "This is an example message of the Astrological mind of Raymond,but do not forget the numbers",
						CurrentUserId: "872g3uf09j13-9u9h4t",
						ReadStatus:    1},
				},
				limit:  2,
				offset: 3,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := finalMessages(tt.args.msgs, tt.args.limit, tt.args.offset); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("finalMessages() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLatestPrivateMessage(t *testing.T) {
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer mockDB.Close()
	DB = mockDB
	
	now := time.Now()
	expectedMsg := m.Message{
		ID:            1,
		SenderID:      Sender_id,
		ReceiverID:    Receiver_id,
		Content:       "Hello",
		Timestamp:     now,
		CurrentUserId: Sender_id,
	}

	tests := []struct {
		name    string
		mockFn  func()
		want    m.Message
		wantErr bool
	}{
		{
			name: "Success - message found",
			mockFn: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT id, sender_id, receiver_id, content, created_at
					FROM messages
					WHERE sender_id = ? AND receiver_id = ?
					ORDER BY created_at DESC
					LIMIT 1
				`)).
					WithArgs(Sender_id, Receiver_id).
					WillReturnRows(sqlmock.NewRows([]string{"id", "sender_id", "receiver_id", "content", "created_at"}).
						AddRow(expectedMsg.ID, expectedMsg.SenderID, expectedMsg.ReceiverID, expectedMsg.Content, expectedMsg.Timestamp))
			},
			want:    expectedMsg,
			wantErr: false,
		},
		{
			name: "Error - no rows",
			mockFn: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT id, sender_id, receiver_id, content, created_at
					FROM messages
					WHERE sender_id = ? AND receiver_id = ?
					ORDER BY created_at DESC
					LIMIT 1
				`)).
					WithArgs(Sender_id, Receiver_id).
					WillReturnError(sql.ErrNoRows)
			},
			want:    m.Message{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFn()

			got, err := LatestPrivateMessage()
			if (err != nil) != tt.wantErr {
				t.Errorf("LatestPrivateMessage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LatestPrivateMessage() = %v, want %v", got, tt.want)
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}
