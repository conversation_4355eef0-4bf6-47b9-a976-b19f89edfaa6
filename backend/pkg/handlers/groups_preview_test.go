package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	db "imson/pkg/db/sqlite"
	m "imson/pkg/models"

	_ "github.com/mattn/go-sqlite3"
)

func init() {
	// Setup mock database
	mockDB, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	db.DBConn = mockDB

	// Create users table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			nickname TEXT NOT NULL,
			first_name TEXT NOT NULL,
			last_name TEXT NOT NULL,
			email TEXT UNIQUE NOT NULL,
			avatar_url TEXT
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create groups table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS groups (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_private BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIG<PERSON> KEY (creator_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create group_members table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS group_members (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			role TEXT DEFAULT 'member',
			joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (user_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test users
	_, err = mockDB.Exec(`
		INSERT INTO users (id, nickname, first_name, last_name, email)
		VALUES
			('user123', 'john_doe', 'John', 'Doe', '<EMAIL>'),
			('user456', 'jane_doe', 'Jane', 'Doe', '<EMAIL>'),
			('user789', 'bob_smith', 'Bob', 'Smith', '<EMAIL>')
	`)
	if err != nil {
		panic(err)
	}

	// Insert test groups
	_, err = mockDB.Exec(`
		INSERT INTO groups (id, name, description, creator_id, is_private)
		VALUES
			(1, 'Tech Enthusiasts', 'A group for technology lovers', 'user123', FALSE),
			(2, 'Private Club', 'Exclusive private group', 'user456', TRUE),
			(3, 'Empty Group', 'Group with no members', 'user789', FALSE)
	`)
	if err != nil {
		panic(err)
	}

	// Insert group members
	_, err = mockDB.Exec(`
		INSERT INTO group_members (group_id, user_id, role)
		VALUES
			(1, 'user123', 'creator'),
			(1, 'user456', 'member'),
			(1, 'user789', 'member'),
			(2, 'user456', 'creator'),
			(2, 'user123', 'member'),
			(3, 'user789', 'creator')
	`)
	if err != nil {
		panic(err)
	}

	// Create sessions table for authentication
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS sessions (
			id TEXT PRIMARY KEY,
			user_id TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			expires_at TIMESTAMP NOT NULL,
			FOREIGN KEY (user_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test sessions
	_, err = mockDB.Exec(`
		INSERT INTO sessions (id, user_id, created_at, expires_at)
		VALUES
			('mock_session_token', 'user123', '2024-01-01 10:00:00', '2024-12-31 23:59:59'),
			('mock_session_token_456', 'user456', '2024-01-01 11:00:00', '2024-12-31 23:59:59'),
			('mock_session_token_789', 'user789', '2024-01-01 12:00:00', '2024-12-31 23:59:59')
	`)
	if err != nil {
		panic(err)
	}
}

func TestGetGroupPreviewHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		groupID        string
		expectedStatus int
	}{
		{"Valid GET request", "GET", "1", http.StatusOK},
		{"Invalid method POST", "POST", "1", http.StatusMethodNotAllowed},
		{"Missing group ID", "GET", "", http.StatusBadRequest},
		{"Invalid group ID", "GET", "invalid", http.StatusBadRequest},
		{"Non-existent group", "GET", "99999", http.StatusNotFound},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, "/api/groups/preview?group_id="+tt.groupID, nil)
			if err != nil {
				t.Fatal(err)
			}

			// Add valid session for authenticated requests
			req.AddCookie(&http.Cookie{
				Name:  "session_id",
				Value: "mock_session_token",
			})

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(GetGroupPreviewHandler)
			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != tt.expectedStatus {
				t.Errorf("Expected status %v, got %v", tt.expectedStatus, status)
			}

			// For successful requests, validate JSON structure
			if rr.Code == http.StatusOK {
				var preview m.GroupPreview
				if err := json.Unmarshal(rr.Body.Bytes(), &preview); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if preview.ID == 0 {
					t.Error("Expected group ID to be set")
				}
			}
		})
	}
}

func TestGroupPreviewStructure(t *testing.T) {
	preview := m.GroupPreview{
		ID:           1,
		Name:         "Test Group",
		Description:  "A test group",
		MemberCount:  5,
		IsPrivate:    false,
		CreatorName:  "John Doe",
		IsUserMember: true,
	}

	// Test JSON marshaling
	data, err := json.Marshal(preview)
	if err != nil {
		t.Errorf("Failed to marshal GroupPreview: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled m.GroupPreview
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Errorf("Failed to unmarshal GroupPreview: %v", err)
	}

	// Verify data integrity
	if unmarshaled.ID != preview.ID {
		t.Errorf("ID mismatch: got %d, want %d", unmarshaled.ID, preview.ID)
	}
	if unmarshaled.Name != preview.Name {
		t.Errorf("Name mismatch: got %s, want %s", unmarshaled.Name, preview.Name)
	}
	if unmarshaled.IsUserMember != preview.IsUserMember {
		t.Errorf("IsUserMember mismatch: got %t, want %t", unmarshaled.IsUserMember, preview.IsUserMember)
	}
}

// TestGroupPreviewValidation tests input validation
func TestGroupPreviewValidation(t *testing.T) {
	// Test validateGroupID function directly
	req, _ := http.NewRequest("GET", "/api/groups/preview?group_id=1", nil)
	groupID, err := validateGroupID(req)
	if err != nil {
		t.Errorf("Expected valid group ID, got error: %v", err)
	}
	if groupID != 1 {
		t.Errorf("Expected group ID 1, got %d", groupID)
	}

	// Test invalid group ID
	req2, _ := http.NewRequest("GET", "/api/groups/preview?group_id=invalid", nil)
	_, err2 := validateGroupID(req2)
	if err2 == nil {
		t.Error("Expected error for invalid group ID")
	}
}

// TestGroupPreviewUtilities tests utility functions
func TestGroupPreviewUtilities(t *testing.T) {
	// Test checkUserMembership function
	role := checkUserMembership(1, "user123")
	if role != "creator" {
		t.Errorf("Expected creator role, got %s", role)
	}

	// Test isUserMemberOfGroup function
	isMember := isUserMemberOfGroup(1, "user123")
	if !isMember {
		t.Error("Expected user to be member of group")
	}
}
