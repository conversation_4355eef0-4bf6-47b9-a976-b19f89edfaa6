package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	m "imson/pkg/models"
	sq "imson/pkg/db/sqlite"
	"imson/pkg/services"
)

var notificationService *services.NotificationService

// InitNotificationService initializes the notification service after database is ready
func InitNotificationService() {
	notificationService = services.NewNotificationService()
}

// GetNotificationsHandler retrieves notifications for the current user
func GetNotificationsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse query parameters
	limitStr := r.URL.Query().Get("limit")
	offsetStr := r.URL.Query().Get("offset")

	limit := 20 // default
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	offset := 0 // default
	if offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	notifications, err := notificationService.GetUserNotifications(userID, limit, offset)
	if err != nil {
		log.Printf("Error getting notifications: %v", err)
		http.Error(w, "Failed to get notifications", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(notifications)
}

// GetUnreadNotificationCountHandler gets the count of unread notifications
func GetUnreadNotificationCountHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	count, err := notificationService.GetUnreadNotificationCount(userID)
	if err != nil {
		log.Printf("Error getting unread notification count: %v", err)
		http.Error(w, "Failed to get notification count", http.StatusInternalServerError)
		return
	}

	response := map[string]int{"unread_count": count}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// MarkNotificationsReadHandler marks notifications as read
func MarkNotificationsReadHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var req m.MarkNotificationsReadRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	if len(req.NotificationIDs) == 0 {
		// Mark all notifications as read
		err = notificationService.MarkAllNotificationsAsRead(userID)
	} else {
		// Mark specific notifications as read
		err = notificationService.MarkNotificationsAsRead(userID, req.NotificationIDs)
	}

	if err != nil {
		log.Printf("Error marking notifications as read: %v", err)
		http.Error(w, "Failed to mark notifications as read", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}

// DeleteNotificationHandler deletes a notification
func DeleteNotificationHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	notificationIDStr := r.URL.Query().Get("id")
	if notificationIDStr == "" {
		http.Error(w, "Notification ID is required", http.StatusBadRequest)
		return
	}

	notificationID, err := strconv.Atoi(notificationIDStr)
	if err != nil {
		http.Error(w, "Invalid notification ID", http.StatusBadRequest)
		return
	}

	err = notificationService.DeleteNotification(userID, notificationID)
	if err != nil {
		log.Printf("Error deleting notification: %v", err)
		http.Error(w, "Failed to delete notification", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}

// BroadcastNotificationToUser sends a real-time notification via WebSocket
func BroadcastNotificationToUser(userID string, notification *m.Notification) {
	clientsMutex.RLock()
	client, exists := clients[userID]
	clientsMutex.RUnlock()

	if exists {
		wsMessage := map[string]interface{}{
			"type": "notification",
			"data": notification,
		}

		notificationJSON, err := json.Marshal(wsMessage)
		if err != nil {
			log.Printf("Error marshaling notification: %v", err)
			return
		}

		select {
		case client.SendCh <- notificationJSON:
			// Notification sent successfully
		default:
			// Client send channel is full, skip notification
			log.Printf("Client send channel full for user %s", userID)
		}
	}
}

// Helper function to create and broadcast a notification
func CreateAndBroadcastNotification(userID, notificationType, title, message string, data interface{}) {
	notification, err := notificationService.CreateNotification(userID, notificationType, title, message, data)
	if err != nil {
		log.Printf("Error creating notification: %v", err)
		return
	}

	// Broadcast the notification in real-time
	BroadcastNotificationToUser(userID, notification)
}