package handlers

import (
	"bytes"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
)

func TestCreateGroup(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/create"

	w := httptest.NewRecorder()
	jsonBody := []byte(`{
	"name": "Astrophysics",
	"admin": "<PERSON>",
	"about": "This group is for Science and Cosmology nerds and geeks who are devastatingly explorers of the unknown",
	"topics": "science,astronomy,cosmology"
}`)

	db := setupTestDB(t)
	userID := "test-user-123"
	nickname := "<PERSON>"
	_, err := db.Exec(`
	INSERT INTO users (id, nickname, first_name, last_name, gender, email, password)
	VALUES (?, ?, ?, ?, ?, ?, ?)`,
		userID, nickname, "<PERSON>", "<PERSON>", "male", "<EMAIL>", "hashed-password")
	if err != nil {
		t.Fatalf("failed to insert test user: %v", err)
	}

	sessionID := "test-session-id"
	_, err = db.Exec(`INSERT INTO sessions (id, user_id, created_at, expires_at) VALUES (?, ?, ?, ?)`, sessionID, userID, "0001-01-01 00:00:00 +0000 UTC", "0001-01-01 00:00:00 +0000 UTC")
	if err != nil {
		t.Fatalf("failed to insert test session: %v", err)
	}
	r := httptest.NewRequest(method, endpoint, bytes.NewReader(jsonBody))
	r.Header.Set("Content-Type", "application/json")

	r.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    "test-session-id",
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	CreateGroup(w, r, db)
	defer w.Result().Body.Close()

	if w.Result().StatusCode != http.StatusOK {
		t.Fatalf("want: %03d, got: %03d", http.StatusOK, w.Result().StatusCode)
	}

	b, err := io.ReadAll(w.Result().Body)
	if err != nil {
		t.Fatalf("want: nil, got: %+q", err)
	}

	want := `{"group_id":1,"name":"Astrophysics","creator_id":"test-user-123","members":1,"admin":"Raymond","about":"This group is for Science and Cosmology nerds and geeks who are devastatingly explorers of the unknown","Members":null,"topics":["science","astronomy","cosmology"],"created_at":"0001-01-01T00:00:00Z","updated_at":"0001-01-01T00:00:00Z"}
`
	got := string(b)

	if want != got {
		t.Fatalf("want: %+q, got: %+q", want, got)
	}
	t.Log("response:", string(b))
}

func TestCreateGroup_Unauthorized(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/create"

	w := httptest.NewRecorder()
	body := []byte(`{
		"name": "Test Group",
		"admin": "Raymond",
		"about": "Testing unauthorized access",
		"topics": "golang,testing"
	}`)

	// 🔸 No session cookie added here
	r := httptest.NewRequest(method, endpoint, bytes.NewReader(body))
	r.Header.Set("Content-Type", "application/json")

	// Setup test DB (assuming you have a function for this)
	db := setupTestDB(t)

	// Act
	CreateGroup(w, r, db)
	res := w.Result()
	defer res.Body.Close()

	// Assert
	if res.StatusCode != http.StatusUnauthorized {
		t.Fatalf("expected 401 Unauthorized, got %d", res.StatusCode)
	}

	respBody, _ := io.ReadAll(res.Body)
	if !strings.Contains(string(respBody), "Unauthorized") {
		t.Fatalf("expected response to contain 'Unauthorized', got: %s", string(respBody))
	}
}

func TestCreateGroup_MessageIncomplete(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/create"

	w := httptest.NewRecorder()

	// Missing "about" and "topics" fields (intentionally)
	body := []byte(`{
		"name": "Astrophysics"
	}`)

	req := httptest.NewRequest(method, endpoint, bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")

	// Add a valid session cookie (so it passes the GetCurrentUserID check)
	req.AddCookie(&http.Cookie{
		Name:  "session",
		Value: "valid-session-id", // must exist in test DB if used
	})

	// Optional: Insert session into test DB if GetCurrentUserID checks it
	// INSERT INTO sessions (id, user_id) VALUES ("valid-session-id", "some-user-id")

	db := setupTestDB(t)
	CreateGroup(w, req, db)

	resp := w.Result()
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusBadRequest {
		t.Fatalf("expected 400 Bad Request, got %d", resp.StatusCode)
	}

	respBody, _ := io.ReadAll(resp.Body)
	if !strings.Contains(string(respBody), "Message incomplete") {
		t.Fatalf("expected 'Message incomplete' in response body, got: %s", string(respBody))
	}
}

func TestCreateGroup_InvalidRequestBody(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/create"
	w := httptest.NewRecorder()

	// 🚨 Malformed JSON (missing a closing brace)
	badJSON := []byte(`{
		"name": "Invalid JSON"
	`) // ← malformed

	r := httptest.NewRequest(method, endpoint, bytes.NewReader(badJSON))
	r.Header.Set("Content-Type", "application/json")

	// Optional: Add session cookie if GetCurrentUserID is used before this check
	// But in your handler, this Decode happens before session check, so it's not needed

	db := setupTestDB(t)
	CreateGroup(w, r, db)

	resp := w.Result()
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusBadRequest {
		t.Fatalf("expected 400 Bad Request, got %d", resp.StatusCode)
	}

	body, _ := io.ReadAll(resp.Body)
	if !strings.Contains(string(body), "Invalid request body") {
		t.Fatalf("expected error message 'Invalid request body', got: %s", string(body))
	}
}

func TestCreateGroup_MethodNotAllowed(t *testing.T) {
	// Use an invalid method like GET
	method := http.MethodGet
	endpoint := "/api/groups/create"
	w := httptest.NewRecorder()

	// Even though the body might be valid, the method is wrong
	body := []byte(`{
		"name": "Test Group",
		"admin": "Raymond",
		"about": "Group for testing",
		"topics": "golang,testing"
	}`)

	r := httptest.NewRequest(method, endpoint, bytes.NewReader(body))
	r.Header.Set("Content-Type", "application/json")

	db := setupTestDB(t)
	CreateGroup(w, r, db)

	resp := w.Result()
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusMethodNotAllowed {
		t.Fatalf("expected 405 Method Not Allowed, got %d", resp.StatusCode)
	}

	respBody, _ := io.ReadAll(resp.Body)
	if !strings.Contains(string(respBody), "Method not allowed") {
		t.Fatalf("expected 'Method not allowed' in response body, got: %s", string(respBody))
	}
}

func TestCreateGroup_DatabaseError(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/create"
	w := httptest.NewRecorder()

	// Valid JSON payload
	jsonBody := []byte(`{
		"name": "Astrophysics",
		"admin": "Unknown",
		"about": "This group is for Science and Cosmology nerds and geeks who are devastatingly explorers of the unknown",
		"topics": "science,astronomy,cosmology"
	}`)

	db := setupTestDB(t)

	// ⚠️ Do NOT insert the user with userID = "test-user-123"
	userID := "test-user-123"
	sessionID := "test-session-id"

	// But we DO insert a session pointing to a non-existent user
	_, err := db.Exec(`
		INSERT INTO sessions (id, user_id, created_at, expires_at)
		VALUES (?, ?, ?, ?)`,
		sessionID, userID, time.Now(), time.Now().Add(time.Hour),
	)
	if err != nil {
		t.Fatalf("failed to insert session: %v", err)
	}

	// Build the request
	r := httptest.NewRequest(method, endpoint, bytes.NewReader(jsonBody))
	r.Header.Set("Content-Type", "application/json")
	r.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	// Call the handler
	CreateGroup(w, r, db)
	res := w.Result()
	defer res.Body.Close()

	// ✅ Expect 500 error because SELECT nickname will fail
	if res.StatusCode != http.StatusInternalServerError {
		body, _ := io.ReadAll(res.Body)
		t.Fatalf("expected 500 error, got %d. Response body: %s", res.StatusCode, string(body))
	}
}
