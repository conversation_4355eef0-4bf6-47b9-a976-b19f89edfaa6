package handlers

import (
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

var PostID_posting int

// / POST HANDLER
// This function handler fetches posts from the databasae filtered by a a specific catogory and martialed into a json and swnt to the frontend
func GetPostsHandler(w http.ResponseWriter, r *http.Request) {
	// Handle CORS preflight
	if r.Method == http.MethodOptions {
		return
	}

	cookieValue, ok := ValidateSession(r)
	var session m.Session
	err := sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookieValue).
		Scan(&session.ID, &session.UserID, &session.CreatedAt, &session.ExpiresAt)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	if ok {
		if r.Method == http.MethodGet {
			category := r.URL.Query().Get("category")

			posts, err := FetchPostsFrmDb(category)
			if err != nil {
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
				return
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(posts)
		} else if r.Method == http.MethodPost {
			// Parse multipart form for file uploads
			err := r.ParseMultipartForm(10 << 20) // 10 MB max
			if err != nil {
				http.Error(w, "Invalid form data", http.StatusBadRequest)
				return
			}

			content := r.FormValue("content")
			visibility := r.FormValue("visibility")
			if visibility == "" {
				visibility = "public"
			}

			if content == "" {
				http.Error(w, "Content is required", http.StatusBadRequest)
				return
			}

			// Handle image upload if present
			var imageURL string
			file, header, err := r.FormFile("image")
			if err == nil {
				defer file.Close()

				// Create directory for post images
				imageDir := "static/posts"
				if err := os.MkdirAll(imageDir, 0o755); err != nil {
					log.Printf("Error creating directory: %v", err)
				} else {
					// Generate unique filename
					ext := filepath.Ext(header.Filename)
					filename := fmt.Sprintf("%s_post_%d%s", session.UserID, time.Now().Unix(), ext)
					imagePath := filepath.Join(imageDir, filename)

					// Save the file
					dst, err := os.Create(imagePath)
					if err == nil {
						defer dst.Close()
						if _, err := io.Copy(dst, file); err == nil {
							// Construct proper URL for static file serving
							imageURL = "/static/posts/" + filename
						}
					}
				}
			}

			result, err := sq.GetDB().Exec(`
				INSERT INTO posts (user_id, content, image, visibility, created_at)
				VALUES (?, ?, ?, ?, ?)`,
				session.UserID, content, imageURL, visibility, time.Now())
			if err != nil {
				log.Printf("Error creating post: %v", err)
				http.Error(w, "Error creating post", http.StatusInternalServerError)
				return
			}

			postID, err := result.LastInsertId()
			PostID_posting = int(postID)
			if err != nil {
				log.Printf("Error getting post ID: %v", err)
				http.Error(w, "Error creating post", http.StatusInternalServerError)
				return
			}

			post, _ := FetchLatestPostFromDb(int(postID))

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(post)
		} else {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

// The function fetches a particular group of posts based on a specific category from the database
func FetchPostsFrmDb(category string) ([]m.Post, error) {
	query := `
	SELECT p.id, p.content, COALESCE(p.image, '') as image, p.visibility, p.created_at, p.user_id,
		   u.nickname as author_nickname, u.first_name as author_first_name, u.last_name as author_last_name,
		   COALESCE(u.avatar_url, '') as author_avatar
	FROM posts p
	JOIN users u ON p.user_id = u.id
`
	// For now, we'll ignore category filtering since posts table doesn't have category column
	query += " ORDER BY p.created_at DESC"

	rows, err := sq.GetDB().Query(query)
	if err != nil {
		log.Printf("Error querying posts: %v", err)
		return []m.Post{}, nil
	}
	defer rows.Close()

	var posts []m.Post
	for rows.Next() {
		var post m.Post
		err := rows.Scan(
			&post.ID,
			&post.Content,
			&post.Image,
			&post.Visibility,
			&post.CreatedAt,
			&post.UserID,
			&post.AuthorNickname,
			&post.AuthorFirstName,
			&post.AuthorLastName,
			&post.AuthorAvatar,
		)
		if err != nil {
			log.Printf("Error scanning post: %v", err)
			continue
		}
		posts = append(posts, post)
	}
	return posts, nil
}

// The function fetches a post from the database based on the particular post id. It is then martialled into a json object and sent to the  frontend
func FetchLatestPostFromDb(postId int) (m.Post, error) {
	var post m.Post
	err := sq.GetDB().QueryRow(`
		SELECT p.id, p.content, COALESCE(p.image, '') as image, p.visibility, p.created_at, p.user_id,
			   u.nickname as author_nickname, u.first_name as author_first_name, u.last_name as author_last_name,
			   COALESCE(u.avatar_url, '') as author_avatar
		FROM posts p
		JOIN users u ON p.user_id = u.id
		WHERE p.id = ?`,
		postId).
		Scan(&post.ID, &post.Content, &post.Image, &post.Visibility, &post.CreatedAt, &post.UserID,
			&post.AuthorNickname, &post.AuthorFirstName, &post.AuthorLastName, &post.AuthorAvatar)
	if err != nil {
		log.Printf("Error retrieving created post: %v", err)
		return m.Post{}, err
	}

	return post, nil
}

// GetUserPostsFromDb fetches all posts for a specific user
func GetUserPostsFromDb(userID string) ([]m.Post, error) {
	query := `
		SELECT p.id, p.content, COALESCE(p.image, '') as image, p.visibility, p.created_at, p.user_id,
			   u.nickname as author_nickname, u.first_name as author_first_name, u.last_name as author_last_name,
			   COALESCE(u.avatar_url, '') as author_avatar
		FROM posts p
		JOIN users u ON p.user_id = u.id
		WHERE p.user_id = ?
		ORDER BY p.created_at DESC`

	rows, err := sq.GetDB().Query(query, userID)
	if err != nil {
		log.Printf("Error querying user posts: %v", err)
		return []m.Post{}, err
	}
	defer rows.Close()

	var posts []m.Post
	for rows.Next() {
		var post m.Post
		err := rows.Scan(
			&post.ID,
			&post.Content,
			&post.Image,
			&post.Visibility,
			&post.CreatedAt,
			&post.UserID,
			&post.AuthorNickname,
			&post.AuthorFirstName,
			&post.AuthorLastName,
			&post.AuthorAvatar,
		)
		if err != nil {
			log.Printf("Error scanning post: %v", err)
			continue
		}
		posts = append(posts, post)
	}
	return posts, nil
}

// The function handles liking a post and registering in the database
func LikePostHandler(w http.ResponseWriter, r *http.Request, postIdStr string) {
	cookieValue, ok := ValidateSession(r)
	postID, err := strconv.Atoi(postIdStr)
	if err != nil {
		http.Error(w, "Invalid post ID", http.StatusBadRequest)
		return
	}
	var (
		userId      string
		likePostObj m.LikePostObject
	)
	likePostObj.PostID = postID
	if ok {

		err = sq.GetDB().QueryRow(`
		SELECT user_id 
		FROM sessions 
		WHERE id = ?`, cookieValue).Scan(&userId)
		if err != nil {
			if err == sql.ErrNoRows {
				http.Error(w, "User not found", http.StatusNotFound)
				fmt.Println("User not found for userID:", cookieValue)
			} else {
				http.Error(w, "Failed to fetch user id", http.StatusInternalServerError)
				fmt.Println("Error querying user id:", err)
			}
			return
		}
		found, _ := checkUserLikedPost(postID, userId)
		if found {
			// Unlike the post - no notification needed
			err = removeRowFromTable("posts_likes", postID, userId)
			likePostObj.Likes, _ = GetPostLikes(postID)
			if err != nil {
				http.Error(w, "Failed to unlike post", http.StatusInternalServerError)
				return
			}
		} else {
			// Like the post
			err := incrementPostLikes(postID, userId)
			likePostObj.Likes, _ = GetPostLikes(postID)
			if err != nil {
				http.Error(w, "Failed to retrieve like count", http.StatusInternalServerError)
				return
			}

			// Get post author ID for notification
			var postAuthorID string
			err = sq.GetDB().QueryRow("SELECT user_id FROM posts WHERE id = ?", postID).Scan(&postAuthorID)
			if err != nil {
				log.Printf("Error getting post author: %v", err)
			} else if postAuthorID != userId {
				// insert function call to create notification for post author (if not liking own post)
			}
		}
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(likePostObj)
}

// function to check for the number of likes a post has
func GetPostLikes(postID int) (int, error) {
	var likes int
	err := sq.GetDB().QueryRow("SELECT likes FROM posts WHERE id = ?", postID).Scan(&likes)
	if err != nil {
		return 0, err
	}
	return likes, nil
}

// function to check whether the user had liked a post
func checkUserLikedPost(postID int, userID string) (bool, error) {
	query := `SELECT COUNT(*) FROM posts_likes WHERE post_id = ? AND user_id = ?`
	var count int

	err := sq.GetDB().QueryRow(query, postID, userID).Scan(&count)
	if err != nil {
		return false, err
	}

	if count > 0 {
		return true, nil
	}
	return false, nil
}

// The function  removes a like by deleting a particular row from the databse
func removeRowFromTable(tableName string, postID int, userID string) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE post_id = ? AND user_id = ?", tableName)

	_, err := sq.GetDB().Exec(query, postID, userID)
	if err != nil {
		return fmt.Errorf("failed to remove row from table %s: %v", tableName, err)
	}

	return nil
}

// The funciton increaments the count of the likes of a post in the database
func incrementPostLikes(postID int, userID string) error {
	tx, err := sq.GetDB().Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	query := `UPDATE posts SET likes = likes + 1 WHERE id = ?`
	_, err = tx.Exec(query, postID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to increment likes: %v", err)
	}

	query = `INSERT INTO posts_likes (post_id, user_id) VALUES (?, ?)`
	_, err = tx.Exec(query, postID, userID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add row to posts_likes table: %v", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}
	return nil
}

// GetPostAnalyticsHandler returns analytics data for user's posts
func GetPostAnalyticsHandler(w http.ResponseWriter, r *http.Request) {
	// Handle CORS preflight
	if r.Method == http.MethodOptions {
		return
	}

	cookieValue, ok := ValidateSession(r)
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var session m.Session
	err := sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookieValue).
		Scan(&session.ID, &session.UserID, &session.CreatedAt, &session.ExpiresAt)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get analytics data
	analytics := struct {
		TotalPosts    int `json:"total_posts"`
		TotalLikes    int `json:"total_likes"`
		TotalComments int `json:"total_comments"`
		TotalViews    int `json:"total_views"`
		PostsByMonth  []struct {
			Month string `json:"month"`
			Count int    `json:"count"`
		} `json:"posts_by_month"`
		TopPosts []struct {
			ID      int    `json:"id"`
			Content string `json:"content"`
			Likes   int    `json:"likes"`
			Views   int    `json:"views"`
		} `json:"top_posts"`
	}{}

	// Get total posts
	err = sq.GetDB().QueryRow("SELECT COUNT(*) FROM posts WHERE user_id = ?", session.UserID).Scan(&analytics.TotalPosts)
	if err != nil {
		log.Printf("Error getting total posts: %v", err)
	}

	// Get total likes (placeholder - would need likes table)
	analytics.TotalLikes = 0

	// Get total comments (placeholder - would need comments table)
	analytics.TotalComments = 0

	// Get total views (placeholder - would need views tracking)
	analytics.TotalViews = 0

	// Get posts by month
	rows, err := sq.GetDB().Query(`
		SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
		FROM posts
		WHERE user_id = ?
		GROUP BY strftime('%Y-%m', created_at)
		ORDER BY month DESC
		LIMIT 12`, session.UserID)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var month string
			var count int
			if err := rows.Scan(&month, &count); err == nil {
				analytics.PostsByMonth = append(analytics.PostsByMonth, struct {
					Month string `json:"month"`
					Count int    `json:"count"`
				}{Month: month, Count: count})
			}
		}
	}

	// Get top posts
	topRows, err := sq.GetDB().Query(`
		SELECT id, content, 0 as likes, 0 as views
		FROM posts
		WHERE user_id = ?
		ORDER BY created_at DESC
		LIMIT 5`, session.UserID)
	if err == nil {
		defer topRows.Close()
		for topRows.Next() {
			var post struct {
				ID      int    `json:"id"`
				Content string `json:"content"`
				Likes   int    `json:"likes"`
				Views   int    `json:"views"`
			}
			if err := topRows.Scan(&post.ID, &post.Content, &post.Likes, &post.Views); err == nil {
				analytics.TopPosts = append(analytics.TopPosts, post)
			}
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(analytics)
}

// ExportPostsHandler exports user's posts as CSV
func ExportPostsHandler(w http.ResponseWriter, r *http.Request) {
	// Handle CORS preflight
	if r.Method == http.MethodOptions {
		return
	}

	cookieValue, ok := ValidateSession(r)
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var session m.Session
	err := sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookieValue).
		Scan(&session.ID, &session.UserID, &session.CreatedAt, &session.ExpiresAt)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user's posts
	posts, err := GetUserPostsFromDb(session.UserID)
	if err != nil {
		http.Error(w, "Error fetching posts", http.StatusInternalServerError)
		return
	}

	// Set headers for CSV download
	w.Header().Set("Content-Type", "text/csv")
	w.Header().Set("Content-Disposition", "attachment; filename=my_posts.csv")

	// Create CSV writer
	writer := csv.NewWriter(w)
	defer writer.Flush()

	// Write CSV header
	header := []string{"ID", "Content", "Image", "Visibility", "Created At", "Likes", "Comments"}
	if err := writer.Write(header); err != nil {
		log.Printf("Error writing CSV header: %v", err)
		return
	}

	// Write posts data
	for _, post := range posts {
		record := []string{
			strconv.Itoa(post.ID),
			post.Content,
			post.Image,
			post.Visibility,
			post.CreatedAt.Format("2006-01-02 15:04:05"),
			strconv.Itoa(post.Likes),
			strconv.Itoa(post.CommentCount),
		}
		if err := writer.Write(record); err != nil {
			log.Printf("Error writing CSV record: %v", err)
			return
		}
	}
}
