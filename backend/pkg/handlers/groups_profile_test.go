package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	db "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

func init() {
	// Setup mock database
	mockDB, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	db.DBConn = mockDB

	// Create users table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			nickname TEXT NOT NULL,
			first_name TEXT NOT NULL,
			last_name TEXT NOT NULL,
			email TEXT UNIQUE NOT NULL,
			avatar_url TEXT
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create groups table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS groups (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_private BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIG<PERSON> KEY (creator_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create group_members table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS group_members (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			role TEXT DEFAULT 'member',
			joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			nickname TEXT,
			first_name TEXT,
			last_name TEXT,
			avatar_url TEXT,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (user_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create events table
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS events (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			creator_id TEXT NOT NULL,
			title TEXT NOT NULL,
			description TEXT,
			event_date TIMESTAMP NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (group_id) REFERENCES groups (id),
			FOREIGN KEY (creator_id) REFERENCES users (id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test users
	_, err = mockDB.Exec(`
		INSERT INTO users (id, nickname, first_name, last_name, email, avatar_url)
		VALUES
			('user123', 'john_doe', 'John', 'Doe', '<EMAIL>', 'avatar1.jpg'),
			('user456', 'jane_doe', 'Jane', 'Doe', '<EMAIL>', 'avatar2.jpg'),
			('user789', 'bob_smith', 'Bob', 'Smith', '<EMAIL>', 'avatar3.jpg'),
			('user999', 'alice_wonder', 'Alice', 'Wonder', '<EMAIL>', NULL)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test groups
	_, err = mockDB.Exec(`
		INSERT INTO groups (id, name, description, creator_id, is_private, created_at)
		VALUES
			(1, 'Tech Enthusiasts', 'A group for technology lovers to discuss latest trends', 'user123', FALSE, '2024-01-01 10:00:00'),
			(2, 'Private Club', 'Exclusive private group for VIP members', 'user456', TRUE, '2024-01-02 11:00:00'),
			(3, 'Gaming Community', 'For gamers to connect and play together', 'user789', FALSE, '2024-01-03 12:00:00')
	`)
	if err != nil {
		panic(err)
	}

	// Insert group members with full details
	_, err = mockDB.Exec(`
		INSERT INTO group_members (group_id, user_id, role, nickname, first_name, last_name, avatar_url, joined_at)
		VALUES
			(1, 'user123', 'creator', 'john_doe', 'John', 'Doe', 'avatar1.jpg', '2024-01-01 10:00:00'),
			(1, 'user456', 'admin', 'jane_doe', 'Jane', 'Doe', 'avatar2.jpg', '2024-01-01 11:00:00'),
			(1, 'user789', 'member', 'bob_smith', 'Bob', 'Smith', 'avatar3.jpg', '2024-01-01 12:00:00'),
			(2, 'user456', 'creator', 'jane_doe', 'Jane', 'Doe', 'avatar2.jpg', '2024-01-02 11:00:00'),
			(2, 'user123', 'member', 'john_doe', 'John', 'Doe', 'avatar1.jpg', '2024-01-02 12:00:00'),
			(3, 'user789', 'creator', 'bob_smith', 'Bob', 'Smith', 'avatar3.jpg', '2024-01-03 12:00:00')
	`)
	if err != nil {
		panic(err)
	}

	// Insert test events
	_, err = mockDB.Exec(`
		INSERT INTO events (id, group_id, creator_id, title, description, event_date, created_at)
		VALUES
			(1, 1, 'user123', 'Tech Meetup 2024', 'Annual technology meetup', '2024-06-15 18:00:00', '2024-01-15 10:00:00'),
			(2, 1, 'user456', 'Workshop: AI Basics', 'Introduction to AI concepts', '2024-07-20 14:00:00', '2024-01-20 09:00:00'),
			(3, 2, 'user456', 'VIP Dinner', 'Exclusive dinner for VIP members', '2024-08-10 19:00:00', '2024-01-25 15:00:00')
	`)
	if err != nil {
		panic(err)
	}
}

func TestGetGroupProfileHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		groupID        string
		expectedStatus int
	}{
		{"Valid GET request", "GET", "1", http.StatusOK},
		{"Invalid method POST", "POST", "1", http.StatusMethodNotAllowed},
		{"Missing group ID", "GET", "", http.StatusBadRequest},
		{"Invalid group ID", "GET", "invalid", http.StatusBadRequest},
		{"Non-existent group", "GET", "99999", http.StatusNotFound},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, "/api/groups/profile?group_id="+tt.groupID, nil)
			if err != nil {
				t.Fatal(err)
			}

			// Add mock session cookie for authentication
			req.AddCookie(&http.Cookie{
				Name:  "session_id",
				Value: "mock_session_token",
			})

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(GetGroupProfileHandler)
			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != tt.expectedStatus {
				t.Errorf("Expected status %v, got %v", tt.expectedStatus, status)
			}

			// For successful requests, validate JSON structure
			if rr.Code == http.StatusOK {
				var details m.GroupDetails
				if err := json.Unmarshal(rr.Body.Bytes(), &details); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if details.ID == 0 {
					t.Error("Expected group ID to be set")
				}
			}
		})
	}
}

func TestGroupDetailsStructure(t *testing.T) {
	details := m.GroupDetails{
		Group: m.Group{
			ID:          1,
			Name:        "Test Group",
			About:       "A test group",
			CreatorID:   "user123",
			IsPrivate:   false,
			Cardinality: 5,
		},
		CreatorName:   "John Doe",
		CreatorAvatar: "avatar.jpg",
		Members:       []m.GroupMember{},
		RecentEvents:  []m.Event{},
		IsUserMember:  true,
		UserRole:      "member",
	}

	// Test JSON marshaling
	data, err := json.Marshal(details)
	if err != nil {
		t.Errorf("Failed to marshal GroupDetails: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled m.GroupDetails
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Errorf("Failed to unmarshal GroupDetails: %v", err)
	}

	// Verify data integrity
	if unmarshaled.ID != details.ID {
		t.Errorf("ID mismatch: got %d, want %d", unmarshaled.ID, details.ID)
	}
	if unmarshaled.CreatorName != details.CreatorName {
		t.Errorf("CreatorName mismatch: got %s, want %s", unmarshaled.CreatorName, details.CreatorName)
	}
	if unmarshaled.IsUserMember != details.IsUserMember {
		t.Errorf("IsUserMember mismatch: got %t, want %t", unmarshaled.IsUserMember, details.IsUserMember)
	}
}
