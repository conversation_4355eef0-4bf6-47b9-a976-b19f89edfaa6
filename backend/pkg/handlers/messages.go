package handlers

import (
	"encoding/json"
	"fmt"
	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	"log"
	"net/http"
	"strconv"
	"time"
)

var DB = sq.GetDB()

// FetchMessagesHandler retrieves messages between the logged-in user and the specified user
func FetchMessagesHandler(w http.ResponseWriter, r *http.Request, userId string) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	query := r.URL.Query()
	offsetStr := query.Get("offset")
	limitStr := query.Get("limit")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	messages, err := FetchMessages(r, userId, offset, limit)
	if err != nil {
		http.Error(w, "Failed to fetch messages", http.StatusInternalServerError)
		return
	}

	w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(messages)
}

// FetchMessages retrieves paginated messages between the logged-in user and the specified user
func FetchMessages(r *http.Request, userId string, offset, limit int) ([]m.Message, error) {
	currentUserID, err := GetCurrentUserID(r, DB)
	if err != nil {
		return nil, err
	}

	// Query only the required slice of messages directly from the database
	rows, err := DB.Query(`
		SELECT id, sender_id, receiver_id, content, created_at, read 
		FROM messages 
		WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
		ORDER BY created_at DESC`,
		currentUserID, userId, userId, currentUserID,
	)
	if err != nil {
		fmt.Println("Error fetching messages:", err)
		return nil, err
	}
	defer rows.Close()

	var messages []m.Message
	for rows.Next() {
		var message m.Message
		if err := rows.Scan(&message.ID, &message.SenderID, &message.ReceiverID, &message.Content, &message.Timestamp, &message.ReadStatus); err != nil {
			fmt.Println("Error scanning message:", err)
			return nil, err
		}
		message.CurrentUserId = currentUserID
		messages = append(messages, message)
	}

	return finalMessages(messages, limit, offset), nil
}

// this function will be responsible to do the calculation of the returning messages
func finalMessages(msgs []m.Message, limit, offset int) []m.Message {
	var fnlMsgs []m.Message
	limit += offset
	if limit > len(msgs) {
		limit = len(msgs)
	}
	if offset >= limit || offset > len(msgs) {
		return nil
	}
	fnlMsgs = msgs[offset:limit]
	return fnlMsgs
}

var Sender_id, Receiver_id string

// this function is responsible for handling a newly created message from the frontend to the backend for updating the database
func SendMessageHandler(w http.ResponseWriter, r *http.Request, recipientID string) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Content string `json:"content"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Content == "" {
		http.Error(w, "Message content cannot be empty", http.StatusBadRequest)
		return
	}

	senderID, err := GetCurrentUserID(r, DB)
	Sender_id = senderID
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// THROTTLE CHECK
	const maxMessages = 5
	const windowSeconds = 10

	now := time.Now()
	windowStart := now.Add(-time.Duration(windowSeconds) * time.Second)

	var count int
	err = DB.QueryRow(`
		SELECT COUNT(*) FROM message_throttle
		WHERE user_id = ? AND timestamp >= ?
	`, senderID, windowStart).Scan(&count)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	if count >= maxMessages {
		w.WriteHeader(http.StatusTooManyRequests)
		http.Error(w, "Rate limit exceeded. Please wait before sending more messages.", http.StatusTooManyRequests)
		return
	}

	result, err := DB.Exec(
		`INSERT INTO messages (sender_id, receiver_id, content, created_at) VALUES (?, ?, ?, ?)`,
		senderID, recipientID, req.Content, now,
	)
	Receiver_id = recipientID
	if err != nil {
		fmt.Printf("%v", err)
		http.Error(w, "Failed to store message", http.StatusInternalServerError)
		return
	}

	_, err = DB.Exec(`INSERT INTO message_throttle (user_id, timestamp) VALUES (?, ?)`, senderID, now)
	if err != nil {
		log.Printf("Throttle logging failed: %v", err)
	}

	messageID, err := result.LastInsertId()
	if err != nil {
		http.Error(w, "Failed to retrieve message ID", http.StatusInternalServerError)
		return
	}

	message := m.Message{
		ID:         int(messageID),
		SenderID:   senderID,
		ReceiverID: recipientID,
		Content:    req.Content,
		Timestamp:  now,
	}

	w.WriteHeader(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(message)
}

// LatestPrivateMessage fetches the most recent private message from the database.
func LatestPrivateMessage() (m.Message, error) {

	var msg m.Message
	row := DB.QueryRow(`
		SELECT id, sender_id, receiver_id, content, created_at
		FROM messages
		WHERE sender_id = ? AND receiver_id = ?
		ORDER BY created_at DESC
		LIMIT 1
	`, Sender_id, Receiver_id)

	err := row.Scan(&msg.ID, &msg.SenderID, &msg.ReceiverID, &msg.Content, &msg.Timestamp)
	if err != nil {
		return m.Message{}, err
	}
	msg.CurrentUserId = Sender_id
	return msg, nil
}

// read messages patches to the databse, the messages that have been read and updates their value of read to 1
func ReadMessagesHandler(w http.ResponseWriter, r *http.Request) {
	// if r.Method != http.MethodPatch {
	// 	http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
	// 	return
	// }
	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return
	}

	var payload struct {
		Subjects []string `json:"parties"`
	}

	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	stmt, err := DB.Prepare(`
	UPDATE messages
	SET read = 1
	WHERE ((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))`)

	if err != nil {
		log.Printf("Failed to prepare statement: %v", err)
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer stmt.Close()

	if _, err := stmt.Exec(payload.Subjects[0], payload.Subjects[1], payload.Subjects[1], payload.Subjects[0]); err != nil {
		log.Printf("Failed to update message as read for %s: %v", payload.Subjects[0], err)
		http.Error(w, "Failed to update messages", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"messages marked as read"}`))
}
