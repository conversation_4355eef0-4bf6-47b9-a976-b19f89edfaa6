package handlers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"strconv"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// GroupUtils contains common utility functions for group handlers
// This file extracts repeated code patterns to improve maintainability

// validateGroupID extracts and validates group_id from query parameters
func validateGroupID(r *http.Request) (int, error) {
	groupIDStr := r.URL.Query().Get("group_id")
	if groupIDStr == "" {
		return 0, fmt.Errorf("group ID is required")
	}

	groupID, err := strconv.Atoi(groupIDStr)
	if err != nil {
		return 0, fmt.Errorf("invalid group ID")
	}

	return groupID, nil
}

// handleGroupError provides consistent error handling for group operations
func handleGroupError(w http.ResponseWriter, err error, operation string) bool {
	if err == sql.ErrNoRows {
		http.Error(w, "Group not found", http.StatusNotFound)
		return true
	}
	if err != nil {
		log.Printf("Error %s: %v", operation, err)
		http.Error(w, fmt.Sprintf("Failed to %s", operation), http.StatusInternalServerError)
		return true
	}
	return false
}

// checkUserMembership returns user's role in group (empty string if not a member)
func checkUserMembership(groupID int, userID string) string {
	var role sql.NullString
	err := sq.GetDB().QueryRow(`
		SELECT role FROM group_members WHERE group_id = ? AND user_id = ?`,
		groupID, userID).Scan(&role)

	if err != nil && err != sql.ErrNoRows {
		log.Printf("Error checking user membership: %v", err)
	}

	if role.Valid {
		return role.String
	}
	return ""
}

// isUserMemberOfGroup checks if a user is a member of a group (returns boolean)
func isUserMemberOfGroup(groupID int, userID string) bool {
	var exists bool
	err := sq.GetDB().QueryRow(`
		SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
		groupID, userID).Scan(&exists)
	if err != nil {
		log.Printf("Error checking group membership: %v", err)
		return false
	}

	return exists
}

// checkGroupAccess verifies if user can access a group (handles private groups)
func checkGroupAccess(groupID int, userID string) (bool, bool, error) {
	var isPrivate bool
	err := sq.GetDB().QueryRow(`SELECT is_private FROM groups WHERE id = ?`, groupID).Scan(&isPrivate)
	if err != nil {
		return false, false, err
	}

	userRole := checkUserMembership(groupID, userID)
	isUserMember := userRole != ""

	// For private groups, only members can access
	canAccess := !isPrivate || isUserMember

	return canAccess, isUserMember, nil
}

// getGroupBasicInfo retrieves basic group information
func getGroupBasicInfo(groupID int) (*m.Group, error) {
	var group m.Group
	err := sq.GetDB().QueryRow(`
		SELECT g.id, g.name, g.description, g.creator_id, g.is_private, g.created_at,
			   COUNT(gm.user_id) as member_count
		FROM groups g
		LEFT JOIN group_members gm ON g.id = gm.group_id
		WHERE g.id = ?
		GROUP BY g.id`, groupID).Scan(
		&group.ID, &group.Name, &group.About, &group.CreatorID,
		&group.IsPrivate, &group.CreatedAt, &group.Cardinality)
	if err != nil {
		return nil, err
	}
	return &group, nil
}

// getCreatorName retrieves the creator's full name for a group
func getCreatorName(groupID int) string {
	var firstName, lastName sql.NullString
	err := sq.GetDB().QueryRow(`
		SELECT u.first_name, u.last_name
		FROM groups g
		JOIN users u ON g.creator_id = u.id
		WHERE g.id = ?`, groupID).Scan(&firstName, &lastName)
	if err != nil {
		log.Printf("Error getting creator name: %v", err)
		return ""
	}

	if firstName.Valid && lastName.Valid {
		return firstName.String + " " + lastName.String
	}
	return ""
}

// getGroupAdmins retrieves the list of group administrators
func getGroupAdmins(groupID int) []m.GroupMember {
	var admins []m.GroupMember

	rows, err := sq.GetDB().Query(`
		SELECT gm.id, gm.group_id, gm.user_id, gm.role, gm.joined_at,
			   u.nickname, u.first_name, u.last_name, COALESCE(u.avatar_url, '') as avatar_url
		FROM group_members gm
		JOIN users u ON gm.user_id = u.id
		WHERE gm.group_id = ? AND gm.role IN ('creator', 'admin')
		ORDER BY CASE gm.role WHEN 'creator' THEN 1 WHEN 'admin' THEN 2 END, gm.joined_at ASC`,
		groupID)
	if err != nil {
		log.Printf("Error getting group admins: %v", err)
		return admins
	}
	defer rows.Close()

	for rows.Next() {
		var admin m.GroupMember
		err := rows.Scan(&admin.ID, &admin.GroupID, &admin.UserID, &admin.Role, &admin.JoinedAt,
			&admin.Nickname, &admin.FirstName, &admin.LastName, &admin.AvatarURL)
		if err != nil {
			log.Printf("Error scanning admin: %v", err)
			continue
		}
		admins = append(admins, admin)
	}

	return admins
}

// getGroupMembers retrieves group members (with optional limit)
func getGroupMembers(groupID int, limit int) []m.GroupMember {
	var members []m.GroupMember

	query := `
		SELECT gm.id, gm.group_id, gm.user_id, gm.role, gm.joined_at,
			   u.nickname, u.first_name, u.last_name, COALESCE(u.avatar_url, '') as avatar_url
		FROM group_members gm
		JOIN users u ON gm.user_id = u.id
		WHERE gm.group_id = ?
		ORDER BY gm.joined_at ASC`

	if limit > 0 {
		query += fmt.Sprintf(" LIMIT %d", limit)
	}

	rows, err := sq.GetDB().Query(query, groupID)
	if err != nil {
		log.Printf("Error getting group members: %v", err)
		return members
	}
	defer rows.Close()

	for rows.Next() {
		var member m.GroupMember
		err := rows.Scan(&member.ID, &member.GroupID, &member.UserID, &member.Role, &member.JoinedAt,
			&member.Nickname, &member.FirstName, &member.LastName, &member.AvatarURL)
		if err != nil {
			log.Printf("Error scanning member: %v", err)
			continue
		}
		members = append(members, member)
	}

	return members
}

// getGroupEvents retrieves recent events for a group
func getGroupEvents(groupID int, limit int) []m.Event {
	var events []m.Event

	rows, err := sq.GetDB().Query(`
		SELECT e.id, e.group_id, e.creator_id, e.title, e.description, e.event_date, e.created_at,
			   u.first_name || ' ' || u.last_name as creator_name
		FROM events e
		JOIN users u ON e.creator_id = u.id
		WHERE e.group_id = ?
		ORDER BY e.event_date DESC
		LIMIT ?`, groupID, limit)
	if err != nil {
		log.Printf("Error getting group events: %v", err)
		return events
	}
	defer rows.Close()

	for rows.Next() {
		var event m.Event
		err := rows.Scan(&event.ID, &event.GroupID, &event.CreatorID, &event.Title,
			&event.Description, &event.EventDate, &event.CreatedAt, &event.CreatorName)
		if err != nil {
			log.Printf("Error scanning event: %v", err)
			continue
		}
		events = append(events, event)
	}

	return events
}
