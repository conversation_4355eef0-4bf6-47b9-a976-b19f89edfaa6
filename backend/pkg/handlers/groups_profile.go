package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	"imson/pkg/services"
)

// GetGroupProfileHandler returns detailed group information for the profile page
// Purpose: Direct URLs like /groups/123/profile, shows full metadata, rules, admin list, etc.
// Focus: Group metadata and settings, NOT chat/activity feed
func GetGroupProfileHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupID, err := validateGroupID(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Get basic group information
	group, err := getGroupBasicInfo(groupID)
	if handleGroupError(w, err, "get group details") {
		return
	}

	// Build detailed profile response
	details := m.GroupDetails{
		Group: *group,
	}

	// Set creator details and defaults
	details.CreatorName = getCreatorName(groupID)
	details.BannerURL = "/images/default_group_banner.jpg"
	details.MembershipRequirements = "No specific requirements to join this group."
	details.Rules = []string{
		"Be respectful to all members",
		"No spam or self-promotion",
		"Stay on topic in discussions",
	}

	// Check user permissions
	userRole := checkUserMembership(groupID, userID)
	details.IsUserMember = userRole != ""
	details.UserRole = userRole
	details.CanEdit = (userRole == "creator" || userRole == "admin")
	// Check invitation status using service
	groupService := services.NewGroupService()
	details.CanJoin = !details.IsUserMember && (!details.IsPrivate || groupService.CheckGroupInvitation(groupID, userID))

	// Get additional data using utility functions
	details.AdminList = getGroupAdmins(groupID)
	details.Members = getGroupMembers(groupID, 20) // Limit to first 20 for performance
	details.RecentEvents = getGroupEvents(groupID, 5)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(details)
}
