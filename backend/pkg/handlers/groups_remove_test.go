package handlers

import (
	"bytes"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	sqlite "imson/pkg/db/sqlite"

	"github.com/DATA-DOG/go-sqlmock"
)

func TestRemoveUserFromGroupHandler(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	originalDB := sqlite.DBConn
	sqlite.DBConn = db
	defer func() { sqlite.DBConn = originalDB }()

	adminID := "admin-123"
	userIDToRemove := "user-456"
	sessionID := "session-abc"

	tests := []struct {
		name           string
		method         string
		cookie         *http.Cookie
		body           []byte
		setupMocks     func()
		wantStatusCode int
		wantBody       string
	}{
		{
			name:           "Method not allowed",
			method:         http.MethodGet,
			cookie:         &http.Cookie{Name: "session_id", Value: sessionID},
			body:           nil,
			setupMocks:     func() {},
			wantStatusCode: http.StatusMethodNotAllowed,
			wantBody:       "Only POST method is allowed",
		},
		{
			name:           "Missing session cookie",
			method:         http.MethodPost,
			cookie:         nil,
			body:           []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks:     func() {},
			wantStatusCode: http.StatusUnauthorized,
			wantBody:       "User not authenticated",
		},
		{
			name:   "Invalid session cookie",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnError(sql.ErrNoRows)
			},
			wantStatusCode: http.StatusUnauthorized,
			wantBody:       "User not authenticated",
		},
		{
			name:   "Invalid JSON body",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{bad json}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(adminID))
			},
			wantStatusCode: http.StatusBadRequest,
			wantBody:       "Invalid request body",
		},
		{
			name:   "Error checking permissions",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(adminID))

				mock.ExpectQuery(`SELECT role FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, adminID).
					WillReturnError(errors.New("db error"))
			},
			wantStatusCode: http.StatusInternalServerError,
			wantBody:       "Error checking permissions",
		},
		{
			name:   "User not admin or creator",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(adminID))

				mock.ExpectQuery(`SELECT role FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, adminID).
					WillReturnRows(sqlmock.NewRows([]string{"role"}).AddRow("member"))
			},
			wantStatusCode: http.StatusForbidden,
			wantBody:       "Only admins or the group creator can remove users",
		},
		{
			name:   "DB error deleting user",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(adminID))

				mock.ExpectQuery(`SELECT role FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, adminID).
					WillReturnRows(sqlmock.NewRows([]string{"role"}).AddRow("admin"))

				mock.ExpectExec(`DELETE FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, userIDToRemove).
					WillReturnError(errors.New("delete error"))
			},
			wantStatusCode: http.StatusInternalServerError,
			wantBody:       "Failed to remove user from group",
		},
		{
			name:   "Successful removal",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1,"user_id":"user-456"}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(adminID))

				mock.ExpectQuery(`SELECT role FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, adminID).
					WillReturnRows(sqlmock.NewRows([]string{"role"}).AddRow("creator"))

				mock.ExpectExec(`DELETE FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, userIDToRemove).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantStatusCode: http.StatusOK,
			wantBody:       `{"message":"User successfully removed from group"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, "/groups/remove", bytes.NewReader(tt.body))
			if tt.cookie != nil {
				req.AddCookie(tt.cookie)
			}

			rr := httptest.NewRecorder()

			tt.setupMocks()

			RemoveUserFromGroupHandler(rr, req)

			if rr.Code != tt.wantStatusCode {
				t.Errorf("[%s] status code: got %d want %d", tt.name, rr.Code, tt.wantStatusCode)
			}

			if !bytes.Contains(rr.Body.Bytes(), []byte(tt.wantBody)) {
				t.Errorf("[%s] body: got %q want to contain %q", tt.name, rr.Body.String(), tt.wantBody)
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("[%s] unfulfilled expectations: %s", tt.name, err)
			}
		})
	}
}
