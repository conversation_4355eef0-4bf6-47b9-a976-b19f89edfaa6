package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	sq "imson/pkg/db/sqlite"

	"imson/pkg/services"

	"github.com/google/uuid"
)

var sessionStore = make(map[string]string) // sessionStore is a map that stores session IDs and their corresponding user IDs.

// Login handles user login requests.
// It only accepts POST requests with JSON body containing "email" and "password".
// The function validates the input, authenticates the user via services.AuthenticateUser,
// and if successful, creates a new session ID stored in an in-memory sessionStore.
// A session cookie is then set in the response with HttpOnly and SameSite=Lax attributes.
// On success, it responds with HTTP 200 and a JSON message indicating successful login.
// On failure, it responds with appropriate HTTP error codes and messages.

func LoginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST allowed", http.StatusMethodNotAllowed)
		return
	}

	var creds struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	if err := json.NewDecoder(r.Body).Decode(&creds); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if creds.Email == "" || creds.Password == "" {
		http.Error(w, "Email and password are required", http.StatusBadRequest)
		return
	}

	userID, err := services.AuthenticateUser(creds.Email, creds.Password)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}

	sessionID := uuid.NewString()
	expiresAt := time.Now().Add(24 * time.Hour)
	_, err = sq.GetDB().Exec(`
		INSERT INTO sessions (id, user_id, created_at, expires_at)
		VALUES (?, ?, ?, ?)`,
		sessionID, userID, time.Now(), expiresAt)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to create session: %v", err), http.StatusInternalServerError)
		return
	}
	sessionStore[sessionID] = userID

	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	token := struct {
		Token string `json:"token"`
	}{
		Token: sessionID,
	}
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(token)
}
