package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	u "imson/pkg/utils"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// Registration handler handles the user registration process from the client.
// It validates the input, hashes the password, handles the uploaded avatar
// and stores the user in the database.
func RegisterHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	r.Body = http.MaxBytesReader(w, r.Body, 20<<20)
	if err := r.ParseMultipartForm(20 << 20); err != nil {
		http.Error(w, "File too large or invalid form", http.StatusBadRequest)
		fmt.Println("Error parsing form:", err)
		return
	}

	// Collect form values
	first := r.FormValue("firstName")
	last := r.FormValue("lastName")
	nickname := r.FormValue("nickname")
	dob := r.FormValue("dob")
	about := r.FormValue("aboutMe")
	email := r.FormValue("email")
	pwd := r.FormValue("password")
	status := r.FormValue("status")
	var private int
	if status == "private" {
		private = 1
	}

	// Basic validation
	if first == "" || last == "" || email == "" || pwd == "" {
		http.Error(w, "Missing required fields", http.StatusBadRequest)
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
	if err != nil {
		http.Error(w, "Error processing password", http.StatusInternalServerError)
		return
	}

	userID := uuid.New().String()
	var avatarURL string

	// Handle avatar upload if provided
	file, header, err := r.FormFile("avatar")
	if err == nil {
		defer file.Close()

		folder := filepath.Join("images", "profiles", first+last)
		if err := os.MkdirAll(folder, 0o755); err != nil {
			fmt.Println("Error creating folders:", err)
			http.Error(w, "Error creating folders", http.StatusInternalServerError)
			return
		}

		ext := filepath.Ext(header.Filename)
		base := strings.TrimSuffix(header.Filename, ext)
		destPath := filepath.Join(folder, base+"-"+userID+ext)

		dst, err := os.Create(destPath)
		if err != nil {
			fmt.Println("Error creating file:", err)
			http.Error(w, "Error saving file", http.StatusInternalServerError)
			return
		}
		defer dst.Close()

		if _, err := io.Copy(dst, file); err != nil {
			fmt.Println("Error writing file:", err)
			http.Error(w, "Error writing file", http.StatusInternalServerError)
			return
		}

		avatarURL = destPath
	}

	// Save to DB
	_, err = sq.GetDB().Exec(`
    INSERT INTO users (
        id, nickname, first_name, last_name, email, password,
        date_of_birth, avatar_url, about_me, private
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		userID, nickname, first, last, email,
		string(hashedPassword), dob, avatarURL, about, private,
	)
	if err != nil {
		fmt.Println("Error inserting user:", err)
		http.Error(w, "Error creating user", http.StatusInternalServerError)
		return
	}
	age, err := u.CalculateAge(dob)
	if err != nil {
		fmt.Println("could not calculate the age of the user")
		http.Error(w, "Error creating user", http.StatusBadRequest)
		return
	}
	user := m.User{
		ID:        userID,
		Nickname:  nickname,
		FirstName: first,
		LastName:  last,
		Email:     email,
		Age:       age,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(user)
}
