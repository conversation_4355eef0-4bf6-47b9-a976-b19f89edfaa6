package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
)

type LeaveGroupRequest struct {
	GroupID int `json:"group_id"`
}

func LeaveGroupHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	cookie, err := r.<PERSON>("session_id")
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}
	userID, err := GetUserIDFromSession(cookie.Value)
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	var req LeaveGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Check if user is a member
	var memberCount int
	err = sq.GetDB().QueryRow("SELECT COUNT(*) FROM group_members WHERE group_id = ? AND user_id = ?", req.GroupID, userID).Scan(&memberCount)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	if memberCount == 0 {
		http.Error(w, "User is not a member of this group", http.StatusForbidden)
		return
	}

	// Remove user from the group
	_, err = sq.GetDB().Exec("DELETE FROM group_members WHERE group_id = ? AND user_id = ?", req.GroupID, userID)
	if err != nil {
		http.Error(w, "Failed to leave group", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Successfully left group"})
}
