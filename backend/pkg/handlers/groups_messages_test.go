package handlers

import (
	"bytes"
	"fmt"
	m "imson/pkg/models"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
)

// Assumes checkMembers() uses GetUsersInGroup(db, groupID)
// and GetUsersInGroup internally uses DB to pull from a `group_members` table.

func Test_checkMembers_UserIsMember(t *testing.T) {
	db := setupTestDB(t)

	// Set up test user and group
	userID := "test-user-123"
	groupID := 1
	nickname := "<PERSON>"
	_, err := db.Exec(`
	INSERT INTO users (id, nickname, first_name, last_name, gender, email, password, groups)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		userID, nickname, "<PERSON>", "<PERSON>", "male", "<EMAIL>", "hashed-password", "1")
	if err != nil {
		t.Fatalf("failed to insert test user: %v", err)
	}

	_, err = db.Exec(`INSERT INTO groups (id, name, creator_id, cardinality, administrator, about, topics) VALUES (?, ?, ?, ?, ?, ?, ?)`, groupID, "Test Group", userID, 1, nickname, "A test group for unit testing", "golang,programming,testing")
	if err != nil {
		t.Fatalf("failed to insert group: %v", err)
	}

	got := checkMembers(groupID, userID, db)
	if !got {
		t.Fatalf("expected user to be a member, got false")
	}
}

func Test_checkMembers_UserIsNotAMember(t *testing.T) {
	db := setupTestDB(t)

	// Set up test user and group
	userID := "test-user-123"
	groupID := 1
	nickname := "Raymond"
	_, err := db.Exec(`
	INSERT INTO users (id, nickname, first_name, last_name, gender, email, password, groups)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		userID, nickname, "Ray", "Smith", "male", "<EMAIL>", "hashed-password", "2")
	if err != nil {
		t.Fatalf("failed to insert test user: %v", err)
	}

	_, err = db.Exec(`INSERT INTO groups (id, name, creator_id, cardinality, administrator, about, topics) VALUES (?, ?, ?, ?, ?, ?, ?)`, groupID, "Test Group", userID, 1, nickname, "A test group for unit testing", "golang,programming,testing")
	if err != nil {
		t.Fatalf("failed to insert group: %v", err)
	}

	got := !checkMembers(groupID, userID, db)
	if !got {
		t.Fatalf("expected false;  user is not a memeber, got true")
	}
}

func TestLatestGroupMessage(t *testing.T) {
	db := setupTestDB(t)

	// Set up test user and group
	userID := "test-user-123"
	groupID := 1
	_, err := db.Exec(`INSERT INTO groups_messages (groupId, sender_id, content, created_at, read_status) VALUES (?, ?, ?, ?, ?)`, 1, userID, "Hello, Group!", time.Now(), 0)

	if err != nil {
		t.Fatalf("failed to insert group: %v", err)
	}
	got, err := LatestGroupMessage(groupID, userID, db)
	if err != nil {
		t.Fatalf("failed to fetch latest group message: %v", err)
	}
	expected := m.GroupMessage{
		ID:         1,
		GroupID:    groupID,
		SenderID:   userID,
		Content:    "Hello, Group!",
		CreatedAt:  time.Now().Format("2006-01-02 15:04:05 -0700 MST"),
		ReadStatus: 0,
	}
	if got.ID != expected.ID || got.GroupID != expected.GroupID || got.SenderID != expected.SenderID || got.Content != expected.Content {
		t.Errorf("got %+v, want %+v", got, expected)
	}
}

func TestLatestGroupMessages_ScanningError(t *testing.T) {
	db := setupTestDB(t)

	// Set up test user and group
	userID := "test-user-123"
	groupID := 1
	_, err := db.Exec(`INSERT INTO groups_messages (groupId, sender_id, created_at, read_status) VALUES (?, ?, ?, ?)`, 1, userID, time.Now(), 0)

	if err != nil {
		t.Fatalf("failed to insert group: %v", err)
	}
	got, err := LatestGroupMessage(groupID, userID, db)
	if err == nil {
		t.Errorf("got %+v, want scanning error", got)
	}
}

func TestFetchGroupMessagesHandler_Method(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/messages/{id}"

	req, err := http.NewRequest(method, endpoint, nil)
	if err != nil {
		t.Fatalf("could not create request: %v", err)
	}

	rr := httptest.NewRecorder()
	db := setupTestDB(t)

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { FetchGroupMessagesHandler(w, r, db) })

	handler.ServeHTTP(rr, req)

	if rr.Code != http.StatusMethodNotAllowed {
		t.Errorf("expected status 405 Method Not Allowed, got %d", rr.Code)
	}

	expected := "Method not allowed"
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected response body to contain %q, got %q", expected, rr.Body.String())
	}
}

func TestFetchGroupMessagesHandler_FetchingError(t *testing.T) {
	db := setupTestDB(t)
	method := http.MethodGet
	endpoint := "/api/groups/messages?id=1"

	req, err := http.NewRequest(method, endpoint, nil)
	if err != nil {
		t.Fatalf("could not create request: %v", err)
	}

	rr := httptest.NewRecorder()

	userID := "test-user-123"
	groupID := 1
	_, err = db.Exec(`INSERT INTO groups_messages (groupId, sender_id, content, created_at, read_status) VALUES (?, ?, ?, ?, ?)`, groupID, userID, "test-group-message", time.Now(), 0)
	if err != nil {
		t.Fatalf("failed to insert group message: %v", err)
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { FetchGroupMessagesHandler(w, r, db) })
	handler.ServeHTTP(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("expected status 500 Internal Server Error, got %d", rr.Code)
	}
	expected := "Failed to fetch group messages\n"
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected response body to contain %q, got %q", expected, rr.Body.String())
	}
}

func TestFetchGroupMessagesHandler_Fetching(t *testing.T) {
	db := setupTestDB(t)
	method := http.MethodGet
	endpoint := "/api/groups/messages?id=1"
	userID := "test-user-123"

	req, err := http.NewRequest(method, endpoint, nil)
	if err != nil {
		t.Fatalf("could not create request: %v", err)
	}

	rr := httptest.NewRecorder()
	sessionID := "test-session-id"
	_, err = db.Exec(`INSERT INTO sessions (id, user_id, created_at, expires_at) VALUES (?, ?, ?, ?)`, sessionID, userID, "0001-01-01 00:00:00 +0000 UTC", "0001-01-01 00:00:00 +0000 UTC")
	if err != nil {
		t.Fatalf("failed to insert test session: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	req.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	groupID := 1
	_, err = db.Exec(`INSERT INTO groups_messages (groupId, sender_id, content, created_at, read_status) VALUES (?, ?, ?, ?, ?)`, groupID, userID, "test-group-message", time.Now().Format(time.RFC3339), 0)
	if err != nil {
		t.Fatalf("failed to insert group message: %v", err)
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { FetchGroupMessagesHandler(w, r, db) })
	handler.ServeHTTP(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("expected status 200 OK, got %d", rr.Code)
	}
	expected := fmt.Sprintf("[{\"id\":1,\"groupId\":1,\"senderId\":\"test-user-123\",\"content\":\"test-group-message\",\"createdAt\":\"%s\",\"currentUserId\":\"\",\"read\":0}]\n", time.Now().Format(time.RFC3339))
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected response body to contain %q, got %q", expected, rr.Body.String())
	}
}

func TestFetchGroupMessagesHandler_ScanError(t *testing.T) {
	db := setupTestDB(t)
	method := http.MethodGet
	endpoint := "/api/groups/messages?id=1"
	userID := "test-user-123"

	req, err := http.NewRequest(method, endpoint, nil)
	if err != nil {
		t.Fatalf("could not create request: %v", err)
	}

	rr := httptest.NewRecorder()
	sessionID := "test-session-id"
	_, err = db.Exec(`INSERT INTO sessions (id, user_id, created_at, expires_at) VALUES (?, ?, ?, ?)`, sessionID, userID, "0001-01-01 00:00:00 +0000 UTC", "0001-01-01 00:00:00 +0000 UTC")
	if err != nil {
		t.Fatalf("failed to insert test session: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	req.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	groupID := 1
	_, err = db.Exec(`INSERT INTO groups_messages (groupId, sender_id, created_at, read_status) VALUES (?, ?, ?, ?)`, groupID, userID, time.Now().Format(time.RFC3339), 0)
	if err != nil {
		t.Fatalf("failed to insert group message: %v", err)
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { FetchGroupMessagesHandler(w, r, db) })
	handler.ServeHTTP(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("expected status 500 internal Server Error, got %d", rr.Code)
	}
	expected := "Failed to fetch group messages" //fmt.Sprintf("[{\"id\":1,\"groupId\":1,\"senderId\":\"test-user-123\",\"content\":\"test-group-message\",\"timestamp\":\"%s\",\"currentUserId\":\"\",\"read\":0}]\n", time.Now().Format(time.RFC3339))
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected: %q, got %q", expected, rr.Body.String())
	}
}

func TestSendGroupMessageHandler_method(t *testing.T) {
	method := http.MethodGet
	endpoint := "/api/groups/message/create?id=1"
	jsonBody := []byte(`{"id": 1,"content": "Mathematics is the language with which God has written the universe."}`)

	req := httptest.NewRequest(method, endpoint, bytes.NewReader(jsonBody))
	rr := httptest.NewRecorder()

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { SendGroupMessageHandler(w, r, nil) })
	handler.ServeHTTP(rr, req)

	if rr.Code != http.StatusMethodNotAllowed {
		t.Errorf("expected status 405 Method Not Allowed, got %d", rr.Code)
	}

	expected := "Method not allowed"
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected response body to contain %q, got %q", expected, rr.Body.String())
	}
}

func TestSendGroupMessageHandler(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/message/create"
	jsonBody := []byte(`{"id": 1,"content": "Mathematics is the language with which God has written the universe."}`)

	db := setupTestDB(t)
	req := httptest.NewRequest(method, endpoint, bytes.NewReader(jsonBody))

	userID := "test-user-123"
	rr := httptest.NewRecorder()
	sessionID := "test-session-id"
	_, err := db.Exec(`INSERT INTO sessions (id, user_id, created_at, expires_at) VALUES (?, ?, ?, ?)`, sessionID, userID, "0001-01-01 00:00:00 +0000 UTC", "0001-01-01 00:00:00 +0000 UTC")
	if err != nil {
		t.Fatalf("failed to insert test session: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { SendGroupMessageHandler(w, r, db) })
	handler.ServeHTTP(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("expected status 200 OK, got %d", rr.Code)
	}
	newTime := time.Now().Format("2006-01-02 15:04:05 -0700 MST")

	expected := fmt.Sprintf(`{"id":1,"groupId":1,"senderId":"test-user-123","content":"Mathematics is the language with which God has written the universe.","createdAt":"%s","currentUserId":"%s","read":0}`, newTime, userID)
	if !strings.Contains(rr.Body.String(), expected) {
		t.Errorf("expected: %q, got %q", expected, rr.Body.String())
	}
}

func TestSendGroupMessageHandler_InvalidRequestBody(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/message/create"
	w := httptest.NewRecorder()

	badJSON := []byte(`{
		"name": "Invalid JSON"
resp := w.Result()
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusBadRequest {
		t.Fatalf("expected 400 Bad Request, got %d", resp.StatusCode)
	}

	body, _ := io.ReadAll(resp.Body)
	if !strings.Contains(string(body), "Invalid request body") {
		t.Fatalf("expected error message 'Invalid request body', got: %s", string(body))
	}	`)

	r := httptest.NewRequest(method, endpoint, bytes.NewReader(badJSON))
	r.Header.Set("Content-Type", "application/json")

	db := setupTestDB(t)
	SendGroupMessageHandler(w, r, db)

	
}

func TestSendGroupMessageHandler_Unauthorized(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/message/create"

	w := httptest.NewRecorder()
	body := []byte(`{"id": 1,"content": "Mathematics is the language with which God has written the universe."}`)

	// 🔸 No session cookie added here
	r := httptest.NewRequest(method, endpoint, bytes.NewReader(body))
	r.Header.Set("Content-Type", "application/json")

	db := setupTestDB(t)

	SendGroupMessageHandler(w, r, db)
	res := w.Result()
	defer res.Body.Close()

	if res.StatusCode != http.StatusUnauthorized {
		t.Fatalf("expected 401 Unauthorized, got %d", res.StatusCode)
	}

	respBody, _ := io.ReadAll(res.Body)
	if !strings.Contains(string(respBody), "Unauthorized") {
		t.Fatalf("expected response to contain 'Unauthorized', got: %s", string(respBody))
	}
}

func TestSendGroupMessageHandler_EmptyContent(t *testing.T) {
	method := http.MethodPost
	endpoint := "/api/groups/message/create"
	jsonBody := []byte(`{"id": 1,"content": ""}`)

	db := setupTestDB(t)
	req := httptest.NewRequest(method, endpoint, bytes.NewReader(jsonBody))

	userID := "test-user-123"
	rr := httptest.NewRecorder()
	sessionID := "test-session-id"
	_, err := db.Exec(`INSERT INTO sessions (id, user_id, created_at, expires_at) VALUES (?, ?, ?, ?)`, sessionID, userID, "0001-01-01 00:00:00 +0000 UTC", "0001-01-01 00:00:00 +0000 UTC")
	if err != nil {
		t.Fatalf("failed to insert test session: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.AddCookie(&http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) { SendGroupMessageHandler(w, r, db) })
	handler.ServeHTTP(rr, req)
	resp := rr.Result()
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusBadRequest {
		t.Fatalf("expected 400 Bad Request, got %d", resp.StatusCode)
	}

	body, _ := io.ReadAll(resp.Body)
	if !strings.Contains(string(body), "Group message content cannot be empty") {
		t.Fatalf("expected error message 'Group message content cannot be empty', got: %s", string(body))
	}
}
