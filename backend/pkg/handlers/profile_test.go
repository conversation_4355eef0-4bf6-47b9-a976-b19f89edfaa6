package handlers

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gorilla/mux"
	m "imson/pkg/models"
)

// Test data setup
func testProfile() m.Profile {
	return m.Profile{
		Username:  "testuser",
		Email:     "<EMAIL>",
		Bio:       "Test bio",
		Location:  "Test City",
		Website:   "https://test.com",
		IsPrivate: false,
	}
}

func makeRequest(method, url string, body interface{}) (*http.Request, *httptest.ResponseRecorder) {
	var buf bytes.Buffer
	if body != nil {
		json.NewEncoder(&buf).Encode(body)
	}
	req := httptest.NewRequest(method, url, &buf)
	req.Header.Set("Content-Type", "application/json")
	return req, httptest.NewRecorder()
}

// Unit Tests for Profile Handlers

func TestCreateProfile(t *testing.T) {
	tests := []struct {
		name   string
		input  interface{}
		status int
		error  string
	}{
		{"Missing Username", m.Profile{Email: "<EMAIL>"}, http.StatusBadRequest, "Username and email are required"},
		{"Missing Email", m.Profile{Username: "test"}, http.StatusBadRequest, "Username and email are required"},
		{"Invalid JSON", "invalid", http.StatusBadRequest, "Invalid request body"},
		{"Empty Username", m.Profile{Username: "", Email: "<EMAIL>"}, http.StatusBadRequest, "Username and email are required"},
		{"Empty Email", m.Profile{Username: "test", Email: ""}, http.StatusBadRequest, "Username and email are required"},
		{"Both Empty", m.Profile{Username: "", Email: ""}, http.StatusBadRequest, "Username and email are required"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, w := makeRequest("POST", "/api/profile", tt.input)
			CreateProfileHandler(w, req)

			if w.Code != tt.status {
				t.Errorf("Expected status %d, got %d", tt.status, w.Code)
			}

			if tt.error != "" && !bytes.Contains(w.Body.Bytes(), []byte(tt.error)) {
				t.Errorf("Expected error '%s', got '%s'", tt.error, w.Body.String())
			}
		})
	}
}

func TestUpdateProfile(t *testing.T) {
	tests := []struct {
		name   string
		input  interface{}
		status int
	}{
		{"Invalid JSON", "invalid", http.StatusBadRequest},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, w := makeRequest("PUT", "/api/profile/1", tt.input)
			req = mux.SetURLVars(req, map[string]string{"id": "1"})

			UpdateProfileHandler(w, req)

			if w.Code != tt.status {
				t.Errorf("Expected status %d, got %d", tt.status, w.Code)
			}
		})
	}
}

// Helper Function Tests

func TestWriteJSON(t *testing.T) {
	w := httptest.NewRecorder()
	data := map[string]string{"test": "data"}

	writeJSON(w, http.StatusOK, data)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	if w.Header().Get("Content-Type") != "application/json" {
		t.Error("Expected Content-Type to be application/json")
	}
}

func TestHandleDBError(t *testing.T) {
	w := httptest.NewRecorder()

	// Test with sql.ErrNoRows
	if !handleDBError(w, sql.ErrNoRows, "test") {
		t.Error("Expected handleDBError to return true for sql.ErrNoRows")
	}

	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status 404, got %d", w.Code)
	}

	// Test with no error
	w = httptest.NewRecorder()
	if handleDBError(w, nil, "test") {
		t.Error("Expected handleDBError to return false for nil error")
	}
}

// Validation Tests

func TestProfileValidation(t *testing.T) {
	tests := []struct {
		name     string
		username string
		email    string
	}{
		{"Empty Username", "", "<EMAIL>"},
		{"Empty Email", "user", ""},
		{"Both Empty", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			profile := m.Profile{Username: tt.username, Email: tt.email}
			req, w := makeRequest("POST", "/api/profile", profile)

			CreateProfileHandler(w, req)

			// Should fail validation
			if w.Code != http.StatusBadRequest {
				t.Errorf("Invalid profile should fail validation, got status %d", w.Code)
			}
		})
	}
}

// JSON Processing Tests

func TestJSONProcessing(t *testing.T) {
	t.Run("Valid JSON Decode", func(t *testing.T) {
		profile := testProfile()
		req, _ := makeRequest("POST", "/api/profile", profile)

		var decoded m.Profile
		err := json.NewDecoder(req.Body).Decode(&decoded)

		if err != nil {
			t.Errorf("Failed to decode valid JSON: %v", err)
		}

		if decoded.Username != profile.Username {
			t.Errorf("Expected username %s, got %s", profile.Username, decoded.Username)
		}
	})

	t.Run("Invalid JSON Decode", func(t *testing.T) {
		req, _ := makeRequest("POST", "/api/profile", "invalid json")

		var decoded m.Profile
		err := json.NewDecoder(req.Body).Decode(&decoded)

		if err == nil {
			t.Error("Expected error for invalid JSON")
		}
	})
}
