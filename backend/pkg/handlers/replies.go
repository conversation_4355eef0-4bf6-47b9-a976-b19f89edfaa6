package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// CreateReplyHandler handles the creation of new replies to comments
func CreateReplyHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Validate session
	cookieValue, ok := ValidateSession(r)
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user ID from session
	var userID string
	err := sq.GetDB().QueryRow("SELECT user_id FROM sessions WHERE id = ?", cookieValue).Scan(&userID)
	if err != nil {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	// Parse request body
	var req m.CreateReplyRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	// Validate input
	if req.CommentID == 0 || req.Content == "" {
		http.Error(w, "Comment ID and content are required", http.StatusBadRequest)
		return
	}

	// Verify comment exists
	var commentExists bool
	err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM comments WHERE id = ?)", req.CommentID).Scan(&commentExists)
	if err != nil || !commentExists {
		http.Error(w, "Comment not found", http.StatusNotFound)
		return
	}

	// Insert reply into database
	result, err := sq.GetDB().Exec(`
		INSERT INTO replies (comment_id, user_id, content, created_at) 
		VALUES (?, ?, ?, ?)`,
		req.CommentID, userID, req.Content, time.Now())
	if err != nil {
		log.Printf("Error creating reply: %v", err)
		http.Error(w, "Error creating reply", http.StatusInternalServerError)
		return
	}

	replyID, err := result.LastInsertId()
	if err != nil {
		log.Printf("Error getting reply ID: %v", err)
		http.Error(w, "Error creating reply", http.StatusInternalServerError)
		return
	}

	// Fetch the created reply with author information
	reply, err := FetchReplyByID(int(replyID))
	if err != nil {
		log.Printf("Error fetching created reply: %v", err)
		http.Error(w, "Error retrieving reply", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(reply)
}

// GetRepliesHandler fetches all replies for a specific comment
func GetRepliesHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Validate session
	_, ok := ValidateSession(r)
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get comment ID from URL parameter
	commentIDStr := r.URL.Query().Get("comment_id")
	if commentIDStr == "" {
		http.Error(w, "Comment ID is required", http.StatusBadRequest)
		return
	}

	commentID, err := strconv.Atoi(commentIDStr)
	if err != nil {
		http.Error(w, "Invalid comment ID", http.StatusBadRequest)
		return
	}

	// Fetch replies from database
	replies, err := FetchRepliesByCommentID(commentID)
	if err != nil {
		log.Printf("Error fetching replies: %v", err)
		http.Error(w, "Error fetching replies", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(replies)
}

// LikeReplyHandler handles liking/unliking a reply
func LikeReplyHandler(w http.ResponseWriter, r *http.Request, replyIDStr string) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Validate session
	cookieValue, ok := ValidateSession(r)
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user ID from session
	var userID string
	err := sq.GetDB().QueryRow("SELECT user_id FROM sessions WHERE id = ?", cookieValue).Scan(&userID)
	if err != nil {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	// Parse reply ID
	replyID, err := strconv.Atoi(replyIDStr)
	if err != nil {
		http.Error(w, "Invalid reply ID", http.StatusBadRequest)
		return
	}

	// Check if reply exists
	var replyExists bool
	err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM replies WHERE id = ?)", replyID).Scan(&replyExists)
	if err != nil || !replyExists {
		http.Error(w, "Reply not found", http.StatusNotFound)
		return
	}

	var likeReplyObj m.LikeReplyObject
	likeReplyObj.ReplyID = replyID

	// Check if user already liked the reply
	hasLiked, err := checkUserLikedReply(replyID, userID)
	if err != nil {
		http.Error(w, "Error checking like status", http.StatusInternalServerError)
		return
	}

	if hasLiked {
		// Unlike the reply
		err = removeReplyLike(replyID, userID)
		if err != nil {
			http.Error(w, "Failed to unlike reply", http.StatusInternalServerError)
			return
		}
	} else {
		// Like the reply
		err = addReplyLike(replyID, userID)
		if err != nil {
			http.Error(w, "Failed to like reply", http.StatusInternalServerError)
			return
		}
	}

	// Get updated like count
	likeReplyObj.Likes, err = GetReplyLikes(replyID)
	if err != nil {
		http.Error(w, "Failed to retrieve like count", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(likeReplyObj)
}

// FetchReplyByID retrieves a single reply by its ID with author information
func FetchReplyByID(replyID int) (m.Reply, error) {
	var reply m.Reply
	err := sq.GetDB().QueryRow(`
		SELECT r.id, r.comment_id, r.user_id, r.content, r.likes, r.created_at,
			   u.nickname as author_nickname, u.first_name as author_first_name, 
			   u.last_name as author_last_name, u.gender as author_gender
		FROM replies r
		JOIN users u ON r.user_id = u.id
		WHERE r.id = ?`,
		replyID).Scan(
		&reply.ID, &reply.CommentID, &reply.UserID, &reply.Content, &reply.Likes, &reply.CreatedAt,
		&reply.AuthorNickname, &reply.AuthorFirstName, &reply.AuthorLastName, &reply.AuthorGender)
	if err != nil {
		return m.Reply{}, err
	}
	return reply, nil
}

// FetchRepliesByCommentID retrieves all replies for a specific comment
func FetchRepliesByCommentID(commentID int) ([]m.Reply, error) {
	rows, err := sq.GetDB().Query(`
		SELECT r.id, r.comment_id, r.user_id, r.content, r.likes, r.created_at,
			   u.nickname as author_nickname, u.first_name as author_first_name, 
			   u.last_name as author_last_name, u.gender as author_gender
		FROM replies r
		JOIN users u ON r.user_id = u.id
		WHERE r.comment_id = ?
		ORDER BY r.created_at ASC`,
		commentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var replies []m.Reply
	for rows.Next() {
		var reply m.Reply
		err := rows.Scan(
			&reply.ID, &reply.CommentID, &reply.UserID, &reply.Content, &reply.Likes, &reply.CreatedAt,
			&reply.AuthorNickname, &reply.AuthorFirstName, &reply.AuthorLastName, &reply.AuthorGender)
		if err != nil {
			log.Printf("Error scanning reply: %v", err)
			continue
		}
		replies = append(replies, reply)
	}
	return replies, nil
}

// checkUserLikedReply checks if a user has already liked a reply
func checkUserLikedReply(replyID int, userID string) (bool, error) {
	var count int
	err := sq.GetDB().QueryRow("SELECT COUNT(*) FROM reply_likes WHERE reply_id = ? AND user_id = ?",
		replyID, userID).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// addReplyLike adds a like to a reply
func addReplyLike(replyID int, userID string) error {
	tx, err := sq.GetDB().Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	// Update reply likes count
	_, err = tx.Exec("UPDATE replies SET likes = likes + 1 WHERE id = ?", replyID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to increment reply likes: %v", err)
	}

	// Insert like record
	_, err = tx.Exec("INSERT INTO reply_likes (reply_id, user_id) VALUES (?, ?)", replyID, userID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add reply like record: %v", err)
	}

	return tx.Commit()
}

// removeReplyLike removes a like from a reply
func removeReplyLike(replyID int, userID string) error {
	tx, err := sq.GetDB().Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	// Update reply likes count
	_, err = tx.Exec("UPDATE replies SET likes = likes - 1 WHERE id = ?", replyID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to decrement reply likes: %v", err)
	}

	// Remove like record
	_, err = tx.Exec("DELETE FROM reply_likes WHERE reply_id = ? AND user_id = ?", replyID, userID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to remove reply like record: %v", err)
	}

	return tx.Commit()
}

// GetReplyLikes returns the number of likes for a reply
func GetReplyLikes(replyID int) (int, error) {
	var likes int
	err := sq.GetDB().QueryRow("SELECT likes FROM replies WHERE id = ?", replyID).Scan(&likes)
	if err != nil {
		return 0, err
	}
	return likes, nil
}
