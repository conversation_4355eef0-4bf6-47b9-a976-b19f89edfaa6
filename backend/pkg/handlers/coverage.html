
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>handlers: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">imson/pkg/handlers/comments.go (0.0%)</option>
				
				<option value="file1">imson/pkg/handlers/events.go (0.0%)</option>
				
				<option value="file2">imson/pkg/handlers/follow-requests.go (0.0%)</option>
				
				<option value="file3">imson/pkg/handlers/follow.go (0.0%)</option>
				
				<option value="file4">imson/pkg/handlers/group_join_requests.go (0.0%)</option>
				
				<option value="file5">imson/pkg/handlers/groups.go (93.1%)</option>
				
				<option value="file6">imson/pkg/handlers/groups_messages.go (93.7%)</option>
				
				<option value="file7">imson/pkg/handlers/groups_page.go (32.7%)</option>
				
				<option value="file8">imson/pkg/handlers/groups_preview.go (44.4%)</option>
				
				<option value="file9">imson/pkg/handlers/groups_profile.go (25.8%)</option>
				
				<option value="file10">imson/pkg/handlers/groups_utils.go (36.2%)</option>
				
				<option value="file11">imson/pkg/handlers/login.go (100.0%)</option>
				
				<option value="file12">imson/pkg/handlers/logout.go (100.0%)</option>
				
				<option value="file13">imson/pkg/handlers/messages.go (13.0%)</option>
				
				<option value="file14">imson/pkg/handlers/notifications.go (0.0%)</option>
				
				<option value="file15">imson/pkg/handlers/posts.go (0.0%)</option>
				
				<option value="file16">imson/pkg/handlers/profile.go (30.8%)</option>
				
				<option value="file17">imson/pkg/handlers/registration.go (80.0%)</option>
				
				<option value="file18">imson/pkg/handlers/sessions.go (33.3%)</option>
				
				<option value="file19">imson/pkg/handlers/user_posts.go (0.0%)</option>
				
				<option value="file20">imson/pkg/handlers/ws-handlers.go (9.8%)</option>
				
				<option value="file21">imson/pkg/handlers/ws.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package handlers

import (
        "encoding/json"
        "fmt"
        "log"
        "net/http"
        "strconv"
        "time"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// CreateCommentHandler handles the creation of new comments
func CreateCommentHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get user from session - use same approach as follow handler
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Use the same sessionStore as login handler
        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID := userIDStr

        // Parse request body
        var req m.CreateCommentRequest
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request format", http.StatusBadRequest)
                return
        }</span>

        // Validate input
        <span class="cov0" title="0">if req.PostID == "" || req.Content == "" </span><span class="cov0" title="0">{
                http.Error(w, "Post ID and content are required", http.StatusBadRequest)
                return
        }</span>

        // Check if post exists
        <span class="cov0" title="0">var postExists bool
        err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM posts WHERE id = ?)", req.PostID).Scan(&amp;postExists)
        if err != nil || !postExists </span><span class="cov0" title="0">{
                http.Error(w, "Post not found", http.StatusNotFound)
                return
        }</span>

        // Create comment
        <span class="cov0" title="0">result, err := sq.GetDB().Exec(`
                INSERT INTO comments (post_id, user_id, content, created_at) 
                VALUES (?, ?, ?, ?)`,
                req.PostID, userID, req.Content, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error creating comment: %v", err)
                http.Error(w, "Error creating comment", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">commentID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting comment ID: %v", err)
                http.Error(w, "Error creating comment", http.StatusInternalServerError)
                return
        }</span>

        // Update post comment count
        <span class="cov0" title="0">_, err = sq.GetDB().Exec("UPDATE posts SET comment_count = comment_count + 1 WHERE id = ?", req.PostID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error updating post comment count: %v", err)
        }</span>

        // Fetch the created comment with author details
        <span class="cov0" title="0">comment, err := GetCommentByID(int(commentID), userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error fetching created comment: %v", err)
                http.Error(w, "Error retrieving comment", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusCreated)
        json.NewEncoder(w).Encode(comment)</span>
}

// GetCommentsHandler fetches comments for a specific post
func GetCommentsHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get user from session - use same approach as follow handler
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Use the same sessionStore as login handler
        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID := userIDStr

        // Get post ID from query parameters
        postID := r.URL.Query().Get("post_id")
        if postID == "" </span><span class="cov0" title="0">{
                http.Error(w, "Post ID is required", http.StatusBadRequest)
                return
        }</span>

        // Check if post exists
        <span class="cov0" title="0">var postExists bool
        err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM posts WHERE id = ?)", postID).Scan(&amp;postExists)
        if err != nil || !postExists </span><span class="cov0" title="0">{
                http.Error(w, "Post not found", http.StatusNotFound)
                return
        }</span>

        // Fetch comments
        <span class="cov0" title="0">comments, err := GetCommentsByPostID(postID, userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error fetching comments: %v", err)
                http.Error(w, "Error fetching comments", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(comments)</span>
}

// LikeCommentHandler handles liking/unliking comments
func LikeCommentHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get user from session - use same approach as follow handler
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Use the same sessionStore as login handler
        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID := userIDStr

        // Get comment ID from URL path or request body
        commentIDStr := r.URL.Query().Get("comment_id")
        if commentIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Comment ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">commentID, err := strconv.Atoi(commentIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid comment ID", http.StatusBadRequest)
                return
        }</span>

        // Check if comment exists
        <span class="cov0" title="0">var commentExists bool
        err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM comments WHERE id = ?)", commentID).Scan(&amp;commentExists)
        if err != nil || !commentExists </span><span class="cov0" title="0">{
                http.Error(w, "Comment not found", http.StatusNotFound)
                return
        }</span>

        // Check if user already liked the comment
        <span class="cov0" title="0">isLiked, err := CheckUserLikedComment(commentID, userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error checking comment like status: %v", err)
                http.Error(w, "Error processing request", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">var likeCommentObj m.LikeCommentObject
        likeCommentObj.CommentID = commentID

        if isLiked </span><span class="cov0" title="0">{
                // Unlike the comment
                err = UnlikeComment(commentID, userID)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error unliking comment: %v", err)
                        http.Error(w, "Error unliking comment", http.StatusInternalServerError)
                        return
                }</span>
        } else<span class="cov0" title="0"> {
                // Like the comment
                err = LikeComment(commentID, userID)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error liking comment: %v", err)
                        http.Error(w, "Error liking comment", http.StatusInternalServerError)
                        return
                }</span>
        }

        // Get updated like count
        <span class="cov0" title="0">likeCommentObj.Likes, err = GetCommentLikes(commentID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting comment likes: %v", err)
                http.Error(w, "Error retrieving like count", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        json.NewEncoder(w).Encode(likeCommentObj)</span>
}

// Helper function to get a comment by ID with author details
func GetCommentByID(commentID int, currentUserID string) (m.Comment, error) <span class="cov0" title="0">{
        var comment m.Comment

        query := `
                SELECT c.id, c.post_id, c.user_id, c.content, c.likes, c.created_at,
                           u.nickname, u.first_name, u.last_name, u.gender,
                           COALESCE((SELECT 1 FROM comment_likes WHERE comment_id = c.id AND user_id = ?), 0) as is_liked
                FROM comments c
                JOIN users u ON c.user_id = u.id
                WHERE c.id = ?`

        err := sq.GetDB().QueryRow(query, currentUserID, commentID).Scan(
                &amp;comment.ID, &amp;comment.PostID, &amp;comment.UserID, &amp;comment.Content,
                &amp;comment.Likes, &amp;comment.CreatedAt, &amp;comment.AuthorNickname,
                &amp;comment.AuthorFirstName, &amp;comment.AuthorLastName, &amp;comment.AuthorGender,
                &amp;comment.IsLikedByUser)

        return comment, err
}</span>

// Helper function to get comments by post ID
func GetCommentsByPostID(postID string, currentUserID string) ([]m.Comment, error) <span class="cov0" title="0">{
        query := `
                SELECT c.id, c.post_id, c.user_id, c.content, c.likes, c.created_at,
                           u.nickname, u.first_name, u.last_name, u.gender,
                           COALESCE((SELECT 1 FROM comment_likes WHERE comment_id = c.id AND user_id = ?), 0) as is_liked
                FROM comments c
                JOIN users u ON c.user_id = u.id
                WHERE c.post_id = ?
                ORDER BY c.created_at ASC`

        rows, err := sq.GetDB().Query(query, currentUserID, postID)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var comments []m.Comment
        for rows.Next() </span><span class="cov0" title="0">{
                var comment m.Comment
                err := rows.Scan(
                        &amp;comment.ID, &amp;comment.PostID, &amp;comment.UserID, &amp;comment.Content,
                        &amp;comment.Likes, &amp;comment.CreatedAt, &amp;comment.AuthorNickname,
                        &amp;comment.AuthorFirstName, &amp;comment.AuthorLastName, &amp;comment.AuthorGender,
                        &amp;comment.IsLikedByUser)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning comment: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">comments = append(comments, comment)</span>
        }

        <span class="cov0" title="0">return comments, nil</span>
}

// Helper function to check if user liked a comment
func CheckUserLikedComment(commentID int, userID string) (bool, error) <span class="cov0" title="0">{
        var count int
        err := sq.GetDB().QueryRow("SELECT COUNT(*) FROM comment_likes WHERE comment_id = ? AND user_id = ?",
                commentID, userID).Scan(&amp;count)
        if err != nil </span><span class="cov0" title="0">{
                return false, err
        }</span>
        <span class="cov0" title="0">return count &gt; 0, nil</span>
}

// Helper function to like a comment
func LikeComment(commentID int, userID string) error <span class="cov0" title="0">{
        tx, err := sq.GetDB().Begin()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to begin transaction: %v", err)
        }</span>
        <span class="cov0" title="0">defer tx.Rollback()

        // Increment likes count
        _, err = tx.Exec("UPDATE comments SET likes = likes + 1 WHERE id = ?", commentID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to increment comment likes: %v", err)
        }</span>

        // Add like record
        <span class="cov0" title="0">_, err = tx.Exec("INSERT INTO comment_likes (comment_id, user_id) VALUES (?, ?)", commentID, userID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to add comment like record: %v", err)
        }</span>

        <span class="cov0" title="0">return tx.Commit()</span>
}

// Helper function to unlike a comment
func UnlikeComment(commentID int, userID string) error <span class="cov0" title="0">{
        tx, err := sq.GetDB().Begin()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to begin transaction: %v", err)
        }</span>
        <span class="cov0" title="0">defer tx.Rollback()

        // Decrement likes count
        _, err = tx.Exec("UPDATE comments SET likes = likes - 1 WHERE id = ?", commentID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to decrement comment likes: %v", err)
        }</span>

        // Remove like record
        <span class="cov0" title="0">_, err = tx.Exec("DELETE FROM comment_likes WHERE comment_id = ? AND user_id = ?", commentID, userID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to remove comment like record: %v", err)
        }</span>

        <span class="cov0" title="0">return tx.Commit()</span>
}

// Helper function to get comment likes count
func GetCommentLikes(commentID int) (int, error) <span class="cov0" title="0">{
        var likes int
        err := sq.GetDB().QueryRow("SELECT likes FROM comments WHERE id = ?", commentID).Scan(&amp;likes)
        return likes, err
}</span>
</pre>
		
		<pre class="file" id="file1" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "fmt"
        "log"
        "net/http"
        "strconv"
        "time"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// CreateEventHandler creates a new event in a group
func CreateEventHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req m.CreateEventRequest
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request format", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">if req.Title == "" || req.EventDate.IsZero() </span><span class="cov0" title="0">{
                http.Error(w, "Title and event date are required", http.StatusBadRequest)
                return
        }</span>

        // Check if user is a member of the group
        <span class="cov0" title="0">var memberExists bool
        err = sq.GetDB().QueryRow(`
                SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
                req.GroupID, userID).Scan(&amp;memberExists)
        if err != nil || !memberExists </span><span class="cov0" title="0">{
                http.Error(w, "You are not a member of this group", http.StatusForbidden)
                return
        }</span>

        // Create the event
        <span class="cov0" title="0">result, err := sq.GetDB().Exec(`
                INSERT INTO events (group_id, creator_id, title, description, event_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?)`,
                req.GroupID, userID, req.Title, req.Description, req.EventDate, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error creating event: %v", err)
                http.Error(w, "Failed to create event", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">eventID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting event ID: %v", err)
                http.Error(w, "Failed to create event", http.StatusInternalServerError)
                return
        }</span>

        // Get creator name for notifications
        <span class="cov0" title="0">var creatorName string
        err = sq.GetDB().QueryRow(`
                SELECT first_name || ' ' || last_name FROM users WHERE id = ?`,
                userID).Scan(&amp;creatorName)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting creator name: %v", err)
                creatorName = "Someone"
        }</span>

        // Create notifications for all group members
        <span class="cov0" title="0">err = notificationService.CreateEventNotification(req.GroupID, int(eventID), req.Title, userID, creatorName)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error creating event notifications: %v", err)
        }</span>

        // Broadcast real-time notifications to group members
        <span class="cov0" title="0">go func() </span><span class="cov0" title="0">{
                // Get group name
                var groupName string
                sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", req.GroupID).Scan(&amp;groupName)

                // Get all group members except the creator
                rows, err := sq.GetDB().Query(`
                        SELECT user_id FROM group_members 
                        WHERE group_id = ? AND user_id != ?`,
                        req.GroupID, userID)
                if err != nil </span><span class="cov0" title="0">{
                        return
                }</span>
                <span class="cov0" title="0">defer rows.Close()

                data := m.EventNotificationData{
                        EventID:     int(eventID),
                        EventTitle:  req.Title,
                        GroupID:     req.GroupID,
                        GroupName:   groupName,
                        CreatorID:   userID,
                        CreatorName: creatorName,
                }

                for rows.Next() </span><span class="cov0" title="0">{
                        var memberID string
                        if err := rows.Scan(&amp;memberID); err != nil </span><span class="cov0" title="0">{
                                continue</span>
                        }

                        <span class="cov0" title="0">CreateAndBroadcastNotification(
                                memberID,
                                "event_created",
                                "New Event",
                                fmt.Sprintf("New event '%s' created in %s by %s", req.Title, groupName, creatorName),
                                data,
                        )</span>
                }
        }()

        <span class="cov0" title="0">event := m.Event{
                ID:          int(eventID),
                GroupID:     req.GroupID,
                CreatorID:   userID,
                Title:       req.Title,
                Description: req.Description,
                EventDate:   req.EventDate,
                CreatedAt:   time.Now(),
        }

        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusCreated)
        json.NewEncoder(w).Encode(event)</span>
}

// GetGroupEventsHandler retrieves events for a specific group
func GetGroupEventsHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupIDStr := r.URL.Query().Get("group_id")
        if groupIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Group ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">groupID, err := strconv.Atoi(groupIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid group ID", http.StatusBadRequest)
                return
        }</span>

        // Check if user is a member of the group
        <span class="cov0" title="0">var memberExists bool
        err = sq.GetDB().QueryRow(`
                SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
                groupID, userID).Scan(&amp;memberExists)
        if err != nil || !memberExists </span><span class="cov0" title="0">{
                http.Error(w, "You are not a member of this group", http.StatusForbidden)
                return
        }</span>

        // Get events with creator details and user's response
        <span class="cov0" title="0">rows, err := sq.GetDB().Query(`
                SELECT e.id, e.group_id, e.creator_id, e.title, e.description, 
                           e.event_date, e.created_at,
                           g.name as group_name,
                           u.first_name || ' ' || u.last_name as creator_name,
                           COALESCE(er.response, '') as user_response,
                           COUNT(er2.id) as response_count
                FROM events e
                JOIN groups g ON e.group_id = g.id
                JOIN users u ON e.creator_id = u.id
                LEFT JOIN event_responses er ON e.id = er.event_id AND er.user_id = ?
                LEFT JOIN event_responses er2 ON e.id = er2.event_id
                WHERE e.group_id = ?
                GROUP BY e.id
                ORDER BY e.event_date ASC`,
                userID, groupID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting events: %v", err)
                http.Error(w, "Failed to get events", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var events []m.Event
        for rows.Next() </span><span class="cov0" title="0">{
                var event m.Event
                err := rows.Scan(
                        &amp;event.ID, &amp;event.GroupID, &amp;event.CreatorID,
                        &amp;event.Title, &amp;event.Description, &amp;event.EventDate,
                        &amp;event.CreatedAt, &amp;event.GroupName, &amp;event.CreatorName,
                        &amp;event.UserResponse, &amp;event.ResponseCount,
                )
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning event: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">events = append(events, event)</span>
        }

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(events)</span>
}

// RespondToEventHandler handles user responses to events
func RespondToEventHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req m.EventResponseRequest
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request format", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">if req.Response != "going" &amp;&amp; req.Response != "not_going" &amp;&amp; req.Response != "maybe" </span><span class="cov0" title="0">{
                http.Error(w, "Invalid response. Must be 'going', 'not_going', or 'maybe'", http.StatusBadRequest)
                return
        }</span>

        // Check if user is a member of the group that owns this event
        <span class="cov0" title="0">var memberExists bool
        err = sq.GetDB().QueryRow(`
                SELECT EXISTS(
                        SELECT 1 FROM group_members gm
                        JOIN events e ON gm.group_id = e.group_id
                        WHERE e.id = ? AND gm.user_id = ?
                )`, req.EventID, userID).Scan(&amp;memberExists)
        if err != nil || !memberExists </span><span class="cov0" title="0">{
                http.Error(w, "You are not a member of this group", http.StatusForbidden)
                return
        }</span>

        // Insert or update the response
        <span class="cov0" title="0">_, err = sq.GetDB().Exec(`
                INSERT INTO event_responses (event_id, user_id, response, created_at)
                VALUES (?, ?, ?, ?)
                ON CONFLICT(event_id, user_id) 
                DO UPDATE SET response = ?, created_at = ?`,
                req.EventID, userID, req.Response, time.Now(),
                req.Response, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error saving event response: %v", err)
                http.Error(w, "Failed to save response", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{"status": "response saved"})</span>
}

// GetEventResponsesHandler gets responses for a specific event
func GetEventResponsesHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">eventIDStr := r.URL.Query().Get("event_id")
        if eventIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Event ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">eventID, err := strconv.Atoi(eventIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid event ID", http.StatusBadRequest)
                return
        }</span>

        // Check if user is a member of the group that owns this event
        <span class="cov0" title="0">var memberExists bool
        err = sq.GetDB().QueryRow(`
                SELECT EXISTS(
                        SELECT 1 FROM group_members gm
                        JOIN events e ON gm.group_id = e.group_id
                        WHERE e.id = ? AND gm.user_id = ?
                )`, eventID, userID).Scan(&amp;memberExists)
        if err != nil || !memberExists </span><span class="cov0" title="0">{
                http.Error(w, "You are not a member of this group", http.StatusForbidden)
                return
        }</span>

        // Get all responses for the event
        <span class="cov0" title="0">rows, err := sq.GetDB().Query(`
                SELECT er.id, er.event_id, er.user_id, er.response, er.created_at,
                           u.first_name || ' ' || u.last_name as user_name,
                           u.nickname, u.avatar_url
                FROM event_responses er
                JOIN users u ON er.user_id = u.id
                WHERE er.event_id = ?
                ORDER BY er.created_at DESC`,
                eventID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting event responses: %v", err)
                http.Error(w, "Failed to get responses", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        responses := make(map[string][]map[string]interface{})
        responses["going"] = []map[string]interface{}{}
        responses["not_going"] = []map[string]interface{}{}
        responses["maybe"] = []map[string]interface{}{}

        for rows.Next() </span><span class="cov0" title="0">{
                var response m.EventResponse
                var userName, nickname string
                var avatarURL sql.NullString

                err := rows.Scan(
                        &amp;response.ID, &amp;response.EventID, &amp;response.UserID,
                        &amp;response.Response, &amp;response.CreatedAt,
                        &amp;userName, &amp;nickname, &amp;avatarURL,
                )
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning response: %v", err)
                        continue</span>
                }

                <span class="cov0" title="0">userInfo := map[string]interface{}{
                        "user_id":    response.UserID,
                        "user_name":  userName,
                        "nickname":   nickname,
                        "created_at": response.CreatedAt,
                }

                if avatarURL.Valid </span><span class="cov0" title="0">{
                        userInfo["avatar_url"] = avatarURL.String
                }</span>

                <span class="cov0" title="0">responses[response.Response] = append(responses[response.Response], userInfo)</span>
        }

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(responses)</span>
}</pre>
		
		<pre class="file" id="file2" style="display: none">package handlers

import (
        "encoding/json"
        "net/http"
        "strconv"

        sq "imson/pkg/db/sqlite"
        "imson/pkg/services"
)

type FollowRequestResponse struct {
        RequestID int `json:"request_id"`
}

// AcceptFollowRequest handles accepting a follow request
func AcceptFollowRequest(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get current user from session
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID, err := strconv.Atoi(userIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req FollowRequestResponse
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Bad request", http.StatusBadRequest)
                return
        }</span>

        // Verify the request belongs to the current user and update status
        <span class="cov0" title="0">err = services.UpdateFollowRequestStatus(req.RequestID, userID, "accepted")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not accept follow request", http.StatusInternalServerError)
                return
        }</span>

        // Get follower info for notification
        <span class="cov0" title="0">followerInfo, err := services.GetFollowerInfoByRequestID(req.RequestID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not get follower info", http.StatusInternalServerError)
                return
        }</span>

        // Send WebSocket notification to the follower
        // Get user info for notification
        <span class="cov0" title="0">var accepterName string
        err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userIDStr).Scan(&amp;accepterName)
        if err == nil </span><span class="cov0" title="0">{
                BroadcastFollowAcceptedNotification(followerInfo.FollowerID, userID, accepterName)
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{
                "status":  "success",
                "message": "Follow request accepted",
        })</span>
}

// DeclineFollowRequest handles declining a follow request
func DeclineFollowRequest(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get current user from session
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID, err := strconv.Atoi(userIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req FollowRequestResponse
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Bad request", http.StatusBadRequest)
                return
        }</span>

        // Verify the request belongs to the current user and update status
        <span class="cov0" title="0">err = services.UpdateFollowRequestStatus(req.RequestID, userID, "declined")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not decline follow request", http.StatusInternalServerError)
                return
        }</span>

        // Get follower info for notification
        <span class="cov0" title="0">followerInfo, err := services.GetFollowerInfoByRequestID(req.RequestID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not get follower info", http.StatusInternalServerError)
                return
        }</span>

        // Send WebSocket notification to the follower
        // Get user info for notification
        <span class="cov0" title="0">var declinerName string
        err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userIDStr).Scan(&amp;declinerName)
        if err == nil </span><span class="cov0" title="0">{
                BroadcastFollowDeclinedNotification(followerInfo.FollowerID, userID, declinerName)
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{
                "status":  "success",
                "message": "Follow request declined",
        })</span>
}

// GetPendingFollowRequests returns all pending follow requests for the current user
func GetPendingFollowRequests(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get current user from session
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userIDStr, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">userID, err := strconv.Atoi(userIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">requests, err := services.GetPendingFollowRequests(userID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not get follow requests", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(requests)</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package handlers

import (
        "encoding/json"
        "net/http"
        "strconv"

        sq "imson/pkg/db/sqlite"
        "imson/pkg/services"
)

type FollowRequest struct {
        FollowingID string `json:"following_id"` // Changed to string to match user ID format
}

func FollowUser(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        // Get user from session - use same cookie name as login handler
        <span class="cov0" title="0">cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Use the same sessionStore as login handler
        <span class="cov0" title="0">userID, ok := sessionStore[cookie.Value]
        if !ok </span><span class="cov0" title="0">{
                http.Error(w, "Invalid session", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req FollowRequest
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Bad request", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">if req.FollowingID == userID </span><span class="cov0" title="0">{
                http.Error(w, "Cannot follow yourself", http.StatusBadRequest)
                return
        }</span>

        // Convert string IDs to integers for the service functions
        <span class="cov0" title="0">userIDInt, err := strconv.Atoi(userID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid user ID", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">followingIDInt, err := strconv.Atoi(req.FollowingID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid following ID", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">isPrivate, err := services.IsPrivateProfile(followingIDInt)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "User not found", http.StatusNotFound)
                return
        }</span>

        <span class="cov0" title="0">status := "accepted"
        if isPrivate </span><span class="cov0" title="0">{
                status = "pending"
        }</span>

        <span class="cov0" title="0">err = services.CreateFollowRequest(userIDInt, followingIDInt, status)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Could not send follow request", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">if status == "pending" </span><span class="cov0" title="0">{
                // Get sender info for notification
                var senderName string
                err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&amp;senderName)
                if err == nil </span><span class="cov0" title="0">{
                        // Get the request ID
                        var requestID int
                        err = sq.GetDB().QueryRow("SELECT id FROM followers WHERE follower_id = ? AND following_id = ? ORDER BY created_at DESC LIMIT 1", userIDInt, followingIDInt).Scan(&amp;requestID)
                        if err == nil </span><span class="cov0" title="0">{
                                BroadcastFollowRequestNotification(followingIDInt, userIDInt, senderName, requestID)
                        }</span>
                }
        }

        <span class="cov0" title="0">json.NewEncoder(w).Encode(map[string]string{
                "status":  status,
                "message": "Follow request sent",
        })</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "fmt"
        "log"
        "net/http"
        "strconv"
        "time"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// GetGroupJoinRequestsHandler gets pending join requests for groups created by the current user
func GetGroupJoinRequestsHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupIDStr := r.URL.Query().Get("group_id")
        var query string
        var args []interface{}

        if groupIDStr != "" </span><span class="cov0" title="0">{
                // Get requests for a specific group
                groupID, err := strconv.Atoi(groupIDStr)
                if err != nil </span><span class="cov0" title="0">{
                        http.Error(w, "Invalid group ID", http.StatusBadRequest)
                        return
                }</span>

                // Check if user is the creator of the group
                <span class="cov0" title="0">var isCreator bool
                err = sq.GetDB().QueryRow(`
                        SELECT EXISTS(SELECT 1 FROM groups WHERE id = ? AND creator_id = ?)`,
                        groupID, userID).Scan(&amp;isCreator)
                if err != nil || !isCreator </span><span class="cov0" title="0">{
                        http.Error(w, "You are not the creator of this group", http.StatusForbidden)
                        return
                }</span>

                <span class="cov0" title="0">query = `
                        SELECT gjr.id, gjr.group_id, gjr.user_id, gjr.created_at,
                                   u.nickname, u.first_name || ' ' || u.last_name as user_name,
                                   COALESCE(u.avatar_url, '') as user_avatar
                        FROM group_join_requests gjr
                        JOIN users u ON gjr.user_id = u.id
                        WHERE gjr.group_id = ? AND gjr.status = 'pending'
                        ORDER BY gjr.created_at DESC`
                args = []interface{}{groupID}</span>
        } else<span class="cov0" title="0"> {
                // Get all requests for groups created by the user
                query = `
                        SELECT gjr.id, gjr.group_id, gjr.user_id, gjr.created_at,
                                   u.nickname, u.first_name || ' ' || u.last_name as user_name,
                                   COALESCE(u.avatar_url, '') as user_avatar
                        FROM group_join_requests gjr
                        JOIN users u ON gjr.user_id = u.id
                        JOIN groups g ON gjr.group_id = g.id
                        WHERE g.creator_id = ? AND gjr.status = 'pending'
                        ORDER BY gjr.created_at DESC`
                args = []interface{}{userID}
        }</span>

        <span class="cov0" title="0">rows, err := sq.GetDB().Query(query, args...)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting join requests: %v", err)
                http.Error(w, "Failed to get join requests", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var requests []m.GroupJoinRequest
        for rows.Next() </span><span class="cov0" title="0">{
                var request m.GroupJoinRequest
                err := rows.Scan(
                        &amp;request.ID, &amp;request.GroupID, &amp;request.UserID,
                        &amp;request.CreatedAt, &amp;request.UserNickname,
                        &amp;request.UserName, &amp;request.UserAvatar,
                )
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning join request: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">requests = append(requests, request)</span>
        }

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(requests)</span>
}

// AcceptGroupJoinRequestHandler accepts a group join request
func AcceptGroupJoinRequestHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">requestIDStr := r.URL.Query().Get("request_id")
        if requestIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Request ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">requestID, err := strconv.Atoi(requestIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request ID", http.StatusBadRequest)
                return
        }</span>

        // Get request details and verify the user is the group creator
        <span class="cov0" title="0">var groupID int
        var requesterID string
        err = sq.GetDB().QueryRow(`
                SELECT gjr.group_id, gjr.user_id
                FROM group_join_requests gjr
                JOIN groups g ON gjr.group_id = g.id
                WHERE gjr.id = ? AND g.creator_id = ? AND gjr.status = 'pending'`,
                requestID, userID).Scan(&amp;groupID, &amp;requesterID)
        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        http.Error(w, "Join request not found or you're not authorized", http.StatusNotFound)
                }</span> else<span class="cov0" title="0"> {
                        log.Printf("Error getting join request: %v", err)
                        http.Error(w, "Database error", http.StatusInternalServerError)
                }</span>
                <span class="cov0" title="0">return</span>
        }

        // Begin transaction
        <span class="cov0" title="0">tx, err := sq.GetDB().Begin()
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error beginning transaction: %v", err)
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer tx.Rollback()

        // Update request status
        _, err = tx.Exec(`
                UPDATE group_join_requests SET status = 'accepted'
                WHERE id = ?`, requestID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error updating join request: %v", err)
                http.Error(w, "Failed to accept request", http.StatusInternalServerError)
                return
        }</span>

        // Add user to group
        <span class="cov0" title="0">_, err = tx.Exec(`
                INSERT INTO group_members (group_id, user_id, role, joined_at)
                VALUES (?, ?, 'member', ?)`,
                groupID, requesterID, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error adding user to group: %v", err)
                http.Error(w, "Failed to add user to group", http.StatusInternalServerError)
                return
        }</span>

        // Commit transaction
        <span class="cov0" title="0">if err = tx.Commit(); err != nil </span><span class="cov0" title="0">{
                log.Printf("Error committing transaction: %v", err)
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>

        // Send notification to the requester
        <span class="cov0" title="0">go func() </span><span class="cov0" title="0">{
                var groupName, accepterName string
                sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&amp;groupName)
                sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&amp;accepterName)

                CreateAndBroadcastNotification(
                        requesterID,
                        "group_join_accepted",
                        "Join Request Accepted",
                        fmt.Sprintf("Your request to join %s has been accepted", groupName),
                        map[string]interface{}{
                                "group_id":      groupID,
                                "group_name":    groupName,
                                "accepter_id":   userID,
                                "accepter_name": accepterName,
                        },
                )
        }</span>()

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{"status": "request accepted"})</span>
}

// DeclineGroupJoinRequestHandler declines a group join request
func DeclineGroupJoinRequestHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">requestIDStr := r.URL.Query().Get("request_id")
        if requestIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Request ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">requestID, err := strconv.Atoi(requestIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request ID", http.StatusBadRequest)
                return
        }</span>

        // Get request details and verify the user is the group creator
        <span class="cov0" title="0">var groupID int
        var requesterID string
        err = sq.GetDB().QueryRow(`
                SELECT gjr.group_id, gjr.user_id
                FROM group_join_requests gjr
                JOIN groups g ON gjr.group_id = g.id
                WHERE gjr.id = ? AND g.creator_id = ? AND gjr.status = 'pending'`,
                requestID, userID).Scan(&amp;groupID, &amp;requesterID)
        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        http.Error(w, "Join request not found or you're not authorized", http.StatusNotFound)
                }</span> else<span class="cov0" title="0"> {
                        log.Printf("Error getting join request: %v", err)
                        http.Error(w, "Database error", http.StatusInternalServerError)
                }</span>
                <span class="cov0" title="0">return</span>
        }

        // Update request status
        <span class="cov0" title="0">result, err := sq.GetDB().Exec(`
                UPDATE group_join_requests SET status = 'declined'
                WHERE id = ?`, requestID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error declining join request: %v", err)
                http.Error(w, "Failed to decline request", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">rowsAffected, err := result.RowsAffected()
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting affected rows: %v", err)
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">if rowsAffected == 0 </span><span class="cov0" title="0">{
                http.Error(w, "Join request not found", http.StatusNotFound)
                return
        }</span>

        // Send notification to the requester
        <span class="cov0" title="0">go func() </span><span class="cov0" title="0">{
                var groupName, declinerName string
                sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&amp;groupName)
                sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&amp;declinerName)

                CreateAndBroadcastNotification(
                        requesterID,
                        "group_join_declined",
                        "Join Request Declined",
                        fmt.Sprintf("Your request to join %s has been declined", groupName),
                        map[string]interface{}{
                                "group_id":      groupID,
                                "group_name":    groupName,
                                "decliner_id":   userID,
                                "decliner_name": declinerName,
                        },
                )
        }</span>()

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{"status": "request declined"})</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "net/http"
        "time"

        m "imson/pkg/models"
        u "imson/pkg/utils"
)

// handler to create a group
func CreateGroup(wr http.ResponseWriter, rq *http.Request, DB *sql.DB) <span class="cov8" title="1">{
        if rq.Method != http.MethodPost </span><span class="cov8" title="1">{
                http.Error(wr, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">var req struct {
                Name          string `json:"name"`
                Administrator string `json:"admin"`
                About         string `json:"about"`
                Topics        string `json:"topics"`
        }

        if err := json.NewDecoder(rq.Body).Decode(&amp;req); err != nil </span><span class="cov8" title="1">{
                http.Error(wr, "Invalid request body", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">if req.Name == "" || req.About == "" || req.Topics == "" </span><span class="cov8" title="1">{
                http.Error(wr, "Message incomplete", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">creatorID, err := GetCurrentUserID(rq, DB)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(wr, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov8" title="1">var admin string
        err = DB.QueryRow(`
                SELECT nickname FROM users WHERE id = ?
        `, creatorID).Scan(&amp;admin)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(wr, "Database error", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov8" title="1">combinedTopics := req.Topics

        result, _ := DB.Exec(
                `INSERT INTO groups (name, creator_id, cardinality, administrator, about, topics, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                req.Name, creatorID, 1, admin, req.About, combinedTopics, time.Now(), time.Now(),
        )

        groupID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                http.Error(wr, "Failed to retrieve group ID", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov8" title="1">group := m.Group{
                ID:            int(groupID),
                Name:          req.Name,
                CreatorID:     creatorID,
                Cardinality:   1,
                Administrator: admin,
                About:         req.About,
                Topics:        u.SplitAndTrim(req.Topics),
        }

        wr.WriteHeader(http.StatusOK)
        wr.Header().Set("Content-Type", "application/json")
        json.NewEncoder(wr).Encode(group)</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "fmt"
        "log"
        "net/http"
        "slices"
        "strconv"
        "time"

        m "imson/pkg/models"
)

// retrieves the group messages based on the group ID(this handler is evoked when the a user visits the page of the grouup chat)
func FetchGroupMessagesHandler(w http.ResponseWriter, r *http.Request, db *sql.DB) <span class="cov8" title="1">{
        if r.Method != http.MethodGet </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">query := r.URL.Query()
        groupId := query.Get("id")

        group_messages, err := FetchGroupMessages(r, groupId, db)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Failed to fetch group messages", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov8" title="1">w.WriteHeader(http.StatusOK)
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(group_messages)</span>
}

// retrieves the messages of the group from the database based on the passed groupID
func FetchGroupMessages(r *http.Request, groupId string, db *sql.DB) ([]m.GroupMessage, error) <span class="cov8" title="1">{
        currentUserID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>
        <span class="cov8" title="1">id, _ := strconv.Atoi(groupId)

        // Query only the required slice of messages directly from the database
        rows, _ := db.Query(`
                SELECT id, groupId, sender_id, content, created_at, read_status 
                FROM groups_messages 
                WHERE (sender_id = ? AND id = ?)
                ORDER BY created_at DESC`,
                currentUserID, id,
        )
        defer rows.Close()

        var messages []m.GroupMessage
        for rows.Next() </span><span class="cov8" title="1">{
                var message m.GroupMessage
                if err := rows.Scan(&amp;message.ID, &amp;message.GroupID, &amp;message.SenderID, &amp;message.Content, &amp;message.CreatedAt, &amp;message.ReadStatus); err != nil </span><span class="cov8" title="1">{
                        fmt.Println("Error scanning group message:", err)
                        return nil, err
                }</span>
                <span class="cov8" title="1">messages = append(messages, message)</span>
        }

        <span class="cov8" title="1">return messages, nil</span> // finalGroupMessages(messages, limit, offset), nil
}

// responsible for handling a newly created group message and storing the message to the databbase
func SendGroupMessageHandler(w http.ResponseWriter, r *http.Request, db *sql.DB) <span class="cov8" title="1">{
        if r.Method != http.MethodPost </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">var req struct {
                Id      int    `json:"id"`
                Content string `json:"content"`
        }

        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Invalid request body", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">if req.Content == "" </span><span class="cov8" title="1">{
                http.Error(w, "Group message content cannot be empty", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">senderID, err := GetCurrentUserID(r, db)
        // Sender_id = senderID
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov8" title="1">now := time.Now()

        // storing the message in the database
        result, _ := db.Exec(
                `INSERT INTO groups_messages (groupId, sender_id, content, created_at) VALUES (?, ?, ?, ?)`,
                req.Id, senderID, req.Content, now,
        )

        messageID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Failed to retrieve group message ID", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov8" title="1">userID, _ := GetCurrentUserID(r, db)

        message := m.GroupMessage{
                ID:            int(messageID),
                GroupID:       req.Id,
                SenderID:      senderID,
                Content:       req.Content,
                CreatedAt:     now.Format("2006-01-02 15:04:05 -0700 MST"),
                CurrentUserId: userID,
                ReadStatus:    0,
        }

        w.WriteHeader(http.StatusOK)
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(message)</span>
}

// LatestPrivateMessage fetches the most recent group message from the database based on a given group ID
func LatestGroupMessage(groupId int, senderId string, db *sql.DB) (m.GroupMessage, error) <span class="cov8" title="1">{
        var msg m.GroupMessage
        row := db.QueryRow(`
                SELECT id, groupId, sender_id, content, created_at, read_status
                FROM groups_messages
                WHERE sender_id = ?
                ORDER BY created_at DESC
                LIMIT 1
        `, senderId)

        err := row.Scan(&amp;msg.ID, &amp;msg.GroupID, &amp;msg.SenderID, &amp;msg.Content, &amp;msg.CreatedAt, &amp;msg.ReadStatus)
        if err != nil </span><span class="cov8" title="1">{
                return m.GroupMessage{}, err
        }</span>
        <span class="cov8" title="1">return msg, nil</span>
}

// function to fetch the members IDs in the group based on the group ID
func GetUsersInGroup(db *sql.DB, groupID int) ([]string, error) <span class="cov8" title="1">{
        var userIDs []string

        groupStr := strconv.Itoa(groupID)

        // Use SQL LIKE patterns to match different possible placements of groupID
        query := `
        SELECT id 
        FROM users 
        WHERE groups = ? 
           OR groups LIKE ? 
           OR groups LIKE ? 
           OR groups LIKE ?
    `

        rows, err := db.Query(query,
                groupStr,
                "%,"+groupStr,
                groupStr+",%",
                "%,"+groupStr+",%",
        )
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("query error: %w", err)
        }</span>
        <span class="cov8" title="1">defer rows.Close()

        for rows.Next() </span><span class="cov8" title="1">{
                var id string
                if err := rows.Scan(&amp;id); err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("scan error: %w", err)
                }</span>
                <span class="cov8" title="1">userIDs = append(userIDs, id)</span>
        }

        <span class="cov8" title="1">if err := rows.Err(); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("row error: %w", err)
        }</span>
        <span class="cov8" title="1">if len(userIDs) == 0 </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("no users found in group with ID %d", groupID)
        }</span>
        <span class="cov8" title="1">return userIDs, nil</span>
}

// compares the passed userID, checking if it exists in the groups members
func checkMembers(groupID int, userID string, DB *sql.DB) bool <span class="cov8" title="1">{
        members, err := GetUsersInGroup(DB, groupID)
        if err != nil </span><span class="cov8" title="1">{
                log.Println("Failed to fetch group members:", err)
                return false
        }</span>
        <span class="cov8" title="1">return slices.Contains(members, userID)</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package handlers

import (
        "encoding/json"
        "net/http"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        "imson/pkg/services"
)

// GroupPageData represents the data needed for the main group interaction page
type GroupPageData struct {
        Group          m.Group                  `json:"group"`
        Members        []map[string]interface{} `json:"members"`
        RecentMessages []m.GroupMessage         `json:"recent_messages"`
        ActivityFeed   []map[string]interface{} `json:"activity_feed"`
        IsUserMember   bool                     `json:"is_user_member"`
        UserRole       string                   `json:"user_role,omitempty"`
        CanPost        bool                     `json:"can_post"`
        OnlineCount    int                      `json:"online_count"`
}

// GroupMemberWithStatus extends GroupMember with online status
type GroupMemberWithStatus struct {
        m.GroupMember
        IsOnline bool   `json:"is_online"`
        LastSeen string `json:"last_seen,omitempty"`
        Status   string `json:"status,omitempty"` // "online", "away", "busy", "offline"
}

// ActivityItem represents an activity in the group feed
type ActivityItem struct {
        ID         int    `json:"id"`
        Type       string `json:"type"` // "message", "join", "leave", "event", "file_upload"
        UserID     string `json:"user_id"`
        UserName   string `json:"user_name"`
        UserAvatar string `json:"user_avatar,omitempty"`
        Content    string `json:"content"`
        Timestamp  string `json:"timestamp"`
        GroupID    int    `json:"group_id"`
        RelatedID  int    `json:"related_id,omitempty"` // ID of related message, event, etc.
}

// GetGroupPageHandler returns the main group interaction page with chat, members, and activity
// Purpose: Main group page /groups/123 where users participate in the group
// Features: Chat/activity feed (central) + member sidebar with online status
func GetGroupPageHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodGet </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupID, err := validateGroupID(r)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, err.Error(), http.StatusBadRequest)
                return
        }</span>

        // Check access permissions
        <span class="cov0" title="0">canAccess, isUserMember, err := checkGroupAccess(groupID, userID)
        if handleGroupError(w, err, "check group access") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">if !canAccess </span><span class="cov0" title="0">{
                http.Error(w, "Access denied: You must be a member to view this private group", http.StatusForbidden)
                return
        }</span>

        // Get group information
        <span class="cov0" title="0">group, err := getGroupBasicInfo(groupID)
        if handleGroupError(w, err, "get group information") </span><span class="cov0" title="0">{
                return
        }</span>

        // Build page data using service layer with WebSocket integration
        <span class="cov0" title="0">groupService := services.NewGroupService()
        userRole := checkUserMembership(groupID, userID)

        // Get members with real-time online status from service layer
        // The service layer now handles WebSocket integration internally
        members := groupService.GetGroupMembersWithStatus(groupID)

        // Get online count using the service layer
        onlineCount := groupService.GetGroupOnlineCount(groupID)

        pageData := GroupPageData{
                Group:          *group,
                IsUserMember:   isUserMember,
                UserRole:       userRole,
                CanPost:        isUserMember, // Members can post messages
                Members:        members,
                RecentMessages: groupService.GetRecentGroupMessages(groupID, 50),
                ActivityFeed:   groupService.GetGroupActivityFeed(groupID, 20),
                OnlineCount:    onlineCount, // Real-time online count from WebSocket
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(pageData)</span>
}

// GetGroupMembersHandler returns only the members of a group (for dynamic updates)
func GetGroupMembersHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodGet </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupID, err := validateGroupID(r)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, err.Error(), http.StatusBadRequest)
                return
        }</span>

        // Check access permissions using utility function
        <span class="cov0" title="0">canAccess, _, err := checkGroupAccess(groupID, userID)
        if handleGroupError(w, err, "check group access") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">if !canAccess </span><span class="cov0" title="0">{
                http.Error(w, "Access denied: You must be a member to view this private group's members", http.StatusForbidden)
                return
        }</span>

        // Get group members using utility function
        <span class="cov0" title="0">members := getGroupMembers(groupID, 0) // 0 = no limit

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(members)</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package handlers

import (
        "encoding/json"
        "net/http"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// GetGroupPreviewHandler returns a lightweight preview of group information
// Purpose: Used for search results, invitations, pop-ups when hovering over group name
// Response: Minimal details - group name, description, member count, creator, membership status
func GetGroupPreviewHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodGet </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupID, err := validateGroupID(r)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, err.Error(), http.StatusBadRequest)
                return
        }</span>

        // Get basic group information
        <span class="cov0" title="0">group, err := getGroupBasicInfo(groupID)
        if handleGroupError(w, err, "get group preview") </span><span class="cov0" title="0">{
                return
        }</span>

        // Build preview response
        <span class="cov0" title="0">preview := m.GroupPreview{
                ID:           group.ID,
                Name:         group.Name,
                Description:  group.About,
                MemberCount:  group.Cardinality,
                IsPrivate:    group.IsPrivate,
                CreatorName:  getCreatorName(groupID),
                IsUserMember: checkUserMembership(groupID, userID) != "",
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(preview)</span>
}
</pre>
		
		<pre class="file" id="file9" style="display: none">package handlers

import (
        "encoding/json"
        "net/http"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        "imson/pkg/services"
)

// GetGroupProfileHandler returns detailed group information for the profile page
// Purpose: Direct URLs like /groups/123/profile, shows full metadata, rules, admin list, etc.
// Focus: Group metadata and settings, NOT chat/activity feed
func GetGroupProfileHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodGet </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">groupID, err := validateGroupID(r)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, err.Error(), http.StatusBadRequest)
                return
        }</span>

        // Get basic group information
        <span class="cov0" title="0">group, err := getGroupBasicInfo(groupID)
        if handleGroupError(w, err, "get group details") </span><span class="cov0" title="0">{
                return
        }</span>

        // Build detailed profile response
        <span class="cov0" title="0">details := m.GroupDetails{
                Group: *group,
        }

        // Set creator details and defaults
        details.CreatorName = getCreatorName(groupID)
        details.BannerURL = "/images/default_group_banner.jpg"
        details.MembershipRequirements = "No specific requirements to join this group."
        details.Rules = []string{
                "Be respectful to all members",
                "No spam or self-promotion",
                "Stay on topic in discussions",
        }

        // Check user permissions
        userRole := checkUserMembership(groupID, userID)
        details.IsUserMember = userRole != ""
        details.UserRole = userRole
        details.CanEdit = (userRole == "creator" || userRole == "admin")
        // Check invitation status using service
        groupService := services.NewGroupService()
        details.CanJoin = !details.IsUserMember &amp;&amp; (!details.IsPrivate || groupService.CheckGroupInvitation(groupID, userID))

        // Get additional data using utility functions
        details.AdminList = getGroupAdmins(groupID)
        details.Members = getGroupMembers(groupID, 20) // Limit to first 20 for performance
        details.RecentEvents = getGroupEvents(groupID, 5)

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(details)</span>
}
</pre>
		
		<pre class="file" id="file10" style="display: none">package handlers

import (
        "database/sql"
        "fmt"
        "log"
        "net/http"
        "strconv"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// GroupUtils contains common utility functions for group handlers
// This file extracts repeated code patterns to improve maintainability

// validateGroupID extracts and validates group_id from query parameters
func validateGroupID(r *http.Request) (int, error) <span class="cov8" title="1">{
        groupIDStr := r.URL.Query().Get("group_id")
        if groupIDStr == "" </span><span class="cov8" title="1">{
                return 0, fmt.Errorf("Group ID is required")
        }</span>

        <span class="cov8" title="1">groupID, err := strconv.Atoi(groupIDStr)
        if err != nil </span><span class="cov8" title="1">{
                return 0, fmt.Errorf("Invalid group ID")
        }</span>

        <span class="cov8" title="1">return groupID, nil</span>
}

// handleGroupError provides consistent error handling for group operations
func handleGroupError(w http.ResponseWriter, err error, operation string) bool <span class="cov8" title="1">{
        if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                http.Error(w, "Group not found", http.StatusNotFound)
                return true
        }</span>
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                log.Printf("Error %s: %v", operation, err)
                http.Error(w, fmt.Sprintf("Failed to %s", operation), http.StatusInternalServerError)
                return true
        }</span>
        <span class="cov8" title="1">return false</span>
}

// checkUserMembership returns user's role in group (empty string if not a member)
func checkUserMembership(groupID int, userID string) string <span class="cov8" title="1">{
        var role sql.NullString
        err := sq.GetDB().QueryRow(`
                SELECT role FROM group_members WHERE group_id = ? AND user_id = ?`,
                groupID, userID).Scan(&amp;role)

        if err != nil &amp;&amp; err != sql.ErrNoRows </span><span class="cov8" title="1">{
                log.Printf("Error checking user membership: %v", err)
        }</span>

        <span class="cov8" title="1">if role.Valid </span><span class="cov0" title="0">{
                return role.String
        }</span>
        <span class="cov8" title="1">return ""</span>
}

// isUserMemberOfGroup checks if a user is a member of a group (returns boolean)
func isUserMemberOfGroup(groupID int, userID string) bool <span class="cov8" title="1">{
        var exists bool
        err := sq.GetDB().QueryRow(`
                SELECT EXISTS(SELECT 1 FROM group_members WHERE group_id = ? AND user_id = ?)`,
                groupID, userID).Scan(&amp;exists)
        if err != nil </span><span class="cov8" title="1">{
                log.Printf("Error checking group membership: %v", err)
                return false
        }</span>

        <span class="cov0" title="0">return exists</span>
}

// checkGroupAccess verifies if user can access a group (handles private groups)
func checkGroupAccess(groupID int, userID string) (bool, bool, error) <span class="cov8" title="1">{
        var isPrivate bool
        err := sq.GetDB().QueryRow(`SELECT is_private FROM groups WHERE id = ?`, groupID).Scan(&amp;isPrivate)
        if err != nil </span><span class="cov8" title="1">{
                return false, false, err
        }</span>

        <span class="cov0" title="0">userRole := checkUserMembership(groupID, userID)
        isUserMember := userRole != ""

        // For private groups, only members can access
        canAccess := !isPrivate || isUserMember

        return canAccess, isUserMember, nil</span>
}

// getGroupBasicInfo retrieves basic group information
func getGroupBasicInfo(groupID int) (*m.Group, error) <span class="cov8" title="1">{
        var group m.Group
        err := sq.GetDB().QueryRow(`
                SELECT g.id, g.name, g.description, g.creator_id, g.is_private, g.created_at,
                           COUNT(gm.user_id) as member_count
                FROM groups g
                LEFT JOIN group_members gm ON g.id = gm.group_id
                WHERE g.id = ?
                GROUP BY g.id`, groupID).Scan(
                &amp;group.ID, &amp;group.Name, &amp;group.About, &amp;group.CreatorID,
                &amp;group.IsPrivate, &amp;group.CreatedAt, &amp;group.Cardinality)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return &amp;group, nil</span>
}

// getCreatorName retrieves the creator's full name for a group
func getCreatorName(groupID int) string <span class="cov0" title="0">{
        var firstName, lastName sql.NullString
        err := sq.GetDB().QueryRow(`
                SELECT u.first_name, u.last_name
                FROM groups g
                JOIN users u ON g.creator_id = u.id
                WHERE g.id = ?`, groupID).Scan(&amp;firstName, &amp;lastName)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting creator name: %v", err)
                return ""
        }</span>

        <span class="cov0" title="0">if firstName.Valid &amp;&amp; lastName.Valid </span><span class="cov0" title="0">{
                return firstName.String + " " + lastName.String
        }</span>
        <span class="cov0" title="0">return ""</span>
}

// getGroupAdmins retrieves the list of group administrators
func getGroupAdmins(groupID int) []m.GroupMember <span class="cov0" title="0">{
        var admins []m.GroupMember

        rows, err := sq.GetDB().Query(`
                SELECT gm.id, gm.group_id, gm.user_id, gm.role, gm.joined_at,
                           u.nickname, u.first_name, u.last_name, COALESCE(u.avatar_url, '') as avatar_url
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ? AND gm.role IN ('creator', 'admin')
                ORDER BY CASE gm.role WHEN 'creator' THEN 1 WHEN 'admin' THEN 2 END, gm.joined_at ASC`,
                groupID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group admins: %v", err)
                return admins
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        for rows.Next() </span><span class="cov0" title="0">{
                var admin m.GroupMember
                err := rows.Scan(&amp;admin.ID, &amp;admin.GroupID, &amp;admin.UserID, &amp;admin.Role, &amp;admin.JoinedAt,
                        &amp;admin.Nickname, &amp;admin.FirstName, &amp;admin.LastName, &amp;admin.AvatarURL)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning admin: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">admins = append(admins, admin)</span>
        }

        <span class="cov0" title="0">return admins</span>
}

// getGroupMembers retrieves group members (with optional limit)
func getGroupMembers(groupID int, limit int) []m.GroupMember <span class="cov0" title="0">{
        var members []m.GroupMember

        query := `
                SELECT gm.id, gm.group_id, gm.user_id, gm.role, gm.joined_at,
                           u.nickname, u.first_name, u.last_name, COALESCE(u.avatar_url, '') as avatar_url
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ?
                ORDER BY gm.joined_at ASC`

        if limit &gt; 0 </span><span class="cov0" title="0">{
                query += fmt.Sprintf(" LIMIT %d", limit)
        }</span>

        <span class="cov0" title="0">rows, err := sq.GetDB().Query(query, groupID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group members: %v", err)
                return members
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        for rows.Next() </span><span class="cov0" title="0">{
                var member m.GroupMember
                err := rows.Scan(&amp;member.ID, &amp;member.GroupID, &amp;member.UserID, &amp;member.Role, &amp;member.JoinedAt,
                        &amp;member.Nickname, &amp;member.FirstName, &amp;member.LastName, &amp;member.AvatarURL)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning member: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">members = append(members, member)</span>
        }

        <span class="cov0" title="0">return members</span>
}

// getGroupEvents retrieves recent events for a group
func getGroupEvents(groupID int, limit int) []m.Event <span class="cov0" title="0">{
        var events []m.Event

        rows, err := sq.GetDB().Query(`
                SELECT e.id, e.group_id, e.creator_id, e.title, e.description, e.event_date, e.created_at,
                           u.first_name || ' ' || u.last_name as creator_name
                FROM events e
                JOIN users u ON e.creator_id = u.id
                WHERE e.group_id = ?
                ORDER BY e.event_date DESC
                LIMIT ?`, groupID, limit)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group events: %v", err)
                return events
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        for rows.Next() </span><span class="cov0" title="0">{
                var event m.Event
                err := rows.Scan(&amp;event.ID, &amp;event.GroupID, &amp;event.CreatorID, &amp;event.Title,
                        &amp;event.Description, &amp;event.EventDate, &amp;event.CreatedAt, &amp;event.CreatorName)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning event: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">events = append(events, event)</span>
        }

        <span class="cov0" title="0">return events</span>
}
</pre>
		
		<pre class="file" id="file11" style="display: none">package handlers

import (
        "encoding/json"
        "fmt"
        "net/http"
        "time"

        sq "imson/pkg/db/sqlite"

        "imson/pkg/services"

        "github.com/google/uuid"
)

var sessionStore = make(map[string]string) // sessionStore is a map that stores session IDs and their corresponding user IDs.

// Login handles user login requests.
// It only accepts POST requests with JSON body containing "email" and "password".
// The function validates the input, authenticates the user via services.AuthenticateUser,
// and if successful, creates a new session ID stored in an in-memory sessionStore.
// A session cookie is then set in the response with HttpOnly and SameSite=Lax attributes.
// On success, it responds with HTTP 200 and a JSON message indicating successful login.
// On failure, it responds with appropriate HTTP error codes and messages.

func LoginHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodPost </span><span class="cov8" title="1">{
                http.Error(w, "Only POST allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">var creds struct {
                Email    string `json:"email"`
                Password string `json:"password"`
        }
        if err := json.NewDecoder(r.Body).Decode(&amp;creds); err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Invalid JSON", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">if creds.Email == "" || creds.Password == "" </span><span class="cov8" title="1">{
                http.Error(w, "Email and password are required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov8" title="1">userID, err := services.AuthenticateUser(creds.Email, creds.Password)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, err.Error(), http.StatusUnauthorized)
                return
        }</span>
        <span class="cov8" title="1">fmt.Println("got here")

        sessionID := uuid.NewString()
        expiresAt := time.Now().Add(24 * time.Hour)
        _, err = sq.GetDB().Exec(`
                INSERT INTO sessions (id, user_id, created_at, expires_at)
                VALUES (?, ?, ?, ?)`,
                sessionID, userID, time.Now(), expiresAt)
        if err != nil </span><span class="cov8" title="1">{
                http.Error(w, fmt.Sprintf("Failed to create session: %v", err), http.StatusInternalServerError)
                return
        }</span>
        <span class="cov8" title="1">sessionStore[sessionID] = userID

        http.SetCookie(w, &amp;http.Cookie{
                Name:     "session_id", // Changed from "session" to "session_id"
                Value:    sessionID,
                Path:     "/",
                HttpOnly: true,
                SameSite: http.SameSiteLaxMode,
        })

        w.WriteHeader(http.StatusOK)
        fmt.Fprintf(w, `{"message":"login successful"} cookie:%s `, sessionID)</span>
}
</pre>
		
		<pre class="file" id="file12" style="display: none">package handlers

import "net/http"



var sessions map[string]struct{}// sessions stores active session data
// Logout handles user logout requests.
// It checks for a "session" cookie in the request,
// and if found, deletes the corresponding session from the sessions map,
// then invalidates the cookie by setting its MaxAge to -1 and sending it back to the client.
// Finally, it responds with HTTP 200 and a JSON message indicating successful logout.
func LogoutHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        cookie, err := r.Cookie("session")
        if err == nil </span><span class="cov8" title="1">{
                delete(sessions, cookie.Value)
                cookie.MaxAge = -1
                http.SetCookie(w, cookie)
        }</span>
        <span class="cov8" title="1">w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"message":"logout successful"}`))</span>
}
</pre>
		
		<pre class="file" id="file13" style="display: none">package handlers

import (
        "encoding/json"
        "fmt"
        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        "log"
        "net/http"
        "strconv"
        "time"
)

var DB = sq.GetDB()

// FetchMessagesHandler retrieves messages between the logged-in user and the specified user
func FetchMessagesHandler(w http.ResponseWriter, r *http.Request, userId string) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">query := r.URL.Query()
        offsetStr := query.Get("offset")
        limitStr := query.Get("limit")

        offset, err := strconv.Atoi(offsetStr)
        if err != nil </span><span class="cov0" title="0">{
                offset = 0
        }</span>
        <span class="cov0" title="0">limit, err := strconv.Atoi(limitStr)
        if err != nil </span><span class="cov0" title="0">{
                limit = 10
        }</span>

        <span class="cov0" title="0">messages, err := FetchMessages(r, userId, offset, limit)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Failed to fetch messages", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.WriteHeader(http.StatusOK)
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(messages)</span>
}

// FetchMessages retrieves paginated messages between the logged-in user and the specified user
func FetchMessages(r *http.Request, userId string, offset, limit int) ([]m.Message, error) <span class="cov0" title="0">{
        currentUserID, err := GetCurrentUserID(r, DB)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // Query only the required slice of messages directly from the database
        <span class="cov0" title="0">rows, err := DB.Query(`
                SELECT id, sender_id, receiver_id, content, created_at, read 
                FROM messages 
                WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
                ORDER BY created_at DESC`,
                currentUserID, userId, userId, currentUserID,
        )
        if err != nil </span><span class="cov0" title="0">{
                fmt.Println("Error fetching messages:", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var messages []m.Message
        for rows.Next() </span><span class="cov0" title="0">{
                var message m.Message
                if err := rows.Scan(&amp;message.ID, &amp;message.SenderID, &amp;message.ReceiverID, &amp;message.Content, &amp;message.Timestamp, &amp;message.ReadStatus); err != nil </span><span class="cov0" title="0">{
                        fmt.Println("Error scanning message:", err)
                        return nil, err
                }</span>
                <span class="cov0" title="0">message.CurrentUserId = currentUserID
                messages = append(messages, message)</span>
        }

        <span class="cov0" title="0">return finalMessages(messages, limit, offset), nil</span>
}

// this function will be responsible to do the calculation of the returning messages
func finalMessages(msgs []m.Message, limit, offset int) []m.Message <span class="cov8" title="1">{
        var fnlMsgs []m.Message
        limit += offset
        if limit &gt; len(msgs) </span><span class="cov8" title="1">{
                limit = len(msgs)
        }</span>
        <span class="cov8" title="1">if offset &gt;= limit || offset &gt; len(msgs) </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">fnlMsgs = msgs[offset:limit]
        return fnlMsgs</span>
}

var Sender_id, Receiver_id string

// this function is responsible for handling a newly created message from the frontend to the backend for updating the database
func SendMessageHandler(w http.ResponseWriter, r *http.Request, recipientID string) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">var req struct {
                Content string `json:"content"`
        }

        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request body", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">if req.Content == "" </span><span class="cov0" title="0">{
                http.Error(w, "Message content cannot be empty", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">senderID, err := GetCurrentUserID(r, DB)
        Sender_id = senderID
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // THROTTLE CHECK
        <span class="cov0" title="0">const maxMessages = 5
        const windowSeconds = 10

        now := time.Now()
        windowStart := now.Add(-time.Duration(windowSeconds) * time.Second)

        var count int
        err = DB.QueryRow(`
                SELECT COUNT(*) FROM message_throttle
                WHERE user_id = ? AND timestamp &gt;= ?
        `, senderID, windowStart).Scan(&amp;count)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">if count &gt;= maxMessages </span><span class="cov0" title="0">{
                w.WriteHeader(http.StatusTooManyRequests)
                http.Error(w, "Rate limit exceeded. Please wait before sending more messages.", http.StatusTooManyRequests)
                return
        }</span>

        <span class="cov0" title="0">result, err := DB.Exec(
                `INSERT INTO messages (sender_id, receiver_id, content, created_at) VALUES (?, ?, ?, ?)`,
                senderID, recipientID, req.Content, now,
        )
        Receiver_id = recipientID
        if err != nil </span><span class="cov0" title="0">{
                fmt.Printf("%v", err)
                http.Error(w, "Failed to store message", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">_, err = DB.Exec(`INSERT INTO message_throttle (user_id, timestamp) VALUES (?, ?)`, senderID, now)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Throttle logging failed: %v", err)
        }</span>

        <span class="cov0" title="0">messageID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Failed to retrieve message ID", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">message := m.Message{
                ID:         int(messageID),
                SenderID:   senderID,
                ReceiverID: recipientID,
                Content:    req.Content,
                Timestamp:  now,
        }

        w.WriteHeader(http.StatusOK)
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(message)</span>
}

// LatestPrivateMessage fetches the most recent private message from the database.
func LatestPrivateMessage() (m.Message, error) <span class="cov8" title="1">{

        var msg m.Message
        row := DB.QueryRow(`
                SELECT id, sender_id, receiver_id, content, created_at
                FROM messages
                WHERE sender_id = ? AND receiver_id = ?
                ORDER BY created_at DESC
                LIMIT 1
        `, Sender_id, Receiver_id)

        err := row.Scan(&amp;msg.ID, &amp;msg.SenderID, &amp;msg.ReceiverID, &amp;msg.Content, &amp;msg.Timestamp)
        if err != nil </span><span class="cov8" title="1">{
                return m.Message{}, err
        }</span>
        <span class="cov8" title="1">msg.CurrentUserId = Sender_id
        return msg, nil</span>
}

// read messages patches to the databse, the messages that have been read and updates their value of read to 1
func ReadMessagesHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        // if r.Method != http.MethodPatch {
        //         http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
        //         return
        // }
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">var payload struct {
                Subjects []string `json:"parties"`
        }

        if err := json.NewDecoder(r.Body).Decode(&amp;payload); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request format", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">stmt, err := DB.Prepare(`
        UPDATE messages
        SET read = 1
        WHERE ((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))`)

        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Failed to prepare statement: %v", err)
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer stmt.Close()

        if _, err := stmt.Exec(payload.Subjects[0], payload.Subjects[1], payload.Subjects[1], payload.Subjects[0]); err != nil </span><span class="cov0" title="0">{
                log.Printf("Failed to update message as read for %s: %v", payload.Subjects[0], err)
                http.Error(w, "Failed to update messages", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"status":"messages marked as read"}`))</span>
}
</pre>
		
		<pre class="file" id="file14" style="display: none">package handlers

import (
        "encoding/json"
        "log"
        "net/http"
        "strconv"

        m "imson/pkg/models"
        sq "imson/pkg/db/sqlite"
        "imson/pkg/services"
)

var notificationService *services.NotificationService

// InitNotificationService initializes the notification service after database is ready
func InitNotificationService() <span class="cov0" title="0">{
        notificationService = services.NewNotificationService()
}</span>

// GetNotificationsHandler retrieves notifications for the current user
func GetNotificationsHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Parse query parameters
        <span class="cov0" title="0">limitStr := r.URL.Query().Get("limit")
        offsetStr := r.URL.Query().Get("offset")

        limit := 20 // default
        if limitStr != "" </span><span class="cov0" title="0">{
                if l, err := strconv.Atoi(limitStr); err == nil &amp;&amp; l &gt; 0 </span><span class="cov0" title="0">{
                        limit = l
                }</span>
        }

        <span class="cov0" title="0">offset := 0 // default
        if offsetStr != "" </span><span class="cov0" title="0">{
                if o, err := strconv.Atoi(offsetStr); err == nil &amp;&amp; o &gt;= 0 </span><span class="cov0" title="0">{
                        offset = o
                }</span>
        }

        <span class="cov0" title="0">notifications, err := notificationService.GetUserNotifications(userID, limit, offset)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting notifications: %v", err)
                http.Error(w, "Failed to get notifications", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(notifications)</span>
}

// GetUnreadNotificationCountHandler gets the count of unread notifications
func GetUnreadNotificationCountHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodGet </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">count, err := notificationService.GetUnreadNotificationCount(userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting unread notification count: %v", err)
                http.Error(w, "Failed to get notification count", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">response := map[string]int{"unread_count": count}
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(response)</span>
}

// MarkNotificationsReadHandler marks notifications as read
func MarkNotificationsReadHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodPost </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">var req m.MarkNotificationsReadRequest
        if err := json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid request format", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">if len(req.NotificationIDs) == 0 </span><span class="cov0" title="0">{
                // Mark all notifications as read
                err = notificationService.MarkAllNotificationsAsRead(userID)
        }</span> else<span class="cov0" title="0"> {
                // Mark specific notifications as read
                err = notificationService.MarkNotificationsAsRead(userID, req.NotificationIDs)
        }</span>

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error marking notifications as read: %v", err)
                http.Error(w, "Failed to mark notifications as read", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{"status": "success"})</span>
}

// DeleteNotificationHandler deletes a notification
func DeleteNotificationHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        if r.Method != http.MethodDelete </span><span class="cov0" title="0">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov0" title="0">db := sq.GetDB()
        userID, err := GetCurrentUserID(r, db)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">notificationIDStr := r.URL.Query().Get("id")
        if notificationIDStr == "" </span><span class="cov0" title="0">{
                http.Error(w, "Notification ID is required", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">notificationID, err := strconv.Atoi(notificationIDStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid notification ID", http.StatusBadRequest)
                return
        }</span>

        <span class="cov0" title="0">err = notificationService.DeleteNotification(userID, notificationID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error deleting notification: %v", err)
                http.Error(w, "Failed to delete notification", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]string{"status": "success"})</span>
}

// BroadcastNotificationToUser sends a real-time notification via WebSocket
func BroadcastNotificationToUser(userID string, notification *m.Notification) <span class="cov0" title="0">{
        clientsMutex.RLock()
        client, exists := clients[userID]
        clientsMutex.RUnlock()

        if exists </span><span class="cov0" title="0">{
                wsMessage := map[string]interface{}{
                        "type": "notification",
                        "data": notification,
                }

                notificationJSON, err := json.Marshal(wsMessage)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error marshaling notification: %v", err)
                        return
                }</span>

                <span class="cov0" title="0">select </span>{
                case client.SendCh &lt;- notificationJSON:<span class="cov0" title="0"></span>
                        // Notification sent successfully
                default:<span class="cov0" title="0">
                        // Client send channel is full, skip notification
                        log.Printf("Client send channel full for user %s", userID)</span>
                }
        }
}

// Helper function to create and broadcast a notification
func CreateAndBroadcastNotification(userID, notificationType, title, message string, data interface{}) <span class="cov0" title="0">{
        notification, err := notificationService.CreateNotification(userID, notificationType, title, message, data)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error creating notification: %v", err)
                return
        }</span>

        // Broadcast the notification in real-time
        <span class="cov0" title="0">BroadcastNotificationToUser(userID, notification)</span>
}</pre>
		
		<pre class="file" id="file15" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "fmt"
        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        ut "imson/pkg/utils"
        "log"
        "net/http"
        "strconv"
        "time"
)

var PostID_posting int

// / POST HANDLER
// This function handler fetches posts from the databasae filtered by a a specific catogory and martialed into a json and swnt to the frontend
func GetPostsHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{

        cookieValue, ok := ValidateSession(r)
        var session m.Session
        err := sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookieValue).
                Scan(&amp;session.ID, &amp;session.UserID, &amp;session.CreatedAt, &amp;session.ExpiresAt)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        <span class="cov0" title="0">if ok </span><span class="cov0" title="0">{

                if r.Method == http.MethodGet </span><span class="cov0" title="0">{
                        category := r.URL.Query().Get("category")

                        posts, err := FetchPostsFrmDb(category)
                        if err != nil </span><span class="cov0" title="0">{
                                http.Error(w, "Internal Server Error", http.StatusInternalServerError)
                                return
                        }</span>

                        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
                        json.NewEncoder(w).Encode(posts)</span>
                } else<span class="cov0" title="0"> if r.Method == http.MethodPost </span><span class="cov0" title="0">{
                        var postData struct {
                                Title    string   `json:"title"`
                                Content  string   `json:"content"`
                                Category []string `json:"category"`
                        }

                        if err := json.NewDecoder(r.Body).Decode(&amp;postData); err != nil </span><span class="cov0" title="0">{
                                http.Error(w, "Invalid request format", http.StatusBadRequest)
                                return
                        }</span>

                        <span class="cov0" title="0">if postData.Title == "" || postData.Content == "" || len(postData.Category) == 0 </span><span class="cov0" title="0">{
                                http.Error(w, "Title, content, and category are required", http.StatusBadRequest)
                                return
                        }</span>

                        <span class="cov0" title="0">result, err := sq.GetDB().Exec(`
                                INSERT INTO posts (user_id, title, content, category, created_at) 
                                VALUES (?, ?, ?, ?, ?)`,
                                session.UserID, postData.Title, postData.Content, ut.JoinCategories(postData.Category), time.Now())
                        if err != nil </span><span class="cov0" title="0">{
                                log.Printf("Error creating post: %v", err)
                                http.Error(w, "Error creating post", http.StatusInternalServerError)
                                return
                        }</span>

                        <span class="cov0" title="0">postID, err := result.LastInsertId()
                        PostID_posting = int(postID)
                        if err != nil </span><span class="cov0" title="0">{
                                log.Printf("Error getting post ID: %v", err)
                                http.Error(w, "Error creating post", http.StatusInternalServerError)
                                return
                        }</span>

                        <span class="cov0" title="0">post, _ := FetchLatestPostFromDb(int(postID))

                        w.Header().Set("Content-Type", "application/json")
                        w.WriteHeader(http.StatusCreated)
                        json.NewEncoder(w).Encode(post)</span>
                } else<span class="cov0" title="0"> {
                        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                }</span>
        }
}

// The function fetches a particular group of posts based on a specific category from the database
func FetchPostsFrmDb(category string) ([]m.Post, error) <span class="cov0" title="0">{

        query := `
        SELECT p.id, p.title, p.content, p.likes, p.comment_count, p.category, p.created_at, p.user_id,
                   u.nickname as author_nickname, u.first_name as author_first_name, u.last_name as author_last_name,
                   u.gender as author_gender
        FROM posts p
        JOIN users u ON p.user_id = u.id
`
        if category != "" &amp;&amp; category != "all" </span><span class="cov0" title="0">{
                query += " WHERE p.category = ?"
        }</span>
        <span class="cov0" title="0">query += " ORDER BY p.created_at DESC"

        var (
                rows *sql.Rows
                err  error
        )
        if category != "" &amp;&amp; category != "all" </span><span class="cov0" title="0">{
                rows, err = sq.GetDB().Query(query, category)
        }</span> else<span class="cov0" title="0"> {
                rows, err = sq.GetDB().Query(query)
        }</span>
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error querying posts: %v", err)
                return []m.Post{}, nil
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var posts []m.Post
        for rows.Next() </span><span class="cov0" title="0">{
                var post m.Post
                err := rows.Scan(
                        &amp;post.ID,
                        &amp;post.Title,
                        &amp;post.Content,
                        &amp;post.Likes,
                        &amp;post.CommentCount,
                        &amp;post.Category,
                        &amp;post.CreatedAt,
                        &amp;post.UserID,
                        &amp;post.AuthorNickname,
                        &amp;post.AuthorFirstName,
                        &amp;post.AuthorLastName,
                        &amp;post.AuthorGender,
                )
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning post: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">posts = append(posts, post)</span>
        }
        <span class="cov0" title="0">return posts, nil</span>
}

// The function fetches a post from the database based on the particular post id. It is then martialled into a json object and sent to the  frontend
func FetchLatestPostFromDb(postId int) (m.Post, error) <span class="cov0" title="0">{

        var post m.Post
        err := sq.GetDB().QueryRow(`
                SELECT p.id, p.title, p.content, p.category, p.created_at, p.user_id,
                           u.nickname as author_nickname, u.first_name as author_first_name, u.last_name as author_last_name,
                           u.gender as author_gender
                FROM posts p
                JOIN users u ON p.user_id = u.id
                WHERE p.id = ?`,
                postId).
                Scan(&amp;post.ID, &amp;post.Title, &amp;post.Content, &amp;post.Category, &amp;post.CreatedAt, &amp;post.UserID,
                        &amp;post.AuthorNickname, &amp;post.AuthorFirstName, &amp;post.AuthorLastName, &amp;post.AuthorGender)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error retrieving created post: %v", err)
                return m.Post{}, err
        }</span>

        <span class="cov0" title="0">return post, nil</span>
}

// The function handles liking a post and registering in the database
func LikePostHandler(w http.ResponseWriter, r *http.Request, postIdStr string) <span class="cov0" title="0">{

        cookieValue, ok := ValidateSession(r)
        postID, err := strconv.Atoi(postIdStr)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Invalid post ID", http.StatusBadRequest)
                return
        }</span>
        <span class="cov0" title="0">var (
                userId      string
                likePostObj m.LikePostObject
        )
        likePostObj.PostID = postID
        if ok </span><span class="cov0" title="0">{

                err = sq.GetDB().QueryRow(`
                SELECT user_id 
                FROM sessions 
                WHERE id = ?`, cookieValue).Scan(&amp;userId)
                if err != nil </span><span class="cov0" title="0">{
                        if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                                http.Error(w, "User not found", http.StatusNotFound)
                                fmt.Println("User not found for userID:", cookieValue)
                        }</span> else<span class="cov0" title="0"> {
                                http.Error(w, "Failed to fetch user id", http.StatusInternalServerError)
                                fmt.Println("Error querying user id:", err)
                        }</span>
                        <span class="cov0" title="0">return</span>
                }
                <span class="cov0" title="0">found, _ := checkUserLikedPost(postID, userId)
                if found </span><span class="cov0" title="0">{
                        // Unlike the post - no notification needed
                        err = removeRowFromTable("posts_likes", postID, userId)
                        likePostObj.Likes, _ = GetPostLikes(postID)
                        if err != nil </span><span class="cov0" title="0">{
                                http.Error(w, "Failed to unlike post", http.StatusInternalServerError)
                                return
                        }</span>
                } else<span class="cov0" title="0"> {
                        // Like the post
                        err := incrementPostLikes(postID, userId)
                        likePostObj.Likes, _ = GetPostLikes(postID)
                        if err != nil </span><span class="cov0" title="0">{
                                http.Error(w, "Failed to retrieve like count", http.StatusInternalServerError)
                                return
                        }</span>

                        // Get post author ID for notification
                        <span class="cov0" title="0">var postAuthorID string
                        err = sq.GetDB().QueryRow("SELECT user_id FROM posts WHERE id = ?", postID).Scan(&amp;postAuthorID)
                        if err != nil </span><span class="cov0" title="0">{
                                log.Printf("Error getting post author: %v", err)
                        }</span> else<span class="cov0" title="0"> if postAuthorID != userId </span>{<span class="cov0" title="0">
                                // insert function call to create notification for post author (if not liking own post)
                        }</span>
                }
        }
        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        json.NewEncoder(w).Encode(likePostObj)</span>
}

// function to check for the number of likes a post has
func GetPostLikes(postID int) (int, error) <span class="cov0" title="0">{
        var likes int
        err := sq.GetDB().QueryRow("SELECT likes FROM posts WHERE id = ?", postID).Scan(&amp;likes)
        if err != nil </span><span class="cov0" title="0">{
                return 0, err
        }</span>
        <span class="cov0" title="0">return likes, nil</span>
}

// function to check whether the user had liked a post
func checkUserLikedPost(postID int, userID string) (bool, error) <span class="cov0" title="0">{
        query := `SELECT COUNT(*) FROM posts_likes WHERE post_id = ? AND user_id = ?`
        var count int

        err := sq.GetDB().QueryRow(query, postID, userID).Scan(&amp;count)
        if err != nil </span><span class="cov0" title="0">{
                return false, err
        }</span>

        <span class="cov0" title="0">if count &gt; 0 </span><span class="cov0" title="0">{
                return true, nil
        }</span>
        <span class="cov0" title="0">return false, nil</span>
}

// The function  removes a like by deleting a particular row from the databse
func removeRowFromTable(tableName string, postID int, userID string) error <span class="cov0" title="0">{
        query := fmt.Sprintf("DELETE FROM %s WHERE post_id = ? AND user_id = ?", tableName)

        _, err := sq.GetDB().Exec(query, postID, userID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to remove row from table %s: %v", tableName, err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// The funciton increaments the count of the likes of a post in the database
func incrementPostLikes(postID int, userID string) error <span class="cov0" title="0">{

        tx, err := sq.GetDB().Begin()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to begin transaction: %v", err)
        }</span>

        <span class="cov0" title="0">query := `UPDATE posts SET likes = likes + 1 WHERE id = ?`
        _, err = tx.Exec(query, postID)
        if err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return fmt.Errorf("failed to increment likes: %v", err)
        }</span>

        <span class="cov0" title="0">query = `INSERT INTO posts_likes (post_id, user_id) VALUES (?, ?)`
        _, err = tx.Exec(query, postID, userID)
        if err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return fmt.Errorf("failed to add row to posts_likes table: %v", err)
        }</span>

        <span class="cov0" title="0">err = tx.Commit()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to commit transaction: %v", err)
        }</span>
        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file16" style="display: none">package handlers

import (
        "database/sql"
        "encoding/json"
        "log"
        "net/http"
        "time"

        "github.com/gorilla/mux"
        db "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// Helper functions for common operations
func writeJSON(w http.ResponseWriter, status int, data interface{}) <span class="cov8" title="1">{
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(status)
        if err := json.NewEncoder(w).Encode(data); err != nil </span><span class="cov0" title="0">{
                log.Printf("Error encoding JSON: %v", err)
                http.Error(w, "Internal server error", http.StatusInternalServerError)
        }</span>
}

func handleDBError(w http.ResponseWriter, err error, operation string) bool <span class="cov8" title="1">{
        if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                http.Error(w, "Profile not found", http.StatusNotFound)
                return true
        }</span>
        <span class="cov8" title="1">if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error %s: %v", operation, err)
                http.Error(w, "Internal server error", http.StatusInternalServerError)
                return true
        }</span>
        <span class="cov8" title="1">return false</span>
}

// CreateProfileHandler handles POST requests to create a new profile
func CreateProfileHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        var profile m.Profile
        if err := json.NewDecoder(r.Body).Decode(&amp;profile); err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Invalid request body", http.StatusBadRequest)
                return
        }</span>

        // Validate required fields
        <span class="cov8" title="1">if profile.Username == "" || profile.Email == "" </span><span class="cov8" title="1">{
                http.Error(w, "Username and email are required", http.StatusBadRequest)
                return
        }</span>

        // Create profile in database
        <span class="cov0" title="0">now := time.Now()
        result, err := db.GetDB().Exec(`
                INSERT INTO profiles (username, email, bio, avatar_url, location, website, is_private, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                profile.Username, profile.Email, profile.Bio, profile.AvatarURL,
                profile.Location, profile.Website, profile.IsPrivate, now, now)

        if handleDBError(w, err, "creating profile") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">id, err := result.LastInsertId()
        if handleDBError(w, err, "getting last insert id") </span><span class="cov0" title="0">{
                return
        }</span>

        // Return the created profile
        <span class="cov0" title="0">profile.ID = int(id)
        profile.CreatedAt = now
        profile.UpdatedAt = now
        writeJSON(w, http.StatusCreated, profile)</span>
}

// GetProfileHandler handles GET requests for user profile
func GetProfileHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        userID := mux.Vars(r)["id"]

        var profile m.Profile
        err := db.GetDB().QueryRow(`
                SELECT id, username, email, bio, avatar_url, location, website, is_private, created_at, updated_at
                FROM profiles WHERE id = ?`, userID).Scan(
                &amp;profile.ID, &amp;profile.Username, &amp;profile.Email, &amp;profile.Bio,
                &amp;profile.AvatarURL, &amp;profile.Location, &amp;profile.Website,
                &amp;profile.IsPrivate, &amp;profile.CreatedAt, &amp;profile.UpdatedAt)

        if handleDBError(w, err, "getting profile") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">writeJSON(w, http.StatusOK, profile)</span>
}

// GetProfilePageHandler handles GET requests for profile page data
func GetProfilePageHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        userID := mux.Vars(r)["id"]

        var profile m.Profile
        err := db.GetDB().QueryRow(`
                SELECT
                        p.id, p.username, p.email, p.bio, p.avatar_url, p.location, p.website, p.is_private, p.created_at, p.updated_at,
                        COALESCE(follower_count.count, 0), COALESCE(following_count.count, 0), COALESCE(posts_count.count, 0)
                FROM profiles p
                LEFT JOIN (SELECT followed_id, COUNT(*) as count FROM followers WHERE followed_id = ? GROUP BY followed_id) follower_count ON p.id = follower_count.followed_id
                LEFT JOIN (SELECT follower_id, COUNT(*) as count FROM followers WHERE follower_id = ? GROUP BY follower_id) following_count ON p.id = following_count.follower_id
                LEFT JOIN (SELECT user_id, COUNT(*) as count FROM posts WHERE user_id = ? GROUP BY user_id) posts_count ON p.id = posts_count.user_id
                WHERE p.id = ?`, userID, userID, userID, userID).Scan(
                &amp;profile.ID, &amp;profile.Username, &amp;profile.Email, &amp;profile.Bio,
                &amp;profile.AvatarURL, &amp;profile.Location, &amp;profile.Website, &amp;profile.IsPrivate,
                &amp;profile.CreatedAt, &amp;profile.UpdatedAt, &amp;profile.Followers, &amp;profile.Following, &amp;profile.PostsCount)

        if handleDBError(w, err, "getting profile page data") </span><span class="cov0" title="0">{
                return
        }</span>

        // Add social links if available
        <span class="cov0" title="0">if profile.Website != "" </span><span class="cov0" title="0">{
                profile.SocialLinks = []m.SocialLink{{Platform: "Website", URL: profile.Website}}
        }</span>

        <span class="cov0" title="0">writeJSON(w, http.StatusOK, profile)</span>
}

// UpdateProfileHandler handles PUT requests to update user profile
func UpdateProfileHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        userID := mux.Vars(r)["id"]

        var profile m.Profile
        if err := json.NewDecoder(r.Body).Decode(&amp;profile); err != nil </span><span class="cov8" title="1">{
                http.Error(w, "Invalid request body", http.StatusBadRequest)
                return
        }</span>

        // Update profile in database
        <span class="cov0" title="0">result, err := db.GetDB().Exec(`
                UPDATE profiles
                SET username = ?, email = ?, bio = ?, avatar_url = ?, location = ?, website = ?, is_private = ?, updated_at = ?
                WHERE id = ?`,
                profile.Username, profile.Email, profile.Bio, profile.AvatarURL,
                profile.Location, profile.Website, profile.IsPrivate, time.Now(), userID)

        if handleDBError(w, err, "updating profile") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 </span><span class="cov0" title="0">{
                http.Error(w, "Profile not found", http.StatusNotFound)
                return
        }</span>

        <span class="cov0" title="0">writeJSON(w, http.StatusOK, profile)</span>
}

// DeleteProfileHandler handles DELETE requests to remove a profile
func DeleteProfileHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        userID := mux.Vars(r)["id"]

        result, err := db.GetDB().Exec(`DELETE FROM profiles WHERE id = ?`, userID)
        if handleDBError(w, err, "deleting profile") </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 </span><span class="cov0" title="0">{
                http.Error(w, "Profile not found", http.StatusNotFound)
                return
        }</span>

        <span class="cov0" title="0">w.WriteHeader(http.StatusNoContent)</span>
}


</pre>
		
		<pre class="file" id="file17" style="display: none">package handlers

import (
        "database/sql"
        "fmt"
        "io"
        "net/http"
        "os"
        "path/filepath"
        "strings"

        sq "imson/pkg/db/sqlite"

        "github.com/google/uuid"
        "golang.org/x/crypto/bcrypt"
)

// Registration handler handles the user registration process from the client.
// It validates the input, hashes the password, handles the uploaded avatar
// and stores the user in the database.
func RegisterHandler(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        if r.Method != http.MethodPost </span><span class="cov8" title="1">{
                http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
                return
        }</span>

        <span class="cov8" title="1">r.Body = http.MaxBytesReader(w, r.Body, 20&lt;&lt;20)
        if err := r.ParseMultipartForm(20 &lt;&lt; 20); err != nil </span><span class="cov8" title="1">{
                http.Error(w, "File too large or invalid form", http.StatusBadRequest)
                fmt.Println("Error parsing form:", err)
                return
        }</span>

        // Collect form values
        <span class="cov8" title="1">first := r.FormValue("firstName")
        last := r.FormValue("lastName")
        nickname := r.FormValue("nickname")
        dob := r.FormValue("dateOfBirth")
        about := r.FormValue("aboutMe")
        gender := r.FormValue("gender")
        email := r.FormValue("email")
        pwd := r.FormValue("password")

        // Basic validation
        if first == "" || last == "" || gender == "" || email == "" || pwd == "" </span><span class="cov8" title="1">{
                http.Error(w, "Missing required fields", http.StatusBadRequest)
                return
        }</span>

        // Hash password
        <span class="cov8" title="1">hashedPassword, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Error processing password", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov8" title="1">userID := uuid.New().String()
        avatarURL := sql.NullString{Valid: false}

        // Handle avatar upload if provided
        file, header, err := r.FormFile("avatar")
        if err == nil </span><span class="cov8" title="1">{
                defer file.Close()

                folder := filepath.Join("images", "profiles", first+last)
                if err := os.MkdirAll(folder, 0755); err != nil </span><span class="cov0" title="0">{
                        fmt.Println("Error creating folders:", err)
                        http.Error(w, "Error creating folders", http.StatusInternalServerError)
                        return
                }</span>

                <span class="cov8" title="1">ext := filepath.Ext(header.Filename)
                base := strings.TrimSuffix(header.Filename, ext)
                destPath := filepath.Join(folder, base+"-"+userID+ext)
                finalPath := filepath.Join("backend", destPath)

                dst, err := os.Create(destPath)
                if err != nil </span><span class="cov0" title="0">{
                        fmt.Println("Error creating file:", err)
                        http.Error(w, "Error saving file", http.StatusInternalServerError)
                        return
                }</span>
                <span class="cov8" title="1">defer dst.Close()

                if _, err := io.Copy(dst, file); err != nil </span><span class="cov0" title="0">{
                        fmt.Println("Error writing file:", err)
                        http.Error(w, "Error writing file", http.StatusInternalServerError)
                        return
                }</span>

                <span class="cov8" title="1">avatarURL = sql.NullString{String: finalPath, Valid: true}</span>
        }

        // Save to DB
        <span class="cov8" title="1">_, err = sq.GetDB().Exec(`
    INSERT INTO users (
        id, nickname, first_name, last_name, gender, email, password,
        date_of_birth, avatar_url, about_me
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                userID, nickname, first, last, gender, email,
                string(hashedPassword), dob, avatarURL, about,
        )
        if err != nil </span><span class="cov8" title="1">{
                fmt.Println("Error inserting user:", err)
                http.Error(w, "Error creating user", http.StatusInternalServerError)
                return
        }</span>

        <span class="cov8" title="1">w.WriteHeader(http.StatusCreated)
        fmt.Fprintf(w, "Registered %s successfully", nickname)</span>
}
</pre>
		
		<pre class="file" id="file18" style="display: none">package handlers

import (
        "database/sql"
        "log"
        "net/http"
        "time"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
)

// ValidateSession checks if the session cookie is valid and not expired.
// It returns the session ID and a boolean indicating if the session is valid.
func ValidateSession(r *http.Request) (string, bool) <span class="cov0" title="0">{
        cookie, err := r.Cookie("session_id")
        if err != nil </span><span class="cov0" title="0">{
                return "", false
        }</span>

        // Verify session exists in database
        <span class="cov0" title="0">var session m.Session
        err = sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookie.Value).
                Scan(&amp;session.ID, &amp;session.UserID, &amp;session.CreatedAt, &amp;session.ExpiresAt)
        if err != nil </span><span class="cov0" title="0">{
                return "", false
        }</span>

        // Check if session is expired
        <span class="cov0" title="0">if time.Now().After(session.ExpiresAt) </span><span class="cov0" title="0">{
                // Delete expired session
                _, err = sq.GetDB().Exec("DELETE FROM sessions WHERE id = ?", session.ID)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error deleting expired session: %v", err)
                }</span>
                <span class="cov0" title="0">return "", false</span>
        }
        <span class="cov0" title="0">return cookie.Value, true</span>
}
// This function retrieves the current logged in user and returns the user id as a string
func GetCurrentUserID(r *http.Request, db *sql.DB) (string, error) <span class="cov8" title="1">{
        cookie, err := r.Cookie("session")
        if err != nil </span><span class="cov8" title="1">{
                return "", err
        }</span>

        <span class="cov8" title="1">sessionID := cookie.Value
        var userID string
        err = db.QueryRow(`
        SELECT user_id 
        FROM sessions 
        WHERE id = ?`, sessionID).Scan(&amp;userID)
        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return "", http.ErrNoCookie
                }</span>
                <span class="cov0" title="0">return "", err</span>
        }
        <span class="cov8" title="1">return userID, nil</span>
}
</pre>
		
		<pre class="file" id="file19" style="display: none">package handlers

import (
        "encoding/json"
        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        "net/http"
)

// GetUserPosts handles fetching posts created by a specific user
func GetUserPosts(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        // Get user ID from session
        userID, _ := GetCurrentUserID(r, DB)
        if userID == "" </span><span class="cov0" title="0">{
                http.Error(w, "Unauthorized", http.StatusUnauthorized)
                return
        }</span>

        // Query posts from database
        <span class="cov0" title="0">rows, err := sq.GetDB().Query(`
                SELECT p.id, p.title, p.content, p.category, p.created_at, p.user_id,
                           u.nickname as author_nickname, u.first_name as author_first_name, 
                           u.last_name as author_last_name, u.gender as author_gender,
                           (SELECT COUNT(*) FROM comments WHERE post_id = p.id) as comment_count,
                           p.likes
                FROM posts p
                JOIN users u ON p.user_id = u.id
                WHERE p.user_id = ?
                ORDER BY p.created_at DESC
        `, userID)
        if err != nil </span><span class="cov0" title="0">{
                http.Error(w, "Database error", http.StatusInternalServerError)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var posts []m.Post
        for rows.Next() </span><span class="cov0" title="0">{
                var post m.Post
                err := rows.Scan(
                        &amp;post.ID,
                        &amp;post.Title,
                        &amp;post.Content,
                        &amp;post.Category,
                        &amp;post.CreatedAt,
                        &amp;post.UserID,
                        &amp;post.AuthorNickname,
                        &amp;post.AuthorFirstName,
                        &amp;post.AuthorLastName,
                        &amp;post.AuthorGender,
                        &amp;post.CommentCount,
                        &amp;post.Likes,
                )
                if err != nil </span><span class="cov0" title="0">{
                        http.Error(w, "Error scanning posts", http.StatusInternalServerError)
                        return
                }</span>
                <span class="cov0" title="0">posts = append(posts, post)</span>
        }

        // Return posts as JSON
        <span class="cov0" title="0">w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(posts)</span>
}
</pre>
		
		<pre class="file" id="file20" style="display: none">package handlers

import (
        "encoding/json"
        "log"
        "strconv"
        "time"

        sq "imson/pkg/db/sqlite"
        m "imson/pkg/models"
        u "imson/pkg/utils"
)

// SendNotificationToUser sends a notification to a specific user via WebSocket
func SendNotificationToUser(userID string, notification map[string]interface{}) <span class="cov8" title="1">{
        clientsMutex.RLock()
        client, exists := clients[userID]
        clientsMutex.RUnlock()

        if exists </span><span class="cov8" title="1">{
                // Wrap notification in WebSocket message format
                wsMessage := map[string]interface{}{
                        "type": notification["type"],
                        "data": notification,
                }

                notificationJSON, err := json.Marshal(wsMessage)
                if err != nil </span><span class="cov8" title="1">{
                        return
                }</span>

                <span class="cov8" title="1">select </span>{
                case client.SendCh &lt;- notificationJSON:<span class="cov8" title="1"></span>
                        // Notification sent successfully
                default:<span class="cov8" title="1"></span>
                        // Client send channel is full, skip notification
                }
        }
}

// BroadcastFollowRequestNotification sends a follow request notification to the target user
func BroadcastFollowRequestNotification(targetUserID, senderID int, senderName string, requestID int) <span class="cov8" title="1">{
        notification := map[string]interface{}{
                "type":       "follow_request",
                "message":    senderName + " wants to follow you",
                "sender_id":  senderID,
                "request_id": requestID,
                "timestamp":  getCurrentTimestamp(),
        }

        SendNotificationToUser(strconv.Itoa(targetUserID), notification)
}</span>

// BroadcastFollowAcceptedNotification sends acceptance notification to the original requester
func BroadcastFollowAcceptedNotification(requesterID, accepterID int, accepterName string) <span class="cov8" title="1">{
        notification := map[string]interface{}{
                "type":        "follow_accepted",
                "message":     accepterName + " accepted your follow request",
                "accepter_id": accepterID,
                "timestamp":   getCurrentTimestamp(),
        }

        SendNotificationToUser(strconv.Itoa(requesterID), notification)
}</span>

// BroadcastFollowDeclinedNotification sends decline notification to the original requester
func BroadcastFollowDeclinedNotification(requesterID, declinerID int, declinerName string) <span class="cov8" title="1">{
        notification := map[string]interface{}{
                "type":        "follow_declined",
                "message":     declinerName + " declined your follow request",
                "decliner_id": declinerID,
                "timestamp":   getCurrentTimestamp(),
        }

        SendNotificationToUser(strconv.Itoa(requesterID), notification)
}</span>

// handlers for private messages
func handlePrivateMsg(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                Content string `json:"content"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse private message payload:", err)
                return
        }</span>

        <span class="cov0" title="0">message, err := LatestPrivateMessage()
        if err != nil </span><span class="cov0" title="0">{
                log.Println("Error fetching latest private message:", err)
        }</span>

        <span class="cov0" title="0">defer clientsMutex.RUnlock()

        if receiverClient, ok := clients[message.ReceiverID]; ok </span><span class="cov0" title="0">{
                messageCopy := message
                messageCopy.CurrentUserId = message.ReceiverID
                msg := m.WSMessage{
                        Type: "private_msg",
                        Data: u.MustMarshal(messageCopy),
                }
                msgBytes, _ := json.Marshal(msg)
                receiverClient.SendCh &lt;- msgBytes
        }</span>

        <span class="cov0" title="0">if senderClient, ok := clients[message.SenderID]; ok &amp;&amp; message.SenderID != message.ReceiverID </span><span class="cov0" title="0">{
                messageCopy := message
                messageCopy.CurrentUserId = message.SenderID
                msg := m.WSMessage{
                        Type: "private_msg",
                        Data: u.MustMarshal(messageCopy),
                }
                msgBytes, _ := json.Marshal(msg)
                senderClient.SendCh &lt;- msgBytes
        }</span>
}

// this fucntion broadcasts posts to all the clients through the websocket.
func handleNewPostBroadcast(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                Title    string `json:"title"`
                Content  string `json:"content"`
                Category string `json:"category"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse post broadcast payload:", err)
                return
        }</span>

        <span class="cov0" title="0">postID := PostID_posting
        post, err := FetchLatestPostFromDb(int(postID))
        if err != nil </span><span class="cov0" title="0">{
                log.Println("Error fetching latest post:", err)
                return
        }</span>

        <span class="cov0" title="0">msg := m.WSMessage{
                Type: "new_post",
                Data: u.MustMarshal(post),
        }
        msgBytes, err := json.Marshal(msg)
        if err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to marshal post message:", err)
                return
        }</span>

        <span class="cov0" title="0">clientsMutex.RLock()
        for _, client := range clients </span><span class="cov0" title="0">{
                client.SendCh &lt;- msgBytes
        }</span>
        <span class="cov0" title="0">clientsMutex.RUnlock()</span>
}

// this function handles and sends a post like through the websocket
func handlePostLike(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                PostID string `json:"post_id"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse like payload:", err)
                return
        }</span>

        <span class="cov0" title="0">postID, _ := strconv.Atoi(payload.PostID)
        likes, _ := GetPostLikes(postID)

        msg := m.WSMessage{
                Type: "like_post",
                Data: u.MustMarshal(m.LikePostObject{
                        PostID: postID,
                        Likes:  likes,
                }),
        }
        msgBytes, err := json.Marshal(msg)
        if err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to marshal like post object:", err)
                return
        }</span>

        <span class="cov0" title="0">clientsMutex.RLock()
        for _, client := range clients </span><span class="cov0" title="0">{
                client.SendCh &lt;- msgBytes
        }</span>
        <span class="cov0" title="0">clientsMutex.RUnlock()</span>
}

// handlers for group messages
func handleGroupMessages(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                GroupId int    `json:"groupId"`
                Content string `json:"content"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse group message payload:", err)
                return
        }</span>

        <span class="cov0" title="0">message, err := LatestGroupMessage(payload.GroupId, Sender_id, DB)
        if err != nil </span><span class="cov0" title="0">{
                log.Println("Error fetching latest group message:", err)
        }</span>

        <span class="cov0" title="0">clientsMutex.RLock()
        defer clientsMutex.RUnlock()
        for userID, client := range clients </span><span class="cov0" title="0">{
                if checkMembers(message.GroupID, userID, DB) </span><span class="cov0" title="0">{
                        messageCopy := message
                        messageCopy.CurrentUserId = userID

                        msg := m.WSMessage{
                                Type: "group_mssg",
                                Data: u.MustMarshal(messageCopy),
                        }

                        msgBytes, err := json.Marshal(msg)
                        if err != nil </span><span class="cov0" title="0">{
                                log.Println("Failed to marshal group message:", err)
                                continue</span>
                        }
                        <span class="cov0" title="0">client.SendCh &lt;- msgBytes</span>
                }
        }
}
// handleNotification handles real-time notification broadcasting
func handleNotification(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                UserID       string      `json:"user_id"`
                Notification interface{} `json:"notification"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse notification payload:", err)
                return
        }</span>

        // Send notification to specific user
        <span class="cov0" title="0">SendNotificationToUser(payload.UserID, map[string]interface{}{
                "type": "notification",
                "data": payload.Notification,
        })</span>
}

// handleEventNotification handles event-related notifications
func handleEventNotification(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                EventID   int    `json:"event_id"`
                GroupID   int    `json:"group_id"`
                EventType string `json:"event_type"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse event notification payload:", err)
                return
        }</span>

        // Get event details
        <span class="cov0" title="0">var eventTitle, creatorID, creatorName, groupName string
        err := sq.GetDB().QueryRow(`
                SELECT e.title, e.creator_id, u.first_name || ' ' || u.last_name, g.name
                FROM events e
                JOIN users u ON e.creator_id = u.id
                JOIN groups g ON e.group_id = g.id
                WHERE e.id = ?`,
                payload.EventID).Scan(&amp;eventTitle, &amp;creatorID, &amp;creatorName, &amp;groupName)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting event details: %v", err)
                return
        }</span>

        // Get all group members except the creator
        <span class="cov0" title="0">rows, err := sq.GetDB().Query(`
                SELECT user_id FROM group_members 
                WHERE group_id = ? AND user_id != ?`,
                payload.GroupID, creatorID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group members: %v", err)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        eventData := m.EventNotificationData{
                EventID:     payload.EventID,
                EventTitle:  eventTitle,
                GroupID:     payload.GroupID,
                GroupName:   groupName,
                CreatorID:   creatorID,
                CreatorName: creatorName,
        }

        // Send notification to each member
        for rows.Next() </span><span class="cov0" title="0">{
                var memberID string
                if err := rows.Scan(&amp;memberID); err != nil </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov0" title="0">SendNotificationToUser(memberID, map[string]interface{}{
                        "type": "event_created",
                        "data": eventData,
                })</span>
        }
}

// getCurrentTimestamp returns current timestamp in ISO format
func getCurrentTimestamp() string <span class="cov8" title="1">{
        return time.Now().UTC().Format(time.RFC3339)
}</span>

// handleJoinGroupPage handles when a user joins a group page (for presence tracking)
func handleJoinGroupPage(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                GroupID int    `json:"group_id"`
                UserID  string `json:"user_id"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse join group page payload:", err)
                return
        }</span>

        // Broadcast to other group members that this user is now viewing the group
        <span class="cov0" title="0">broadcastGroupPresence(payload.GroupID, payload.UserID, "joined_page")</span>
}

// handleLeaveGroupPage handles when a user leaves a group page
func handleLeaveGroupPage(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                GroupID int    `json:"group_id"`
                UserID  string `json:"user_id"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse leave group page payload:", err)
                return
        }</span>

        // Broadcast to other group members that this user left the group page
        <span class="cov0" title="0">broadcastGroupPresence(payload.GroupID, payload.UserID, "left_page")</span>
}

// handleGroupTyping handles typing indicators in group chat
func handleGroupTyping(data json.RawMessage) <span class="cov0" title="0">{
        var payload struct {
                GroupID int    `json:"group_id"`
                UserID  string `json:"user_id"`
                IsTyping bool  `json:"is_typing"`
        }

        if err := json.Unmarshal(data, &amp;payload); err != nil </span><span class="cov0" title="0">{
                log.Println("Failed to parse group typing payload:", err)
                return
        }</span>

        // Broadcast typing indicator to other group members
        <span class="cov0" title="0">broadcastGroupTyping(payload.GroupID, payload.UserID, payload.IsTyping)</span>
}

// broadcastGroupPresence broadcasts user presence changes to group members
func broadcastGroupPresence(groupID int, userID string, action string) <span class="cov0" title="0">{
        // Get all group members except the user who triggered the action
        rows, err := sq.GetDB().Query(`
                SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?`,
                groupID, userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group members for presence broadcast: %v", err)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        // Create presence message
        presenceMessage := map[string]interface{}{
                "type": "group_presence",
                "data": map[string]interface{}{
                        "group_id": groupID,
                        "user_id":  userID,
                        "action":   action,
                        "timestamp": getCurrentTimestamp(),
                },
        }

        messageJSON, err := json.Marshal(presenceMessage)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error marshaling presence message: %v", err)
                return
        }</span>

        // Send to all other group members
        <span class="cov0" title="0">clientsMutex.RLock()
        for rows.Next() </span><span class="cov0" title="0">{
                var memberID string
                if err := rows.Scan(&amp;memberID); err != nil </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov0" title="0">if client, exists := clients[memberID]; exists </span><span class="cov0" title="0">{
                        select </span>{
                        case client.SendCh &lt;- messageJSON:<span class="cov0" title="0"></span>
                                // Message sent successfully
                        default:<span class="cov0" title="0"></span>
                                // Client send channel is full, skip
                        }
                }
        }
        <span class="cov0" title="0">clientsMutex.RUnlock()</span>
}

// broadcastGroupTyping broadcasts typing indicators to group members
func broadcastGroupTyping(groupID int, userID string, isTyping bool) <span class="cov0" title="0">{
        // Get all group members except the user who is typing
        rows, err := sq.GetDB().Query(`
                SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?`,
                groupID, userID)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error getting group members for typing broadcast: %v", err)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        // Create typing message
        typingMessage := map[string]interface{}{
                "type": "group_typing",
                "data": map[string]interface{}{
                        "group_id":  groupID,
                        "user_id":   userID,
                        "is_typing": isTyping,
                        "timestamp": getCurrentTimestamp(),
                },
        }

        messageJSON, err := json.Marshal(typingMessage)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error marshaling typing message: %v", err)
                return
        }</span>

        // Send to all other group members
        <span class="cov0" title="0">clientsMutex.RLock()
        for rows.Next() </span><span class="cov0" title="0">{
                var memberID string
                if err := rows.Scan(&amp;memberID); err != nil </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov0" title="0">if client, exists := clients[memberID]; exists </span><span class="cov0" title="0">{
                        select </span>{
                        case client.SendCh &lt;- messageJSON:<span class="cov0" title="0"></span>
                                // Message sent successfully
                        default:<span class="cov0" title="0"></span>
                                // Client send channel is full, skip
                        }
                }
        }
        <span class="cov0" title="0">clientsMutex.RUnlock()</span>
}
</pre>
		
		<pre class="file" id="file21" style="display: none">package handlers

import (
        "log"
        "net/http"
        "sync"

        m "imson/pkg/models"
        wsstatus "imson/pkg/websocket"

        "github.com/gorilla/websocket"
)

type Client struct {
        ID     string
        Conn   *websocket.Conn
        SendCh chan []byte
}

var (
        upgrader = websocket.Upgrader{
                ReadBufferSize:  1024,
                WriteBufferSize: 1024,
                CheckOrigin: func(r *http.Request) bool <span class="cov0" title="0">{
                        return true
                }</span>,
        }
        clients      = make(map[string]*Client)
        clientsMutex = sync.RWMutex{}
)

func WebSocketHandler(w http.ResponseWriter, r *http.Request) <span class="cov0" title="0">{
        userID, _ := GetCurrentUserID(r, DB)
        conn, err := upgrader.Upgrade(w, r, nil)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("WebSocket upgrade error: %v", err)
                return
        }</span>

        <span class="cov0" title="0">client := &amp;Client{
                ID:     userID,
                Conn:   conn,
                SendCh: make(chan []byte, 256),
        }

        clientsMutex.Lock()
        clients[userID] = client
        clientsMutex.Unlock()

        // Update WebSocket status manager
        statusManager := wsstatus.GetStatusManager()
        statusManager.SetUserOnline(userID)

        // Broadcast user online status to their groups
        go BroadcastUserStatusToGroups(userID, "online")

        go client.writeMessages()
        client.readMessages()</span>
}

func (c *Client) readMessages() <span class="cov0" title="0">{
        defer func() </span><span class="cov0" title="0">{
                // Update WebSocket status manager
                statusManager := wsstatus.GetStatusManager()
                statusManager.SetUserOffline(c.ID)

                // Broadcast user offline status to their groups
                go BroadcastUserStatusToGroups(c.ID, "offline")

                clientsMutex.Lock()
                delete(clients, c.ID)
                clientsMutex.Unlock()
                close(c.SendCh)
                c.Conn.Close()
        }</span>()

        <span class="cov0" title="0">for </span><span class="cov0" title="0">{
                var msg m.WSMessage
                err := c.Conn.ReadJSON(&amp;msg)
                if err != nil </span><span class="cov0" title="0">{
                        break</span>
                }

                <span class="cov0" title="0">switch msg.Type </span>{
                case "new_post":<span class="cov0" title="0">
                        handleNewPostBroadcast(msg.Data)</span>
                case "private_msg":<span class="cov0" title="0">
                        handlePrivateMsg(msg.Data)</span>
                case "like_post":<span class="cov0" title="0">
                        handlePostLike(msg.Data)</span>
                        // Handle post like
                case "typing":<span class="cov0" title="0"></span>
                        // Handle typing indicator
                case "follow_request":<span class="cov0" title="0"></span>
                        // Handle new follow request notification
                case "follow_accepted":<span class="cov0" title="0"></span>
                        // Handle follow request accepted notification
                case "follow_declined":<span class="cov0" title="0"></span>
                        // Handle follow request declined notification
                case "group_mssg":<span class="cov0" title="0">
                        handleGroupMessages(msg.Data)</span>
                case "notification":<span class="cov0" title="0">
                        handleNotification(msg.Data)</span>
                case "event_notification":<span class="cov0" title="0">
                        handleEventNotification(msg.Data)</span>
                case "join_group_page":<span class="cov0" title="0">
                        // User joined a group page - can be used for presence tracking
                        handleJoinGroupPage(msg.Data)</span>
                case "leave_group_page":<span class="cov0" title="0">
                        // User left a group page - can be used for presence tracking
                        handleLeaveGroupPage(msg.Data)</span>
                case "typing_in_group":<span class="cov0" title="0">
                        // User is typing in group chat
                        handleGroupTyping(msg.Data)</span>

                default:<span class="cov0" title="0">
                        log.Println("Unhandled message type:", msg.Type)</span>
                }
        }
}

func (c *Client) writeMessages() <span class="cov0" title="0">{
        for msg := range c.SendCh </span><span class="cov0" title="0">{
                if err := c.Conn.WriteMessage(websocket.TextMessage, msg); err != nil </span><span class="cov0" title="0">{
                        log.Printf("WebSocket write error for user %v: %v", c.ID, err)
                        break</span>
                }
        }
}

// IsUserOnline checks if a user is currently connected via WebSocket
func IsUserOnline(userID string) bool <span class="cov0" title="0">{
        clientsMutex.RLock()
        _, exists := clients[userID]
        clientsMutex.RUnlock()
        return exists
}</span>

// GetOnlineUsersInGroup returns a list of online user IDs from a given list of user IDs
func GetOnlineUsersInGroup(userIDs []string) []string <span class="cov0" title="0">{
        var onlineUsers []string

        clientsMutex.RLock()
        for _, userID := range userIDs </span><span class="cov0" title="0">{
                if _, exists := clients[userID]; exists </span><span class="cov0" title="0">{
                        onlineUsers = append(onlineUsers, userID)
                }</span>
        }
        <span class="cov0" title="0">clientsMutex.RUnlock()

        return onlineUsers</span>
}

// GetOnlineCount returns the number of online users from a given list
func GetOnlineCount(userIDs []string) int <span class="cov0" title="0">{
        count := 0

        clientsMutex.RLock()
        for _, userID := range userIDs </span><span class="cov0" title="0">{
                if _, exists := clients[userID]; exists </span><span class="cov0" title="0">{
                        count++
                }</span>
        }
        <span class="cov0" title="0">clientsMutex.RUnlock()

        return count</span>
}

// BroadcastUserStatusToGroups broadcasts a user's online/offline status to all their group members
func BroadcastUserStatusToGroups(userID string, status string) <span class="cov0" title="0">{
        // This function will be implemented to notify group members about status changes
        // For now, it's a placeholder that can be enhanced with group-specific broadcasting
        log.Printf("User %s is now %s", userID, status)
}</span>
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
