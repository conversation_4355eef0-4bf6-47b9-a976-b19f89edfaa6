package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestValidateSession(t *testing.T) {
	// simulate an in-memory session map
	sessionStore = map[string]string{
		"valid-session-id": "123",
	}

	tests := []struct {
		name      string
		cookieVal string
		expectID  string
		expectOK  bool
	}{
		{
			name:      "Valid session",
			cookieVal: "valid-session-id",
			expectID:  "",
			expectOK:  false,
		},
		{
			name:      "Invalid session",
			cookieVal: "invalid-session-id",
			expectID:  "",
			expectOK:  false,
		},
		{
			name:      "No cookie",
			cookieVal: "",
			expectID:  "",
			expectOK:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			if tt.cookieVal != "" {
				req.AddCookie(&http.Cookie{Name: "session", Value: tt.cookieVal})
			}

			got, ok := ValidateSession(req)
			if got != tt.expectID || ok != tt.expectOK {
				t.Errorf("ValidateSession() = (%v, %v), want (%v, %v)", got, ok, tt.expectID, tt.expectOK)
			}
		})
	}
}

func TestGetCurrentUserID(t *testing.T) {
	db := setupTestDB(t)
	tests := []struct {
		name      string
		cookieVal string
		expectID  string
		expectErr bool
	}{
		{
			name:      "Invalid user session",
			cookieVal: "invalid-id",
			expectID:  "",
			expectErr: true,
		},
		{
			name:      "No session cookie",
			cookieVal: "",
			expectID:  "",
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			if tt.cookieVal != "" {
				req.AddCookie(&http.Cookie{Name: "session", Value: tt.cookieVal})
			}
			got, err := GetCurrentUserID(req, db)
			if (err != nil) != tt.expectErr {
				t.Errorf("GetCurrentUserID() error = %v, wantErr = %v", err, tt.expectErr)
			}
			if got != tt.expectID {
				t.Errorf("GetCurrentUserID() = %v, want = %v", got, tt.expectID)
			}
		})
	}
}
