package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	sq "imson/pkg/db/sqlite"
	"imson/pkg/services"
)

type FollowRequestResponse struct {
	RequestID int `json:"request_id"`
}

// AcceptFollowRequest handles accepting a follow request
func AcceptFollowRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user from session
	cookie, err := r.<PERSON><PERSON>("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	var req FollowRequestResponse
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	// Verify the request belongs to the current user and update status
	err = services.UpdateFollowRequestStatus(req.RequestID, userID, "accepted")
	if err != nil {
		http.Error(w, "Could not accept follow request", http.StatusInternalServerError)
		return
	}

	// Get follower info for notification
	followerInfo, err := services.GetFollowerInfoByRequestID(req.RequestID)
	if err != nil {
		http.Error(w, "Could not get follower info", http.StatusInternalServerError)
		return
	}

	// Send WebSocket notification to the follower
	// Get user info for notification
	var accepterName string
	err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userIDStr).Scan(&accepterName)
	if err == nil {
		BroadcastFollowAcceptedNotification(followerInfo.FollowerID, userID, accepterName)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "success",
		"message": "Follow request accepted",
	})
}

// DeclineFollowRequest handles declining a follow request
func DeclineFollowRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user from session
	cookie, err := r.Cookie("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	var req FollowRequestResponse
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	// Verify the request belongs to the current user and update status
	err = services.UpdateFollowRequestStatus(req.RequestID, userID, "declined")
	if err != nil {
		http.Error(w, "Could not decline follow request", http.StatusInternalServerError)
		return
	}

	// Get follower info for notification
	followerInfo, err := services.GetFollowerInfoByRequestID(req.RequestID)
	if err != nil {
		http.Error(w, "Could not get follower info", http.StatusInternalServerError)
		return
	}

	// Send WebSocket notification to the follower
	// Get user info for notification
	var declinerName string
	err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userIDStr).Scan(&declinerName)
	if err == nil {
		BroadcastFollowDeclinedNotification(followerInfo.FollowerID, userID, declinerName)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "success",
		"message": "Follow request declined",
	})
}

// GetPendingFollowRequests returns all pending follow requests for the current user
func GetPendingFollowRequests(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user from session
	cookie, err := r.Cookie("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	requests, err := services.GetPendingFollowRequests(userID)
	if err != nil {
		http.Error(w, "Could not get follow requests", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(requests)
}
