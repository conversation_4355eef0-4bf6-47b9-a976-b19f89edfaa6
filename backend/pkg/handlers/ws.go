package handlers

import (
	"log"
	"net/http"
	"sync"

	m "imson/pkg/models"
	wsstatus "imson/pkg/websocket"

	"github.com/gorilla/websocket"
)

type Client struct {
	ID     string
	Conn   *websocket.Conn
	SendCh chan []byte
}

var (
	upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	clients      = make(map[string]*Client)
	clientsMutex = sync.RWMutex{}
)

func WebSocketHandler(w http.ResponseWriter, r *http.Request) {
	userID, _ := GetCurrentUserID(r, DB)
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	client := &Client{
		ID:     userID,
		Conn:   conn,
		SendCh: make(chan []byte, 256),
	}

	clientsMutex.Lock()
	clients[userID] = client
	clientsMutex.Unlock()

	// Update WebSocket status manager
	statusManager := wsstatus.GetStatusManager()
	statusManager.SetUserOnline(userID)

	// Broadcast user online status to their groups
	go BroadcastUserStatusToGroups(userID, "online")

	go client.writeMessages()
	client.readMessages()
}

func (c *Client) readMessages() {
	defer func() {
		// Update WebSocket status manager
		statusManager := wsstatus.GetStatusManager()
		statusManager.SetUserOffline(c.ID)

		// Broadcast user offline status to their groups
		go BroadcastUserStatusToGroups(c.ID, "offline")

		clientsMutex.Lock()
		delete(clients, c.ID)
		clientsMutex.Unlock()
		close(c.SendCh)
		c.Conn.Close()
	}()

	for {
		var msg m.WSMessage
		err := c.Conn.ReadJSON(&msg)
		if err != nil {
			break
		}

		switch msg.Type {
		case "new_post":
			handleNewPostBroadcast(msg.Data)
		case "private_msg":
			handlePrivateMsg(msg.Data)
		case "like_post":
			handlePostLike(msg.Data)
			// Handle post like
		case "typing":
			// Handle typing indicator
		case "follow_request":
			// Handle new follow request notification
		case "follow_accepted":
			// Handle follow request accepted notification
		case "follow_declined":
			// Handle follow request declined notification
		case "group_mssg":
			handleGroupMessages(msg.Data)
		case "notification":
			handleNotification(msg.Data)
		case "event_notification":
			handleEventNotification(msg.Data)
		case "join_group_page":
			// User joined a group page - can be used for presence tracking
			handleJoinGroupPage(msg.Data)
		case "leave_group_page":
			// User left a group page - can be used for presence tracking
			handleLeaveGroupPage(msg.Data)
		case "typing_in_group":
			// User is typing in group chat
			handleGroupTyping(msg.Data)
		case "new_reply":
			handleNewReply(msg.Data)
		case "like_reply":
			handleReplyLike(msg.Data)

		default:
			log.Println("Unhandled message type:", msg.Type)
		}
	}
}

func (c *Client) writeMessages() {
	for msg := range c.SendCh {
		if err := c.Conn.WriteMessage(websocket.TextMessage, msg); err != nil {
			log.Printf("WebSocket write error for user %v: %v", c.ID, err)
			break
		}
	}
}

// IsUserOnline checks if a user is currently connected via WebSocket
func IsUserOnline(userID string) bool {
	clientsMutex.RLock()
	_, exists := clients[userID]
	clientsMutex.RUnlock()
	return exists
}

// GetOnlineUsersInGroup returns a list of online user IDs from a given list of user IDs
func GetOnlineUsersInGroup(userIDs []string) []string {
	var onlineUsers []string

	clientsMutex.RLock()
	for _, userID := range userIDs {
		if _, exists := clients[userID]; exists {
			onlineUsers = append(onlineUsers, userID)
		}
	}
	clientsMutex.RUnlock()

	return onlineUsers
}

// GetOnlineCount returns the number of online users from a given list
func GetOnlineCount(userIDs []string) int {
	count := 0

	clientsMutex.RLock()
	for _, userID := range userIDs {
		if _, exists := clients[userID]; exists {
			count++
		}
	}
	clientsMutex.RUnlock()

	return count
}

// BroadcastUserStatusToGroups broadcasts a user's online/offline status to all their group members
func BroadcastUserStatusToGroups(userID string, status string) {
	// This function will be implemented to notify group members about status changes
	// For now, it's a placeholder that can be enhanced with group-specific broadcasting
	log.Printf("User %s is now %s", userID, status)
}
