package handlers

import (
	"bytes"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	sqlite "imson/pkg/db/sqlite"

	"github.com/DATA-DOG/go-sqlmock"
)

func TestLeaveGroupHandler(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	originalDB := sqlite.DBConn
	sqlite.DBConn = db
	defer func() { sqlite.DBConn = originalDB }()

	userID := "user-123"
	sessionID := "session-abc"

	tests := []struct {
		name           string
		method         string
		cookie         *http.Cookie
		body           []byte
		setupMocks     func()
		wantStatusCode int
		wantBody       string
	}{
		{
			name:           "Method not allowed",
			method:         http.MethodGet,
			cookie:         &http.Cookie{Name: "session_id", Value: sessionID},
			body:           nil,
			setupMocks:     func() {},
			wantStatusCode: http.StatusMethodNotAllowed,
			wantBody:       "Only POST method is allowed",
		},
		{
			name:           "Missing session cookie",
			method:         http.MethodPost,
			cookie:         nil,
			body:           []byte(`{"group_id":1}`),
			setupMocks:     func() {},
			wantStatusCode: http.StatusUnauthorized,
			wantBody:       "User not authenticated",
		},
		{
			name:   "Invalid session cookie",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnError(sql.ErrNoRows)
			},
			wantStatusCode: http.StatusUnauthorized,
			wantBody:       "User not authenticated",
		},
		{
			name:   "Invalid JSON body",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{bad json}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(userID))
			},
			wantStatusCode: http.StatusBadRequest,
			wantBody:       "Invalid request body",
		},
		{
			name:   "DB error on membership check",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":2}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(userID))

				mock.ExpectQuery(`SELECT COUNT\(\*\) FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(2, userID).
					WillReturnError(errors.New("db error"))
			},
			wantStatusCode: http.StatusInternalServerError,
			wantBody:       "Database error",
		},
		{
			name:   "User not a member",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":3}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(userID))

				mock.ExpectQuery(`SELECT COUNT\(\*\) FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(3, userID).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))
			},
			wantStatusCode: http.StatusForbidden,
			wantBody:       "User is not a member of this group",
		},
		{
			name:   "DB error on delete",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":4}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(userID))

				mock.ExpectQuery(`SELECT COUNT\(\*\) FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(4, userID).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

				mock.ExpectExec(`DELETE FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(4, userID).
					WillReturnError(errors.New("delete error"))
			},
			wantStatusCode: http.StatusInternalServerError,
			wantBody:       "Failed to leave group",
		},
		{
			name:   "Successful leave",
			method: http.MethodPost,
			cookie: &http.Cookie{Name: "session_id", Value: sessionID},
			body:   []byte(`{"group_id":1}`),
			setupMocks: func() {
				mock.ExpectQuery(`SELECT user_id FROM sessions WHERE id = \?`).
					WithArgs(sessionID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id"}).AddRow(userID))

				mock.ExpectQuery(`SELECT COUNT\(\*\) FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, userID).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

				mock.ExpectExec(`DELETE FROM group_members WHERE group_id = \? AND user_id = \?`).
					WithArgs(1, userID).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantStatusCode: http.StatusOK,
			wantBody:       `{"message":"Successfully left group"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, "/groups/leave", bytes.NewReader(tt.body))
			if tt.cookie != nil {
				req.AddCookie(tt.cookie)
			}

			rr := httptest.NewRecorder()

			tt.setupMocks()

			LeaveGroupHandler(rr, req)

			if rr.Code != tt.wantStatusCode {
				t.Errorf("[%s] status code: got %d want %d", tt.name, rr.Code, tt.wantStatusCode)
			}

			if !bytes.Contains(rr.Body.Bytes(), []byte(tt.wantBody)) {
				t.Errorf("[%s] body: got %q want to contain %q", tt.name, rr.Body.String(), tt.wantBody)
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("[%s] unfulfilled expectations: %s", tt.name, err)
			}
		})
	}
}
