package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// GetUserPosts handles fetching posts created by a specific user
func GetUserPosts(w http.ResponseWriter, r *http.Request) {
	// Handle CORS preflight
	if r.Method == http.MethodOptions {
		return
	}

	// Get user ID from session
	userID, _ := GetCurrentUserID(r, sq.GetDB())
	if userID == "" {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Query posts from database
	rows, err := sq.GetDB().Query(`
		SELECT p.id, p.content, COALESCE(p.image, '') as image, p.visibility, p.created_at, p.user_id,
			   u.nickname as author_nickname, u.first_name as author_first_name,
			   u.last_name as author_last_name, COALESCE(u.avatar_url, '') as author_avatar,
			   (SELECT COUNT(*) FROM comments WHERE post_id = p.id) as comment_count
		FROM posts p
		JOIN users u ON p.user_id = u.id
		WHERE p.user_id = ?
		ORDER BY p.created_at DESC
	`, userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var posts []m.Post
	for rows.Next() {
		var post m.Post
		err := rows.Scan(
			&post.ID,
			&post.Content,
			&post.Image,
			&post.Visibility,
			&post.CreatedAt,
			&post.UserID,
			&post.AuthorNickname,
			&post.AuthorFirstName,
			&post.AuthorLastName,
			&post.AuthorAvatar,
			&post.CommentCount,
		)
		if err != nil {
			http.Error(w, "Error scanning posts", http.StatusInternalServerError)
			return
		}
		posts = append(posts, post)
	}

	// Return posts as JSON
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(posts)
}
