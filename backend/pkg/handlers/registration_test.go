package handlers

import (
	"bytes"
	"errors"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"

	sqlite "imson/pkg/db/sqlite"

	"github.com/DATA-DOG/go-sqlmock"
	"golang.org/x/crypto/bcrypt"
)

var (
	osMkdirAll = os.MkdirAll
	osCreate   = os.Create
	ioCopy     = io.Copy
)

// Patchable bcrypt for testing
var bcryptGenerateFromPassword = bcrypt.GenerateFromPassword

func TestRegisterHandler(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	// Patch the DB connection
	originalDBConn := sqlite.DBConn
	sqlite.DBConn = db
	defer func() { sqlite.DBConn = originalDBConn }()

	tmpDir := t.TempDir()
	originalMkdirAll := osMkdirAll
	originalCreate := osCreate
	originalCopy := ioCopy

	osMkdirAll = func(path string, perm os.FileMode) error {
		return os.MkdirAll(filepath.Join(tmpDir, path), perm)
	}
	osCreate = func(name string) (*os.File, error) {
		return os.Create(filepath.Join(tmpDir, name))
	}
	defer func() {
		osMkdirAll = originalMkdirAll
		osCreate = originalCreate
		ioCopy = originalCopy
	}()

	tests := []struct {
		name           string
		formFields     map[string]string
		includeAvatar  bool
		mockDB         func()
		failMkdir      bool
		failCreate     bool
		failCopy       bool
		wantStatus     int
		wantInResponse string
		failBcrypt     bool
		method         string
	}{
		{
			name: "Valid registration without avatar",
			formFields: map[string]string{
				"firstName":   "Jane",
				"lastName":    "Doe",
				"nickname":    "jdoe",
				"dateOfBirth": "1990-01-01",
				"aboutMe":     "Just Jane",
				"gender":      "female",
				"email":       "<EMAIL>",
				"password":    "pass123",
			},
			includeAvatar: false,
			mockDB: func() {
				mock.ExpectExec("INSERT INTO users").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantStatus:     http.StatusCreated,
			wantInResponse: "Registered jdoe successfully",
		},
		{
			name: "Missing required fields",
			formFields: map[string]string{
				"nickname": "missingFields",
			},
			mockDB:     func() {},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "Invalid multipart format",
			formFields: map[string]string{
				"bad": "format",
			},
			mockDB:     func() {},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "DB insert error",
			formFields: map[string]string{
				"firstName":   "John",
				"lastName":    "Smith",
				"nickname":    "jsmith",
				"dateOfBirth": "1985-01-01",
				"gender":      "male",
				"email":       "<EMAIL>",
				"password":    "secret123",
			},
			includeAvatar: false,
			mockDB: func() {
				mock.ExpectExec("INSERT INTO users").
					WillReturnError(errors.New("db failure"))
			},
			wantStatus: http.StatusInternalServerError,
		},
		{
			name: "Avatar upload: mkdir failure",
			formFields: map[string]string{
				"firstName":   "Error",
				"lastName":    "Mkdir",
				"nickname":    "mkdirfail",
				"dateOfBirth": "1992-02-02",
				"gender":      "female",
				"email":       "<EMAIL>",
				"password":    "failpwd",
			},
			includeAvatar: true,
			failMkdir:     true,
			mockDB:        func() {},
			wantStatus:    http.StatusInternalServerError,
		},
		{
			name: "Avatar upload: file create failure",
			formFields: map[string]string{
				"firstName":   "Error",
				"lastName":    "Create",
				"nickname":    "createfail",
				"dateOfBirth": "1993-03-03",
				"gender":      "male",
				"email":       "<EMAIL>",
				"password":    "failpwd",
			},
			includeAvatar: true,
			failCreate:    true,
			mockDB:        func() {},
			wantStatus:    http.StatusInternalServerError,
		},
		{
			name: "Avatar upload: io.Copy failure",
			formFields: map[string]string{
				"firstName":   "Error",
				"lastName":    "Copy",
				"nickname":    "copyfail",
				"dateOfBirth": "1994-04-04",
				"gender":      "non-binary",
				"email":       "<EMAIL>",
				"password":    "failpwd",
			},
			includeAvatar: true,
			failCopy:      true,
			mockDB:        func() {},
			wantStatus:    http.StatusInternalServerError,
		},
		{
			name: "Non-POST method not allowed",
			formFields: map[string]string{
				"firstName": "John", "lastName": "Doe", "gender": "male", "email": "<EMAIL>", "password": "1234",
			},
			includeAvatar:  false,
			mockDB:         func() {},
			wantStatus:     http.StatusMethodNotAllowed,
			wantInResponse: "Method not allowed",
			method:         http.MethodGet,
		},
		{
			name: "bcrypt failure",
			formFields: map[string]string{
				"firstName": "Bcrypt", "lastName": "Fail", "nickname": "bcryptfail", "dateOfBirth": "1990-01-01", "gender": "male", "email": "<EMAIL>", "password": "failpwd",
			},
			includeAvatar:  false,
			failBcrypt:     true,
			mockDB:         func() {},
			wantStatus:     http.StatusInternalServerError,
			wantInResponse: "Error creating user\n",
			method:         http.MethodPost,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.failMkdir {
				osMkdirAll = func(path string, perm os.FileMode) error {
					return errors.New("mkdir failed")
				}
			} else {
				osMkdirAll = func(path string, perm os.FileMode) error {
					return os.MkdirAll(filepath.Join(tmpDir, path), perm)
				}
			}

			if tt.failCreate {
				osCreate = func(name string) (*os.File, error) {
					return nil, errors.New("create failed")
				}
			} else {
				osCreate = func(name string) (*os.File, error) {
					return os.Create(filepath.Join(tmpDir, name))
				}
			}

			if tt.failCopy {
				ioCopy = func(dst io.Writer, src io.Reader) (int64, error) {
					return 0, errors.New("copy failed")
				}
			} else {
				ioCopy = io.Copy
			}

			var body bytes.Buffer
			writer := multipart.NewWriter(&body)

			if tt.name == "Invalid multipart format" {
				body = *bytes.NewBufferString("not multipart")
				writer = nil
			} else {
				for key, val := range tt.formFields {
					writer.WriteField(key, val)
				}
				if tt.includeAvatar {
					part, _ := writer.CreateFormFile("avatar", "avatar.png")
					part.Write([]byte("fake image content"))
				}
				writer.Close()
			}

			// In the test runner, patch bcrypt if needed
			if tt.failBcrypt {
				bcryptGenerateFromPassword = func(pw []byte, cost int) ([]byte, error) {
					return nil, errors.New("bcrypt fail")
				}
			} else {
				bcryptGenerateFromPassword = bcrypt.GenerateFromPassword
			}
			defer func() { bcryptGenerateFromPassword = bcrypt.GenerateFromPassword }()

			// Use the test's method if set, otherwise default to POST
			method := tt.method
			if method == "" {
				method = http.MethodPost
			}

			req := httptest.NewRequest(method, "/api/auth/signup", &body)
			if writer != nil {
				req.Header.Set("Content-Type", writer.FormDataContentType())
			}

			rr := httptest.NewRecorder()
			tt.mockDB()
			RegisterHandler(rr, req)

			resp := rr.Result()
			defer resp.Body.Close()

			if resp.StatusCode != tt.wantStatus {
				t.Errorf("[%s] expected status %d, got %d", tt.name, tt.wantStatus, resp.StatusCode)
			}

			if tt.wantInResponse != "" {
				data, _ := io.ReadAll(resp.Body)
				if !strings.Contains(string(data), tt.wantInResponse) {
					t.Errorf("[%s] expected response to contain %q, got %q", tt.name, tt.wantInResponse, string(data))
				}
			}
		})
	}
}
