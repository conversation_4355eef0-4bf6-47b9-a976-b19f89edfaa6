package handlers

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"

	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// Test database setup
func setupTestDB(t *testing.T) *sql.DB {
	// Create a temporary database file
	dbFile := fmt.Sprintf("test_%s.db", uuid.New().String())

	// Initialize database with migrations
	db, err := sq.Init(dbFile, "../db/migrations/sqlite")
	if err != nil {
		t.Fatalf("Failed to initialize test database: %v", err)
	}

	// Database is already initialized with migrations
	// Set the global database connection for handlers
	sq.DBConn = db

	// Cleanup function
	t.Cleanup(func() {
		db.Close()
		os.<PERSON>move(dbFile)
	})

	return db
}

// Helper function to create test user
func createTestUser(t *testing.T, db *sql.DB, userID, email, nickname string) {
	_, err := db.Exec(`
		INSERT INTO users (id, nickname, first_name, last_name, gender, email, password)
		VALUES (?, ?, 'Test', 'User', 'male', ?, 'hashedpassword')`,
		userID, nickname, email)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}
}

// Helper function to create test session
func createTestSession(t *testing.T, db *sql.DB, sessionID, userID string) {
	expiresAt := time.Now().Add(24 * time.Hour)
	_, err := db.Exec(`
		INSERT INTO sessions (id, user_id, expires_at)
		VALUES (?, ?, ?)`,
		sessionID, userID, expiresAt)
	if err != nil {
		t.Fatalf("Failed to create test session: %v", err)
	}
}

// Helper function to create test post
func createTestPost(t *testing.T, db *sql.DB, userID string) string {
	postID := "post1"
	_, err := db.Exec(`
		INSERT INTO posts (id, user_id, content)
		VALUES (?, ?, 'Test content')`,
		postID, userID)
	if err != nil {
		t.Fatalf("Failed to create test post: %v", err)
	}

	return postID
}

func TestCreateCommentHandler(t *testing.T) {
	db := setupTestDB(t)

	// Create test data
	userID := "user1"
	sessionID := "session1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	createTestSession(t, db, sessionID, userID)
	postID := createTestPost(t, db, userID)

	tests := []struct {
		name           string
		method         string
		body           m.CreateCommentRequest
		sessionCookie  string
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "Valid comment creation",
			method: "POST",
			body: m.CreateCommentRequest{
				PostID:  postID,
				Content: "This is a test comment",
			},
			sessionCookie:  sessionID,
			expectedStatus: http.StatusCreated,
		},
		{
			name:           "Invalid method",
			method:         "GET",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name:           "No session cookie",
			method:         "POST",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:   "Empty content",
			method: "POST",
			body: m.CreateCommentRequest{
				PostID:  postID,
				Content: "",
			},
			sessionCookie:  sessionID,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "Invalid post ID",
			method: "POST",
			body: m.CreateCommentRequest{
				PostID:  "nonexistent",
				Content: "Test comment",
			},
			sessionCookie:  sessionID,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			var req *http.Request
			if tt.method == "POST" {
				bodyBytes, _ := json.Marshal(tt.body)
				req = httptest.NewRequest(tt.method, "/api/comments", bytes.NewBuffer(bodyBytes))
				req.Header.Set("Content-Type", "application/json")
			} else {
				req = httptest.NewRequest(tt.method, "/api/comments", nil)
			}

			// Add session cookie if provided
			if tt.sessionCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "session",
					Value: tt.sessionCookie,
				})
				// Also add to sessionStore for testing
				sessionStore[tt.sessionCookie] = userID
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			CreateCommentHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// For successful creation, check response body
			if tt.expectedStatus == http.StatusCreated {
				var comment m.Comment
				err := json.Unmarshal(rr.Body.Bytes(), &comment)
				if err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if comment.Content != tt.body.Content {
					t.Errorf("Expected content %s, got %s", tt.body.Content, comment.Content)
				}

				if comment.PostID != tt.body.PostID {
					t.Errorf("Expected post ID %s, got %s", tt.body.PostID, comment.PostID)
				}
			}
		})
	}
}

func TestGetCommentsHandler(t *testing.T) {
	db := setupTestDB(t)

	// Create test data
	userID := "user1"
	sessionID := "session1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	createTestSession(t, db, sessionID, userID)
	postID := createTestPost(t, db, userID)

	// Create test comments
	_, err := db.Exec(`
		INSERT INTO comments (post_id, user_id, content, created_at)
		VALUES (?, ?, 'First comment', ?), (?, ?, 'Second comment', ?)`,
		postID, userID, time.Now().Add(-2*time.Hour),
		postID, userID, time.Now().Add(-1*time.Hour))
	if err != nil {
		t.Fatalf("Failed to create test comments: %v", err)
	}

	tests := []struct {
		name           string
		method         string
		postID         string
		sessionCookie  string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Valid get comments",
			method:         "GET",
			postID:         postID,
			sessionCookie:  sessionID,
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Invalid method",
			method:         "POST",
			postID:         postID,
			sessionCookie:  sessionID,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name:           "No session cookie",
			method:         "GET",
			postID:         postID,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Missing post ID",
			method:         "GET",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Invalid post ID",
			method:         "GET",
			postID:         "invalid",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "Non-existent post",
			method:         "GET",
			postID:         "nonexistent",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			url := "/api/comments"
			if tt.postID != "" {
				url += "?post_id=" + tt.postID
			}
			req := httptest.NewRequest(tt.method, url, nil)

			// Add session cookie if provided
			if tt.sessionCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "session",
					Value: tt.sessionCookie,
				})
				// Also add to sessionStore for testing
				sessionStore[tt.sessionCookie] = userID
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			GetCommentsHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// For successful requests, check response body
			if tt.expectedStatus == http.StatusOK {
				var comments []m.Comment
				err := json.Unmarshal(rr.Body.Bytes(), &comments)
				if err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if len(comments) != tt.expectedCount {
					t.Errorf("Expected %d comments, got %d", tt.expectedCount, len(comments))
				}
			}
		})
	}
}

func TestLikeCommentHandler(t *testing.T) {
	db := setupTestDB(t)

	// Create test data
	userID := "user1"
	sessionID := "session1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	createTestSession(t, db, sessionID, userID)
	postID := createTestPost(t, db, userID)

	// Create test comment
	result, err := db.Exec(`
		INSERT INTO comments (post_id, user_id, content)
		VALUES (?, ?, 'Test comment')`,
		postID, userID)
	if err != nil {
		t.Fatalf("Failed to create test comment: %v", err)
	}

	commentID, err := result.LastInsertId()
	if err != nil {
		t.Fatalf("Failed to get comment ID: %v", err)
	}

	tests := []struct {
		name           string
		method         string
		commentID      string
		sessionCookie  string
		expectedStatus int
		setupLike      bool // Whether to pre-like the comment
	}{
		{
			name:           "Valid like comment",
			method:         "POST",
			commentID:      fmt.Sprintf("%d", commentID),
			sessionCookie:  sessionID,
			expectedStatus: http.StatusOK,
			setupLike:      false,
		},
		{
			name:           "Valid unlike comment",
			method:         "POST",
			commentID:      fmt.Sprintf("%d", commentID),
			sessionCookie:  sessionID,
			expectedStatus: http.StatusOK,
			setupLike:      true,
		},
		{
			name:           "Invalid method",
			method:         "GET",
			commentID:      fmt.Sprintf("%d", commentID),
			sessionCookie:  sessionID,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name:           "No session cookie",
			method:         "POST",
			commentID:      fmt.Sprintf("%d", commentID),
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Missing comment ID",
			method:         "POST",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Invalid comment ID",
			method:         "POST",
			commentID:      "invalid",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Non-existent comment",
			method:         "POST",
			commentID:      "99999",
			sessionCookie:  sessionID,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup like if needed
			if tt.setupLike {
				_, err := db.Exec(`
					INSERT INTO comment_likes (comment_id, user_id) VALUES (?, ?)
					ON CONFLICT(comment_id, user_id) DO NOTHING`,
					commentID, userID)
				if err != nil {
					t.Fatalf("Failed to setup like: %v", err)
				}

				_, err = db.Exec("UPDATE comments SET likes = 1 WHERE id = ?", commentID)
				if err != nil {
					t.Fatalf("Failed to update comment likes: %v", err)
				}
			}

			// Create request
			url := "/api/comments/like"
			if tt.commentID != "" {
				url += "?comment_id=" + tt.commentID
			}
			req := httptest.NewRequest(tt.method, url, nil)

			// Add session cookie if provided
			if tt.sessionCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "session",
					Value: tt.sessionCookie,
				})
				// Also add to sessionStore for testing
				sessionStore[tt.sessionCookie] = userID
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			LikeCommentHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// For successful requests, check response body
			if tt.expectedStatus == http.StatusOK {
				var likeObj m.LikeCommentObject
				err := json.Unmarshal(rr.Body.Bytes(), &likeObj)
				if err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if likeObj.CommentID != int(commentID) {
					t.Errorf("Expected comment ID %d, got %d", commentID, likeObj.CommentID)
				}
			}

			// Cleanup for next test
			db.Exec("DELETE FROM comment_likes WHERE comment_id = ? AND user_id = ?", commentID, userID)
			db.Exec("UPDATE comments SET likes = 0 WHERE id = ?", commentID)
		})
	}
}

// Test helper functions
func TestCheckUserLikedComment(t *testing.T) {
	db := setupTestDB(t)

	userID := "user1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	postID := createTestPost(t, db, userID)

	// Create test comment
	result, err := db.Exec("INSERT INTO comments (post_id, user_id, content) VALUES (?, ?, 'Test')", postID, userID)
	if err != nil {
		t.Fatalf("Failed to create comment: %v", err)
	}
	commentID, _ := result.LastInsertId()

	// Test when user hasn't liked
	liked, err := CheckUserLikedComment(int(commentID), userID)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if liked {
		t.Error("Expected user to not have liked comment")
	}

	// Add like
	_, err = db.Exec("INSERT INTO comment_likes (comment_id, user_id) VALUES (?, ?)", commentID, userID)
	if err != nil {
		t.Fatalf("Failed to add like: %v", err)
	}

	// Test when user has liked
	liked, err = CheckUserLikedComment(int(commentID), userID)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if !liked {
		t.Error("Expected user to have liked comment")
	}
}

func TestLikeComment(t *testing.T) {
	db := setupTestDB(t)

	userID := "user1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	postID := createTestPost(t, db, userID)

	// Create test comment
	result, err := db.Exec("INSERT INTO comments (post_id, user_id, content) VALUES (?, ?, 'Test')", postID, userID)
	if err != nil {
		t.Fatalf("Failed to create comment: %v", err)
	}
	commentID, _ := result.LastInsertId()

	// Test liking comment
	err = LikeComment(int(commentID), userID)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify like was added
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM comment_likes WHERE comment_id = ? AND user_id = ?", commentID, userID).Scan(&count)
	if err != nil {
		t.Errorf("Error checking like: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected 1 like record, got %d", count)
	}

	// Verify likes count was incremented
	var likes int
	err = db.QueryRow("SELECT likes FROM comments WHERE id = ?", commentID).Scan(&likes)
	if err != nil {
		t.Errorf("Error checking likes count: %v", err)
	}
	if likes != 1 {
		t.Errorf("Expected 1 like, got %d", likes)
	}
}

func TestUnlikeComment(t *testing.T) {
	db := setupTestDB(t)

	userID := "user1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	postID := createTestPost(t, db, userID)

	// Create test comment with a like
	result, err := db.Exec("INSERT INTO comments (post_id, user_id, content, likes) VALUES (?, ?, 'Test', 1)", postID, userID)
	if err != nil {
		t.Fatalf("Failed to create comment: %v", err)
	}
	commentID, _ := result.LastInsertId()

	// Add like record
	_, err = db.Exec("INSERT INTO comment_likes (comment_id, user_id) VALUES (?, ?)", commentID, userID)
	if err != nil {
		t.Fatalf("Failed to add like: %v", err)
	}

	// Test unliking comment
	err = UnlikeComment(int(commentID), userID)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify like was removed
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM comment_likes WHERE comment_id = ? AND user_id = ?", commentID, userID).Scan(&count)
	if err != nil {
		t.Errorf("Error checking like: %v", err)
	}
	if count != 0 {
		t.Errorf("Expected 0 like records, got %d", count)
	}

	// Verify likes count was decremented
	var likes int
	err = db.QueryRow("SELECT likes FROM comments WHERE id = ?", commentID).Scan(&likes)
	if err != nil {
		t.Errorf("Error checking likes count: %v", err)
	}
	if likes != 0 {
		t.Errorf("Expected 0 likes, got %d", likes)
	}
}

func TestGetCommentLikes(t *testing.T) {
	db := setupTestDB(t)

	userID := "user1"
	createTestUser(t, db, userID, "<EMAIL>", "testuser")
	postID := createTestPost(t, db, userID)

	// Create test comment with specific likes count
	result, err := db.Exec("INSERT INTO comments (post_id, user_id, content, likes) VALUES (?, ?, 'Test', 5)", postID, userID)
	if err != nil {
		t.Fatalf("Failed to create comment: %v", err)
	}
	commentID, _ := result.LastInsertId()

	// Test getting likes count
	likes, err := GetCommentLikes(int(commentID))
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if likes != 5 {
		t.Errorf("Expected 5 likes, got %d", likes)
	}
}
