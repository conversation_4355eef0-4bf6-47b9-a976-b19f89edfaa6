package handlers

import (
	"database/sql"
	"log"
	"net/http"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// ValidateSession checks if the session cookie is valid and not expired.
// It returns the session ID and a boolean indicating if the session is valid.
func ValidateSession(r *http.Request) (string, bool) {
	cookie, err := r.<PERSON>("session_id")
	if err != nil {
		return "", false
	}

	// Verify session exists in database
	var session m.Session
	err = sq.GetDB().QueryRow("SELECT id, user_id, created_at, expires_at FROM sessions WHERE id = ?", cookie.Value).
		Scan(&session.ID, &session.UserID, &session.CreatedAt, &session.ExpiresAt)
	if err != nil {
		return "", false
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		// Delete expired session
		_, err = sq.GetDB().Exec("DELETE FROM sessions WHERE id = ?", session.ID)
		if err != nil {
			log.Printf("Error deleting expired session: %v", err)
		}
		return "", false
	}
	return cookie.Value, true
}

// This function retrieves the current logged in user and returns the user id as a string
func GetCurrentUserID(r *http.Request, db *sql.DB) (string, error) {
	cookie, err := r.Cookie("session_id")
	if err != nil {
		return "", err
	}

	sessionID := cookie.Value
	var userID string
	err = db.QueryRow(`
        SELECT user_id
        FROM sessions
        WHERE id = ?`, sessionID).Scan(&userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", http.ErrNoCookie
		}
		return "", err
	}
	return userID, nil
}

func GetUserIDFromSession(sessionID string) (string, error) {
	var userID string
	err := sq.GetDB().QueryRow("SELECT user_id FROM sessions WHERE id = ?", sessionID).Scan(&userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", http.ErrNoCookie
		}
		return "", err
	}
	return userID, nil
}
