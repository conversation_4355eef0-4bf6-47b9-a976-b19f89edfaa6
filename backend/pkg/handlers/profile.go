package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	db "imson/pkg/db/sqlite"

	"github.com/gorilla/mux"
)

// Helper functions for common operations
func writeJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Printf("Error encoding JSON: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func handleDBError(w http.ResponseWriter, err error, operation string) bool {
	if err == sql.ErrNoRows {
		http.Error(w, "Profile not found", http.StatusNotFound)
		return true
	}
	if err != nil {
		log.Printf("Error %s: %v", operation, err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return true
	}
	return false
}

// Note: Profile creation is handled by the registration process
// Users are created in the users table, not a separate profiles table

// GetProfileHandler handles GET requests for user profile
func GetProfileHandler(w http.ResponseWriter, r *http.Request) {
	userID := mux.Vars(r)["id"]

	var user struct {
		ID          string `json:"id"`
		Nickname    string `json:"nickname"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
		Email       string `json:"email"`
		DateOfBirth string `json:"dateOfBirth"`
		AvatarURL   string `json:"avatarUrl"`
		AboutMe     string `json:"aboutMe"`
		Private     bool   `json:"private"`
	}

	err := db.GetDB().QueryRow(`
		SELECT id, nickname, first_name, last_name, email, date_of_birth,
		       COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(about_me, '') as about_me,
		       private
		FROM users WHERE id = ?`, userID).Scan(
		&user.ID, &user.Nickname, &user.FirstName, &user.LastName, &user.Email,
		&user.DateOfBirth, &user.AvatarURL, &user.AboutMe, &user.Private)

	if handleDBError(w, err, "getting profile") {
		return
	}

	writeJSON(w, http.StatusOK, user)
}

// GetProfilePageHandler handles GET requests for profile page data
func GetProfilePageHandler(w http.ResponseWriter, r *http.Request) {
	userID := mux.Vars(r)["id"]

	var user struct {
		ID          string `json:"id"`
		Nickname    string `json:"nickname"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
		Email       string `json:"email"`
		DateOfBirth string `json:"dateOfBirth"`
		AvatarURL   string `json:"avatarUrl"`
		AboutMe     string `json:"aboutMe"`
		Private     bool   `json:"private"`
		PostsCount  int    `json:"postsCount"`
	}

	err := db.GetDB().QueryRow(`
		SELECT u.id, u.nickname, u.first_name, u.last_name, u.email, u.date_of_birth,
		       COALESCE(u.avatar_url, '') as avatar_url,
		       COALESCE(u.about_me, '') as about_me,
		       u.private,
		       COALESCE(posts_count.count, 0) as posts_count
		FROM users u
		LEFT JOIN (SELECT user_id, COUNT(*) as count FROM posts WHERE user_id = ? GROUP BY user_id) posts_count ON u.id = posts_count.user_id
		WHERE u.id = ?`, userID, userID).Scan(
		&user.ID, &user.Nickname, &user.FirstName, &user.LastName, &user.Email,
		&user.DateOfBirth, &user.AvatarURL, &user.AboutMe, &user.Private, &user.PostsCount)

	if handleDBError(w, err, "getting profile page data") {
		return
	}

	writeJSON(w, http.StatusOK, user)
}

// UpdateProfileHandler handles PUT requests to update user profile
func UpdateProfileHandler(w http.ResponseWriter, r *http.Request) {
	userID := mux.Vars(r)["id"]

	// Parse multipart form data
	err := r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "Invalid form data", http.StatusBadRequest)
		return
	}

	// Get form values
	firstName := r.FormValue("firstName")
	lastName := r.FormValue("lastName")
	nickname := r.FormValue("nickname")
	email := r.FormValue("email")
	dateOfBirth := r.FormValue("dateOfBirth")
	aboutMe := r.FormValue("aboutMe")
	private := r.FormValue("private") == "true"

	// Validate required fields
	if firstName == "" || lastName == "" || nickname == "" || email == "" {
		http.Error(w, "Required fields missing", http.StatusBadRequest)
		return
	}

	// Update user in database
	result, err := db.GetDB().Exec(`
		UPDATE users
		SET nickname = ?, first_name = ?, last_name = ?, email = ?,
		    date_of_birth = ?, about_me = ?, private = ?
		WHERE id = ?`,
		nickname, firstName, lastName, email, dateOfBirth, aboutMe, private, userID)

	if handleDBError(w, err, "updating profile") {
		return
	}

	if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
		http.Error(w, "Profile not found", http.StatusNotFound)
		return
	}

	// Return updated user data
	var updatedUser struct {
		ID          string `json:"id"`
		Nickname    string `json:"nickname"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
		Email       string `json:"email"`
		DateOfBirth string `json:"dateOfBirth"`
		AvatarURL   string `json:"avatarUrl"`
		AboutMe     string `json:"aboutMe"`
		Private     bool   `json:"private"`
	}

	err = db.GetDB().QueryRow(`
		SELECT id, nickname, first_name, last_name, email, date_of_birth,
		       COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(about_me, '') as about_me,
		       private
		FROM users WHERE id = ?`, userID).Scan(
		&updatedUser.ID, &updatedUser.Nickname, &updatedUser.FirstName, &updatedUser.LastName,
		&updatedUser.Email, &updatedUser.DateOfBirth, &updatedUser.AvatarURL, &updatedUser.AboutMe, &updatedUser.Private)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	writeJSON(w, http.StatusOK, updatedUser)
}

// UploadCoverPhotoHandler handles cover photo uploads
func UploadCoverPhotoHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse multipart form
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "Invalid form data", http.StatusBadRequest)
		return
	}

	// Get the uploaded file
	file, header, err := r.FormFile("cover")
	if err != nil {
		http.Error(w, "No cover photo provided", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file type
	if !isValidImageType(header.Filename) {
		http.Error(w, "Invalid file type. Only JPG, PNG, and GIF are allowed", http.StatusBadRequest)
		return
	}

	// Create directory for cover photos
	coverDir := "static/covers"
	if err := os.MkdirAll(coverDir, 0o755); err != nil {
		http.Error(w, "Error creating directory", http.StatusInternalServerError)
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s_cover_%d%s", userID, time.Now().Unix(), ext)
	coverPath := filepath.Join(coverDir, filename)

	// Save the file
	dst, err := os.Create(coverPath)
	if err != nil {
		http.Error(w, "Error saving file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		http.Error(w, "Error writing file", http.StatusInternalServerError)
		return
	}

	// Update user's cover photo URL in database
	coverURL := "/" + coverPath
	_, err = db.GetDB().Exec(`UPDATE users SET cover_url = ? WHERE id = ?`, coverURL, userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]string{
		"message":   "Cover photo uploaded successfully",
		"cover_url": coverURL,
	}
	writeJSON(w, http.StatusOK, response)
}

// UploadAvatarHandler handles avatar photo uploads
func UploadAvatarHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse multipart form
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "Invalid form data", http.StatusBadRequest)
		return
	}

	// Get the uploaded file
	file, header, err := r.FormFile("avatar")
	if err != nil {
		http.Error(w, "No avatar provided", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file type
	if !isValidImageType(header.Filename) {
		http.Error(w, "Invalid file type. Only JPG, PNG, and GIF are allowed", http.StatusBadRequest)
		return
	}

	// Create directory for avatars
	avatarDir := "static/avatars"
	if err := os.MkdirAll(avatarDir, 0o755); err != nil {
		http.Error(w, "Error creating directory", http.StatusInternalServerError)
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s_avatar_%d%s", userID, time.Now().Unix(), ext)
	avatarPath := filepath.Join(avatarDir, filename)

	// Save the file
	dst, err := os.Create(avatarPath)
	if err != nil {
		http.Error(w, "Error saving file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		http.Error(w, "Error writing file", http.StatusInternalServerError)
		return
	}

	// Update user's avatar URL in database
	avatarURL := "/" + avatarPath
	_, err = db.GetDB().Exec(`UPDATE users SET avatar_url = ? WHERE id = ?`, avatarURL, userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]string{
		"message":    "Avatar uploaded successfully",
		"avatar_url": avatarURL,
	}
	writeJSON(w, http.StatusOK, response)
}

// Helper function to validate image file types
func isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif"
}

// DeleteProfileHandler handles DELETE requests to remove a user
func DeleteProfileHandler(w http.ResponseWriter, r *http.Request) {
	userID := mux.Vars(r)["id"]

	result, err := db.GetDB().Exec(`DELETE FROM users WHERE id = ?`, userID)
	if handleDBError(w, err, "deleting user") {
		return
	}

	if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// GetUserPhotosHandler handles GET requests for user's photos/gallery
func GetUserPhotosHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	userID := mux.Vars(r)["id"]
	if userID == "" {
		http.Error(w, "User ID is required", http.StatusBadRequest)
		return
	}

	// Get photos from posts (assuming posts can have images)
	var photos []map[string]interface{}

	rows, err := db.GetDB().Query(`
		SELECT p.id, p.content, p.image_url, p.created_at
		FROM posts p
		WHERE p.user_id = ? AND p.image_url IS NOT NULL AND p.image_url != ''
		ORDER BY p.created_at DESC
		LIMIT 50`, userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var photo map[string]interface{}
		var postID, content, imageURL, createdAt string

		err := rows.Scan(&postID, &content, &imageURL, &createdAt)
		if err != nil {
			continue
		}

		photo = map[string]interface{}{
			"id":         postID,
			"image_url":  imageURL,
			"caption":    content,
			"created_at": createdAt,
			"type":       "post",
		}
		photos = append(photos, photo)
	}

	// Also get profile photos (avatar, cover)
	var user struct {
		AvatarURL string `json:"avatar_url"`
		CoverURL  string `json:"cover_url"`
	}

	err = db.GetDB().QueryRow(`
		SELECT COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(cover_url, '') as cover_url
		FROM users WHERE id = ?`, userID).Scan(&user.AvatarURL, &user.CoverURL)

	if err == nil {
		if user.AvatarURL != "" {
			photos = append(photos, map[string]interface{}{
				"id":         "avatar",
				"image_url":  user.AvatarURL,
				"caption":    "Profile Picture",
				"created_at": "",
				"type":       "avatar",
			})
		}
		if user.CoverURL != "" {
			photos = append(photos, map[string]interface{}{
				"id":         "cover",
				"image_url":  user.CoverURL,
				"caption":    "Cover Photo",
				"created_at": "",
				"type":       "cover",
			})
		}
	}

	response := map[string]interface{}{
		"photos": photos,
		"total":  len(photos),
	}

	writeJSON(w, http.StatusOK, response)
}

// GetUserFriendsHandler handles GET requests for user's friends/followers
func GetUserFriendsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	userID := mux.Vars(r)["id"]
	if userID == "" {
		http.Error(w, "User ID is required", http.StatusBadRequest)
		return
	}

	// Get followers
	var friends []map[string]interface{}

	rows, err := db.GetDB().Query(`
		SELECT u.id, u.nickname, u.first_name, u.last_name,
		       COALESCE(u.avatar_url, '') as avatar_url,
		       f.created_at
		FROM followers f
		JOIN users u ON f.follower_id = u.id
		WHERE f.followed_id = ? AND f.status = 'accepted'
		ORDER BY f.created_at DESC
		LIMIT 100`, userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var friend map[string]interface{}
		var id, nickname, firstName, lastName, avatarURL, createdAt string

		err := rows.Scan(&id, &nickname, &firstName, &lastName, &avatarURL, &createdAt)
		if err != nil {
			continue
		}

		friend = map[string]interface{}{
			"id":         id,
			"nickname":   nickname,
			"first_name": firstName,
			"last_name":  lastName,
			"avatar_url": avatarURL,
			"since":      createdAt,
		}
		friends = append(friends, friend)
	}

	response := map[string]interface{}{
		"friends": friends,
		"total":   len(friends),
	}

	writeJSON(w, http.StatusOK, response)
}

// GetUserSettingsHandler handles GET requests for user settings
func GetUserSettingsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user settings
	var settings struct {
		ID                   string `json:"id"`
		Email                string `json:"email"`
		Private              bool   `json:"private"`
		EmailNotifications   bool   `json:"email_notifications"`
		PushNotifications    bool   `json:"push_notifications"`
		ShowOnlineStatus     bool   `json:"show_online_status"`
		AllowMessagesFromAll bool   `json:"allow_messages_from_all"`
		TwoFactorEnabled     bool   `json:"two_factor_enabled"`
	}

	err = db.GetDB().QueryRow(`
		SELECT id, email, private,
		       COALESCE(email_notifications, true) as email_notifications,
		       COALESCE(push_notifications, true) as push_notifications,
		       COALESCE(show_online_status, true) as show_online_status,
		       COALESCE(allow_messages_from_all, false) as allow_messages_from_all,
		       COALESCE(two_factor_enabled, false) as two_factor_enabled
		FROM users WHERE id = ?`, userID).Scan(
		&settings.ID, &settings.Email, &settings.Private,
		&settings.EmailNotifications, &settings.PushNotifications,
		&settings.ShowOnlineStatus, &settings.AllowMessagesFromAll,
		&settings.TwoFactorEnabled)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "User not found", http.StatusNotFound)
			return
		}
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	writeJSON(w, http.StatusOK, settings)
}

// UpdateUserSettingsHandler handles PUT requests to update user settings
func UpdateUserSettingsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse JSON request body
	var settings struct {
		Private              bool `json:"private"`
		EmailNotifications   bool `json:"email_notifications"`
		PushNotifications    bool `json:"push_notifications"`
		ShowOnlineStatus     bool `json:"show_online_status"`
		AllowMessagesFromAll bool `json:"allow_messages_from_all"`
		TwoFactorEnabled     bool `json:"two_factor_enabled"`
	}

	if err := json.NewDecoder(r.Body).Decode(&settings); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Update settings in database
	_, err = db.GetDB().Exec(`
		UPDATE users SET
			private = ?,
			email_notifications = ?,
			push_notifications = ?,
			show_online_status = ?,
			allow_messages_from_all = ?,
			two_factor_enabled = ?
		WHERE id = ?`,
		settings.Private, settings.EmailNotifications, settings.PushNotifications,
		settings.ShowOnlineStatus, settings.AllowMessagesFromAll, settings.TwoFactorEnabled,
		userID)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	response := map[string]string{
		"message": "Settings updated successfully",
	}
	writeJSON(w, http.StatusOK, response)
}

// GetCurrentUserHandler handles GET requests for current logged-in user's information
func GetCurrentUserHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user information from users table
	var user struct {
		ID          string `json:"id"`
		Nickname    string `json:"nickname"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
		Email       string `json:"email"`
		DateOfBirth string `json:"dateOfBirth"`
		AvatarURL   string `json:"avatarUrl"`
		CoverURL    string `json:"coverUrl"`
		AboutMe     string `json:"aboutMe"`
		Private     bool   `json:"private"`
	}

	err = db.GetDB().QueryRow(`
		SELECT id, nickname, first_name, last_name, email, date_of_birth,
		       COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(cover_url, '') as cover_url,
		       COALESCE(about_me, '') as about_me,
		       private
		FROM users WHERE id = ?`, userID).Scan(
		&user.ID, &user.Nickname, &user.FirstName, &user.LastName, &user.Email,
		&user.DateOfBirth, &user.AvatarURL, &user.CoverURL, &user.AboutMe, &user.Private)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "User not found", http.StatusNotFound)
			return
		}
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	writeJSON(w, http.StatusOK, user)
}

// GetProfileCardSummaryHandler handles GET requests for lightweight profile card data
// Purpose: Used for hover cards, search results, friend suggestions, comment previews
// Similar to Facebook's profile preview cards
func GetProfileCardSummaryHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	userID := mux.Vars(r)["id"]
	if userID == "" {
		http.Error(w, "User ID is required", http.StatusBadRequest)
		return
	}

	// Get current user ID from session for privacy checks
	sessionID, valid := ValidateSession(r)
	var currentUserID string
	if valid {
		currentUserID, _ = GetUserIDFromSession(sessionID)
	}

	// Profile card summary structure
	var card struct {
		ID             string `json:"id"`
		Nickname       string `json:"nickname"`
		FirstName      string `json:"firstName"`
		LastName       string `json:"lastName"`
		AvatarURL      string `json:"avatarUrl"`
		AboutMe        string `json:"aboutMe"`
		IsPrivate      bool   `json:"isPrivate"`
		IsOnline       bool   `json:"isOnline"`
		LastSeen       string `json:"lastSeen"`
		MutualFriends  int    `json:"mutualFriends"`
		PostsCount     int    `json:"postsCount"`
		FollowersCount int    `json:"followersCount"`
		IsFollowing    bool   `json:"isFollowing"`
		CanMessage     bool   `json:"canMessage"`
		CanFollow      bool   `json:"canFollow"`
	}

	// Get basic user information
	err := db.GetDB().QueryRow(`
		SELECT id, nickname, first_name, last_name,
		       COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(SUBSTR(about_me, 1, 100), '') as about_me,
		       private
		FROM users WHERE id = ?`, userID).Scan(
		&card.ID, &card.Nickname, &card.FirstName, &card.LastName,
		&card.AvatarURL, &card.AboutMe, &card.IsPrivate)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "User not found", http.StatusNotFound)
			return
		}
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	// If profile is private and current user is not following, limit data
	if card.IsPrivate && currentUserID != userID {
		isFollowing := false
		if currentUserID != "" {
			// Check if current user is following this private user
			var count int
			db.GetDB().QueryRow(`
				SELECT COUNT(*) FROM followers
				WHERE follower_id = ? AND followed_id = ?`,
				currentUserID, userID).Scan(&count)
			isFollowing = count > 0
		}

		if !isFollowing {
			// Return limited data for private profiles
			card.AboutMe = ""
			card.PostsCount = 0
			card.FollowersCount = 0
			card.CanMessage = false
			card.CanFollow = currentUserID != "" && currentUserID != userID
			writeJSON(w, http.StatusOK, card)
			return
		}
	}

	// Get additional stats for public profiles or followed private profiles
	if currentUserID != "" {
		// Check if current user is following this user
		var followCount int
		db.GetDB().QueryRow(`
			SELECT COUNT(*) FROM followers
			WHERE follower_id = ? AND followed_id = ?`,
			currentUserID, userID).Scan(&followCount)
		card.IsFollowing = followCount > 0

		// Calculate mutual friends (users both follow)
		db.GetDB().QueryRow(`
			SELECT COUNT(DISTINCT f1.followed_id)
			FROM followers f1
			INNER JOIN followers f2 ON f1.followed_id = f2.followed_id
			WHERE f1.follower_id = ? AND f2.follower_id = ?
			AND f1.followed_id != ? AND f1.followed_id != ?`,
			currentUserID, userID, currentUserID, userID).Scan(&card.MutualFriends)
	}

	// Get posts count
	db.GetDB().QueryRow(`
		SELECT COUNT(*) FROM posts WHERE user_id = ?`, userID).Scan(&card.PostsCount)

	// Get followers count
	db.GetDB().QueryRow(`
		SELECT COUNT(*) FROM followers WHERE followed_id = ?`, userID).Scan(&card.FollowersCount)

	// Set permissions
	card.CanMessage = currentUserID != "" && currentUserID != userID && (!card.IsPrivate || card.IsFollowing)
	card.CanFollow = currentUserID != "" && currentUserID != userID && !card.IsFollowing

	// TODO: Implement online status and last seen from WebSocket connections
	// For now, set default values
	card.IsOnline = false
	card.LastSeen = "Unknown"

	writeJSON(w, http.StatusOK, card)
}

// UpdateCurrentUserHandler handles PUT requests to update current user's information
func UpdateCurrentUserHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current user ID from session
	sessionID, valid := ValidateSession(r)
	if !valid {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	userID, err := GetUserIDFromSession(sessionID)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse multipart form data
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "Invalid form data", http.StatusBadRequest)
		return
	}

	// Get form values
	firstName := r.FormValue("firstName")
	lastName := r.FormValue("lastName")
	nickname := r.FormValue("nickname")
	email := r.FormValue("email")
	dateOfBirth := r.FormValue("dateOfBirth")
	aboutMe := r.FormValue("aboutMe")
	private := r.FormValue("private") == "true"

	// Validate required fields
	if firstName == "" || lastName == "" || nickname == "" || email == "" {
		http.Error(w, "Required fields missing", http.StatusBadRequest)
		return
	}

	// Handle avatar upload if present
	var avatarURL string
	file, header, err := r.FormFile("avatar")
	if err == nil {
		defer file.Close()

		// Save the uploaded file (simplified - in production you'd want proper validation and storage)
		avatarURL = "/static/avatars/" + userID + "_" + header.Filename
		// Here you would implement the actual file saving logic
	}

	// Update user in database
	query := `
		UPDATE users
		SET nickname = ?, first_name = ?, last_name = ?, email = ?,
		    date_of_birth = ?, about_me = ?, private = ?`
	args := []interface{}{nickname, firstName, lastName, email, dateOfBirth, aboutMe, private}

	if avatarURL != "" {
		query += ", avatar_url = ?"
		args = append(args, avatarURL)
	}

	query += " WHERE id = ?"
	args = append(args, userID)

	result, err := db.GetDB().Exec(query, args...)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	// Return updated user data
	var updatedUser struct {
		ID          string `json:"id"`
		Nickname    string `json:"nickname"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
		Email       string `json:"email"`
		DateOfBirth string `json:"dateOfBirth"`
		AvatarURL   string `json:"avatarUrl"`
		AboutMe     string `json:"aboutMe"`
		Private     bool   `json:"private"`
	}

	err = db.GetDB().QueryRow(`
		SELECT id, nickname, first_name, last_name, email, date_of_birth,
		       COALESCE(avatar_url, '') as avatar_url,
		       COALESCE(about_me, '') as about_me,
		       private
		FROM users WHERE id = ?`, userID).Scan(
		&updatedUser.ID, &updatedUser.Nickname, &updatedUser.FirstName, &updatedUser.LastName,
		&updatedUser.Email, &updatedUser.DateOfBirth, &updatedUser.AvatarURL, &updatedUser.AboutMe, &updatedUser.Private)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	writeJSON(w, http.StatusOK, updatedUser)
}
