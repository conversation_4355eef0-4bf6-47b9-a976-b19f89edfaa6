package handlers

import (
	"bytes"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	sqlite "imson/pkg/db/sqlite"
	"imson/pkg/services"

	"github.com/DATA-DOG/go-sqlmock"
)

func TestLoginHandler(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	// Patch the db.DBConn used in services package
	originalDBConn := sqlite.DBConn
	sqlite.DBConn = db
	defer func() { sqlite.DBConn = originalDBConn }()

	tests := []struct {
		name           string
		method         string
		body           string
		mockDB         func()
		wantStatus     int
		wantInResponse string
	}{
		{
			name:   "Valid login",
			method: http.MethodPost,
			body:   `{"email":"<EMAIL>","password":"correctpass"}`,
			mockDB: func() {
				// Mock user lookup for authentication
				hashedPass, _ := services.HashPassword("correctpass")
				mock.ExpectQuery("SELECT id, password FROM users").
					WithArgs("<EMAIL>").
					WillReturnRows(sqlmock.NewRows([]string{"id", "password"}).
						AddRow("user-123", hashedPass))

				// Mock session insert
				mock.ExpectExec("INSERT INTO sessions").
					WithArgs(sqlmock.AnyArg(), "user-123", sqlmock.AnyArg(), sqlmock.AnyArg()).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantStatus:     http.StatusOK,
			wantInResponse: "login successful",
		},
		{
			name:           "Invalid JSON",
			method:         http.MethodPost,
			body:           `{"email": "foo"`, // broken JSON
			mockDB:         func() {},
			wantStatus:     http.StatusBadRequest,
			wantInResponse: "Invalid JSON",
		},
		{
			name:           "Missing email",
			method:         http.MethodPost,
			body:           `{"password": "abc"}`,
			mockDB:         func() {},
			wantStatus:     http.StatusBadRequest,
			wantInResponse: "Email and password are required",
		},
		{
			name:   "Invalid credentials (no user)",
			method: http.MethodPost,
			body:   `{"email":"<EMAIL>","password":"wrongpass"}`,
			mockDB: func() {
				mock.ExpectQuery("SELECT id, password FROM users").
					WithArgs("<EMAIL>").
					WillReturnError(sql.ErrNoRows)
			},
			wantStatus:     http.StatusUnauthorized,
			wantInResponse: "invalid email or password",
		},
		{
			name:   "Incorrect password",
			method: http.MethodPost,
			body:   `{"email":"<EMAIL>","password":"wrongpass"}`,
			mockDB: func() {
				hashedPass, _ := services.HashPassword("correctpass") // actual password is different
				mock.ExpectQuery("SELECT id, password FROM users").
					WithArgs("<EMAIL>").
					WillReturnRows(sqlmock.NewRows([]string{"id", "password"}).
						AddRow("user-123", hashedPass))
			},
			wantStatus:     http.StatusUnauthorized,
			wantInResponse: "invalid email or password",
		},
		{
			name:   "DB error on session insert",
			method: http.MethodPost,
			body:   `{"email":"<EMAIL>","password":"correctpass"}`,
			mockDB: func() {
				hashedPass, _ := services.HashPassword("correctpass")
				mock.ExpectQuery("SELECT id, password FROM users").
					WithArgs("<EMAIL>").
					WillReturnRows(sqlmock.NewRows([]string{"id", "password"}).
						AddRow("user-123", hashedPass))

				mock.ExpectExec("INSERT INTO sessions").
					WillReturnError(errors.New("db insert failed"))
			},
			wantStatus:     http.StatusInternalServerError,
			wantInResponse: "Failed to create session",
		},
		{
			name:           "Wrong method",
			method:         http.MethodGet,
			body:           ``,
			mockDB:         func() {},
			wantStatus:     http.StatusMethodNotAllowed,
			wantInResponse: "Only POST allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockDB()

			req := httptest.NewRequest(tt.method, "/api/auth/login", bytes.NewBufferString(tt.body))
			req.Header.Set("Content-Type", "application/json")
			rr := httptest.NewRecorder()

			LoginHandler(rr, req)

			resp := rr.Result()
			defer resp.Body.Close()

			if resp.StatusCode != tt.wantStatus {
				t.Errorf("expected status %d, got %d", tt.wantStatus, resp.StatusCode)
			}

			buf := new(bytes.Buffer)
			buf.ReadFrom(resp.Body)
			body := buf.String()

			if !bytes.Contains([]byte(body), []byte(tt.wantInResponse)) {
				t.Errorf("expected response to contain %q, got %q", tt.wantInResponse, body)
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("unmet DB expectations: %v", err)
			}
		})
	}
}
