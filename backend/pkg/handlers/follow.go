package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	sq "imson/pkg/db/sqlite"
	"imson/pkg/services"
)

type FollowRequest struct {
	FollowingID string `json:"following_id"` // Changed to string to match user ID format
}

func FollowUser(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user from session - use same cookie name as login handler
	cookie, err := r.<PERSON>("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Use the same sessionStore as login handler
	userID, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	var req FollowRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	if req.FollowingID == userID {
		http.Error(w, "Cannot follow yourself", http.StatusBadRequest)
		return
	}

	// Convert string IDs to integers for the service functions
	userIDInt, err := strconv.Atoi(userID)
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusInternalServerError)
		return
	}

	followingIDInt, err := strconv.Atoi(req.FollowingID)
	if err != nil {
		http.Error(w, "Invalid following ID", http.StatusBadRequest)
		return
	}

	isPrivate, err := services.IsPrivateProfile(followingIDInt)
	if err != nil {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	status := "accepted"
	if isPrivate {
		status = "pending"
	}

	err = services.CreateFollowRequest(userIDInt, followingIDInt, status)
	if err != nil {
		http.Error(w, "Could not send follow request", http.StatusInternalServerError)
		return
	}

	if status == "pending" {
		// Get sender info for notification
		var senderName string
		err = sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&senderName)
		if err == nil {
			// Get the request ID
			var requestID int
			err = sq.GetDB().QueryRow("SELECT id FROM followers WHERE follower_id = ? AND following_id = ? ORDER BY created_at DESC LIMIT 1", userIDInt, followingIDInt).Scan(&requestID)
			if err == nil {
				BroadcastFollowRequestNotification(followingIDInt, userIDInt, senderName, requestID)
			}
		}
	}

	json.NewEncoder(w).Encode(map[string]string{
		"status":  status,
		"message": "Follow request sent",
	})
}
