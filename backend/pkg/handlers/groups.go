package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"time"

	m "imson/pkg/models"
	u "imson/pkg/utils"
)

// handler to create a group
func CreateGroup(wr http.ResponseWriter, rq *http.Request, DB *sql.DB) {
	if rq.Method != http.MethodPost {
		http.Error(wr, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Name          string `json:"name"`
		Administrator string `json:"admin"`
		About         string `json:"about"`
		Topics        string `json:"topics"`
	}

	if err := json.NewDecoder(rq.Body).Decode(&req); err != nil {
		http.Error(wr, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Name == "" || req.About == "" || req.Topics == "" {
		http.Error(wr, "Message incomplete", http.StatusBadRequest)
		return
	}

	creatorID, err := GetCurrentUserID(rq, DB)
	if err != nil {
		http.Error(wr, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var admin string
	err = DB.QueryRow(`
		SELECT nickname FROM users WHERE id = ?
	`, creatorID).Scan(&admin)
	if err != nil {
		http.Error(wr, "Database error", http.StatusInternalServerError)
		return
	}
	combinedTopics := req.Topics

	result, _ := DB.Exec(
		`INSERT INTO groups (name, creator_id, cardinality, administrator, about, topics, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		req.Name, creatorID, 1, admin, req.About, combinedTopics, time.Now(), time.Now(),
	)

	groupID, err := result.LastInsertId()
	if err != nil {
		http.Error(wr, "Failed to retrieve group ID", http.StatusInternalServerError)
		return
	}

	group := m.Group{
		ID:            int(groupID),
		Name:          req.Name,
		CreatorID:     creatorID,
		Cardinality:   1,
		Administrator: admin,
		About:         req.About,
		Topics:        u.SplitAndTrim(req.Topics),
	}

	wr.WriteHeader(http.StatusOK)
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(group)
}
