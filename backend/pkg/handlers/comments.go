package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// CreateCommentHandler handles the creation of new comments
func CreateCommentHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user from session - use same approach as follow handler
	cookie, err := r.<PERSON>("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Use the same sessionStore as login handler
	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID := userIDStr

	// Parse request body
	var req m.CreateCommentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	// Validate input
	if req.PostID == "" || req.Content == "" {
		http.Error(w, "Post ID and content are required", http.StatusBadRequest)
		return
	}

	// Check if post exists
	var postExists bool
	err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM posts WHERE id = ?)", req.PostID).Scan(&postExists)
	if err != nil || !postExists {
		http.Error(w, "Post not found", http.StatusNotFound)
		return
	}

	// Create comment
	result, err := sq.GetDB().Exec(`
		INSERT INTO comments (post_id, user_id, content, created_at) 
		VALUES (?, ?, ?, ?)`,
		req.PostID, userID, req.Content, time.Now())
	if err != nil {
		log.Printf("Error creating comment: %v", err)
		http.Error(w, "Error creating comment", http.StatusInternalServerError)
		return
	}

	commentID, err := result.LastInsertId()
	if err != nil {
		log.Printf("Error getting comment ID: %v", err)
		http.Error(w, "Error creating comment", http.StatusInternalServerError)
		return
	}

	// Update post comment count
	_, err = sq.GetDB().Exec("UPDATE posts SET comment_count = comment_count + 1 WHERE id = ?", req.PostID)
	if err != nil {
		log.Printf("Error updating post comment count: %v", err)
	}

	// Fetch the created comment with author details
	comment, err := GetCommentByID(int(commentID), userID)
	if err != nil {
		log.Printf("Error fetching created comment: %v", err)
		http.Error(w, "Error retrieving comment", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(comment)
}

// GetCommentsHandler fetches comments for a specific post
func GetCommentsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user from session - use same approach as follow handler
	cookie, err := r.Cookie("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Use the same sessionStore as login handler
	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID := userIDStr

	// Get post ID from query parameters
	postID := r.URL.Query().Get("post_id")
	if postID == "" {
		http.Error(w, "Post ID is required", http.StatusBadRequest)
		return
	}

	// Check if post exists
	var postExists bool
	err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM posts WHERE id = ?)", postID).Scan(&postExists)
	if err != nil || !postExists {
		http.Error(w, "Post not found", http.StatusNotFound)
		return
	}

	// Fetch comments
	comments, err := GetCommentsByPostID(postID, userID)
	if err != nil {
		log.Printf("Error fetching comments: %v", err)
		http.Error(w, "Error fetching comments", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(comments)
}

// LikeCommentHandler handles liking/unliking comments
func LikeCommentHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user from session - use same approach as follow handler
	cookie, err := r.Cookie("session")
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Use the same sessionStore as login handler
	userIDStr, ok := sessionStore[cookie.Value]
	if !ok {
		http.Error(w, "Invalid session", http.StatusUnauthorized)
		return
	}

	userID := userIDStr

	// Get comment ID from URL path or request body
	commentIDStr := r.URL.Query().Get("comment_id")
	if commentIDStr == "" {
		http.Error(w, "Comment ID is required", http.StatusBadRequest)
		return
	}

	commentID, err := strconv.Atoi(commentIDStr)
	if err != nil {
		http.Error(w, "Invalid comment ID", http.StatusBadRequest)
		return
	}

	// Check if comment exists
	var commentExists bool
	err = sq.GetDB().QueryRow("SELECT EXISTS(SELECT 1 FROM comments WHERE id = ?)", commentID).Scan(&commentExists)
	if err != nil || !commentExists {
		http.Error(w, "Comment not found", http.StatusNotFound)
		return
	}

	// Check if user already liked the comment
	isLiked, err := CheckUserLikedComment(commentID, userID)
	if err != nil {
		log.Printf("Error checking comment like status: %v", err)
		http.Error(w, "Error processing request", http.StatusInternalServerError)
		return
	}

	var likeCommentObj m.LikeCommentObject
	likeCommentObj.CommentID = commentID

	if isLiked {
		// Unlike the comment
		err = UnlikeComment(commentID, userID)
		if err != nil {
			log.Printf("Error unliking comment: %v", err)
			http.Error(w, "Error unliking comment", http.StatusInternalServerError)
			return
		}
	} else {
		// Like the comment
		err = LikeComment(commentID, userID)
		if err != nil {
			log.Printf("Error liking comment: %v", err)
			http.Error(w, "Error liking comment", http.StatusInternalServerError)
			return
		}
	}

	// Get updated like count
	likeCommentObj.Likes, err = GetCommentLikes(commentID)
	if err != nil {
		log.Printf("Error getting comment likes: %v", err)
		http.Error(w, "Error retrieving like count", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(likeCommentObj)
}

// Helper function to get a comment by ID with author details
func GetCommentByID(commentID int, currentUserID string) (m.Comment, error) {
	var comment m.Comment

	query := `
		SELECT c.id, c.post_id, c.user_id, c.content, c.likes, c.created_at,
			   u.nickname, u.first_name, u.last_name, u.gender,
			   COALESCE((SELECT 1 FROM comment_likes WHERE comment_id = c.id AND user_id = ?), 0) as is_liked
		FROM comments c
		JOIN users u ON c.user_id = u.id
		WHERE c.id = ?`

	err := sq.GetDB().QueryRow(query, currentUserID, commentID).Scan(
		&comment.ID, &comment.PostID, &comment.UserID, &comment.Content,
		&comment.Likes, &comment.CreatedAt, &comment.AuthorNickname,
		&comment.AuthorFirstName, &comment.AuthorLastName, &comment.AuthorGender,
		&comment.IsLikedByUser)

	return comment, err
}

// Helper function to get comments by post ID
func GetCommentsByPostID(postID string, currentUserID string) ([]m.Comment, error) {
	query := `
		SELECT c.id, c.post_id, c.user_id, c.content, c.likes, c.created_at,
			   u.nickname, u.first_name, u.last_name, u.gender,
			   COALESCE((SELECT 1 FROM comment_likes WHERE comment_id = c.id AND user_id = ?), 0) as is_liked
		FROM comments c
		JOIN users u ON c.user_id = u.id
		WHERE c.post_id = ?
		ORDER BY c.created_at ASC`

	rows, err := sq.GetDB().Query(query, currentUserID, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var comments []m.Comment
	for rows.Next() {
		var comment m.Comment
		err := rows.Scan(
			&comment.ID, &comment.PostID, &comment.UserID, &comment.Content,
			&comment.Likes, &comment.CreatedAt, &comment.AuthorNickname,
			&comment.AuthorFirstName, &comment.AuthorLastName, &comment.AuthorGender,
			&comment.IsLikedByUser)
		if err != nil {
			log.Printf("Error scanning comment: %v", err)
			continue
		}
		comments = append(comments, comment)
	}

	return comments, nil
}

// Helper function to check if user liked a comment
func CheckUserLikedComment(commentID int, userID string) (bool, error) {
	var count int
	err := sq.GetDB().QueryRow("SELECT COUNT(*) FROM comment_likes WHERE comment_id = ? AND user_id = ?",
		commentID, userID).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// Helper function to like a comment
func LikeComment(commentID int, userID string) error {
	tx, err := sq.GetDB().Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Increment likes count
	_, err = tx.Exec("UPDATE comments SET likes = likes + 1 WHERE id = ?", commentID)
	if err != nil {
		return fmt.Errorf("failed to increment comment likes: %v", err)
	}

	// Add like record
	_, err = tx.Exec("INSERT INTO comment_likes (comment_id, user_id) VALUES (?, ?)", commentID, userID)
	if err != nil {
		return fmt.Errorf("failed to add comment like record: %v", err)
	}

	return tx.Commit()
}

// Helper function to unlike a comment
func UnlikeComment(commentID int, userID string) error {
	tx, err := sq.GetDB().Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Decrement likes count
	_, err = tx.Exec("UPDATE comments SET likes = likes - 1 WHERE id = ?", commentID)
	if err != nil {
		return fmt.Errorf("failed to decrement comment likes: %v", err)
	}

	// Remove like record
	_, err = tx.Exec("DELETE FROM comment_likes WHERE comment_id = ? AND user_id = ?", commentID, userID)
	if err != nil {
		return fmt.Errorf("failed to remove comment like record: %v", err)
	}

	return tx.Commit()
}

// Helper function to get comment likes count
func GetCommentLikes(commentID int) (int, error) {
	var likes int
	err := sq.GetDB().QueryRow("SELECT likes FROM comments WHERE id = ?", commentID).Scan(&likes)
	return likes, err
}
