package handlers

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestLogoutHandler(t *testing.T) {
	tests := []struct {
		name           string
		sessionCookie  *http.Cookie
		setupSessions  func()
		wantStatus     int
		wantDeletedKey string // key we expect to be removed from the sessions map
	}{
		{
			name: "Valid logout",
			sessionCookie: &http.Cookie{
				Name:  "session",
				Value: "abc123",
			},
			setupSessions: func() {
				sessions = map[string]struct{}{
					"abc123": {},
				}
			},
			wantStatus:     http.StatusOK,
			wantDeletedKey: "abc123",
		},
		{
			name: "Session not found",
			sessionCookie: &http.Cookie{
				Name:  "session",
				Value: "nonexistent",
			},
			setupSessions: func() {
				sessions = map[string]struct{}{
					"somethingElse": {},
				}
			},
			wantStatus:     http.StatusOK,
			wantDeletedKey: "", // nothing should be removed
		},
		{
			name:          "No session cookie",
			sessionCookie: nil,
			setupSessions: func() {
				sessions = map[string]struct{}{
					"another": {},
				}
			},
			wantStatus:     http.StatusOK,
			wantDeletedKey: "", // nothing removed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupSessions()

			req := httptest.NewRequest("GET", "/api/auth/logout", nil)
			if tt.sessionCookie != nil {
				req.AddCookie(tt.sessionCookie)
			}

			rr := httptest.NewRecorder()
			LogoutHandler(rr, req)

			resp := rr.Result()
			defer resp.Body.Close()

			if resp.StatusCode != tt.wantStatus {
				t.Errorf("Expected status %d, got %d", tt.wantStatus, resp.StatusCode)
			}

			if tt.wantDeletedKey != "" {
				if _, exists := sessions[tt.wantDeletedKey]; exists {
					t.Errorf("Expected session %q to be deleted, but it still exists", tt.wantDeletedKey)
				}
			}

			// Check if expired cookie is returned
			if tt.sessionCookie != nil {
				found := false
				for _, c := range resp.Cookies() {
					if c.Name == "session" {
						found = true
						if c.MaxAge != -1 {
							t.Errorf("Expected cookie MaxAge -1, got %d", c.MaxAge)
						}
					}
				}
				if !found {
					t.Errorf("Expected expired session cookie to be returned")
				}
			}

			// Optional: check response body
			respBody, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("failed to read response body: %v", err)
			}
			if !strings.Contains(string(respBody), "logout successful") {
				t.Errorf("Expected 'logout successful' message, got: %s", string(respBody))
			}
		})
	}
}
