package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// GetGroupJoinRequestsHandler gets pending join requests for groups created by the current user
func GetGroupJoinRequestsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupIDStr := r.URL.Query().Get("group_id")
	var query string
	var args []interface{}

	if groupIDStr != "" {
		// Get requests for a specific group
		groupID, err := strconv.Atoi(groupIDStr)
		if err != nil {
			http.Error(w, "Invalid group ID", http.StatusBadRequest)
			return
		}

		// Check if user is the creator of the group
		var isCreator bool
		err = sq.GetDB().QueryRow(`
			SELECT EXISTS(SELECT 1 FROM groups WHERE id = ? AND creator_id = ?)`,
			groupID, userID).Scan(&isCreator)
		if err != nil || !isCreator {
			http.Error(w, "You are not the creator of this group", http.StatusForbidden)
			return
		}

		query = `
			SELECT gjr.id, gjr.group_id, gjr.user_id, gjr.created_at,
				   u.nickname, u.first_name || ' ' || u.last_name as user_name,
				   COALESCE(u.avatar_url, '') as user_avatar
			FROM group_join_requests gjr
			JOIN users u ON gjr.user_id = u.id
			WHERE gjr.group_id = ? AND gjr.status = 'pending'
			ORDER BY gjr.created_at DESC`
		args = []interface{}{groupID}
	} else {
		// Get all requests for groups created by the user
		query = `
			SELECT gjr.id, gjr.group_id, gjr.user_id, gjr.created_at,
				   u.nickname, u.first_name || ' ' || u.last_name as user_name,
				   COALESCE(u.avatar_url, '') as user_avatar
			FROM group_join_requests gjr
			JOIN users u ON gjr.user_id = u.id
			JOIN groups g ON gjr.group_id = g.id
			WHERE g.creator_id = ? AND gjr.status = 'pending'
			ORDER BY gjr.created_at DESC`
		args = []interface{}{userID}
	}

	rows, err := sq.GetDB().Query(query, args...)
	if err != nil {
		log.Printf("Error getting join requests: %v", err)
		http.Error(w, "Failed to get join requests", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var requests []m.GroupJoinRequest
	for rows.Next() {
		var request m.GroupJoinRequest
		err := rows.Scan(
			&request.ID, &request.GroupID, &request.UserID,
			&request.CreatedAt, &request.UserNickname,
			&request.UserName, &request.UserAvatar,
		)
		if err != nil {
			log.Printf("Error scanning join request: %v", err)
			continue
		}
		requests = append(requests, request)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(requests)
}

// AcceptGroupJoinRequestHandler accepts a group join request
func AcceptGroupJoinRequestHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	requestIDStr := r.URL.Query().Get("request_id")
	if requestIDStr == "" {
		http.Error(w, "Request ID is required", http.StatusBadRequest)
		return
	}

	requestID, err := strconv.Atoi(requestIDStr)
	if err != nil {
		http.Error(w, "Invalid request ID", http.StatusBadRequest)
		return
	}

	// Get request details and verify the user is the group creator
	var groupID int
	var requesterID string
	err = sq.GetDB().QueryRow(`
		SELECT gjr.group_id, gjr.user_id
		FROM group_join_requests gjr
		JOIN groups g ON gjr.group_id = g.id
		WHERE gjr.id = ? AND g.creator_id = ? AND gjr.status = 'pending'`,
		requestID, userID).Scan(&groupID, &requesterID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "Join request not found or you're not authorized", http.StatusNotFound)
		} else {
			log.Printf("Error getting join request: %v", err)
			http.Error(w, "Database error", http.StatusInternalServerError)
		}
		return
	}

	// Begin transaction
	tx, err := sq.GetDB().Begin()
	if err != nil {
		log.Printf("Error beginning transaction: %v", err)
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer tx.Rollback()

	// Update request status
	_, err = tx.Exec(`
		UPDATE group_join_requests SET status = 'accepted'
		WHERE id = ?`, requestID)
	if err != nil {
		log.Printf("Error updating join request: %v", err)
		http.Error(w, "Failed to accept request", http.StatusInternalServerError)
		return
	}

	// Add user to group
	_, err = tx.Exec(`
		INSERT INTO group_members (group_id, user_id, role, joined_at)
		VALUES (?, ?, 'member', ?)`,
		groupID, requesterID, time.Now())
	if err != nil {
		log.Printf("Error adding user to group: %v", err)
		http.Error(w, "Failed to add user to group", http.StatusInternalServerError)
		return
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		log.Printf("Error committing transaction: %v", err)
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	// Send notification to the requester
	go func() {
		var groupName, accepterName string
		sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&groupName)
		sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&accepterName)

		CreateAndBroadcastNotification(
			requesterID,
			"group_join_accepted",
			"Join Request Accepted",
			fmt.Sprintf("Your request to join %s has been accepted", groupName),
			map[string]interface{}{
				"group_id":      groupID,
				"group_name":    groupName,
				"accepter_id":   userID,
				"accepter_name": accepterName,
			},
		)
	}()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "request accepted"})
}

// DeclineGroupJoinRequestHandler declines a group join request
func DeclineGroupJoinRequestHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	requestIDStr := r.URL.Query().Get("request_id")
	if requestIDStr == "" {
		http.Error(w, "Request ID is required", http.StatusBadRequest)
		return
	}

	requestID, err := strconv.Atoi(requestIDStr)
	if err != nil {
		http.Error(w, "Invalid request ID", http.StatusBadRequest)
		return
	}

	// Get request details and verify the user is the group creator
	var groupID int
	var requesterID string
	err = sq.GetDB().QueryRow(`
		SELECT gjr.group_id, gjr.user_id
		FROM group_join_requests gjr
		JOIN groups g ON gjr.group_id = g.id
		WHERE gjr.id = ? AND g.creator_id = ? AND gjr.status = 'pending'`,
		requestID, userID).Scan(&groupID, &requesterID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "Join request not found or you're not authorized", http.StatusNotFound)
		} else {
			log.Printf("Error getting join request: %v", err)
			http.Error(w, "Database error", http.StatusInternalServerError)
		}
		return
	}

	// Update request status
	result, err := sq.GetDB().Exec(`
		UPDATE group_join_requests SET status = 'declined'
		WHERE id = ?`, requestID)
	if err != nil {
		log.Printf("Error declining join request: %v", err)
		http.Error(w, "Failed to decline request", http.StatusInternalServerError)
		return
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("Error getting affected rows: %v", err)
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	if rowsAffected == 0 {
		http.Error(w, "Join request not found", http.StatusNotFound)
		return
	}

	// Send notification to the requester
	go func() {
		var groupName, declinerName string
		sq.GetDB().QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&groupName)
		sq.GetDB().QueryRow("SELECT first_name || ' ' || last_name FROM users WHERE id = ?", userID).Scan(&declinerName)

		CreateAndBroadcastNotification(
			requesterID,
			"group_join_declined",
			"Join Request Declined",
			fmt.Sprintf("Your request to join %s has been declined", groupName),
			map[string]interface{}{
				"group_id":      groupID,
				"group_name":    groupName,
				"decliner_id":   userID,
				"decliner_name": declinerName,
			},
		)
	}()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "request declined"})
}
