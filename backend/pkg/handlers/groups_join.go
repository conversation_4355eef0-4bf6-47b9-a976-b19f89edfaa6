package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
)

type JoinGroupRequest struct {
	GroupID int `json:"group_id"`
}

func JoinGroupHand<PERSON>(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	cookie, err := r.<PERSON>("session_id")
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}
	userID, err := GetUserIDFromSession(cookie.Value)
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	var req JoinGroupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Check if group exists and if it's private
	var isPrivate bool
	err = sq.GetDB().QueryRow("SELECT is_private FROM groups WHERE id = ?", req.GroupID).Scan(&isPrivate)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, "Group not found", http.StatusNotFound)
			return
		}
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	if isPrivate {
		http.Error(w, "This is a private group. Please send a join request.", http.StatusForbidden)
		return
	}

	// Check if user is already a member
	var memberCount int
	err = sq.GetDB().QueryRow("SELECT COUNT(*) FROM group_members WHERE group_id = ? AND user_id = ?", req.GroupID, userID).Scan(&memberCount)
	if err != nil {
		http.Error(w, "Database error checking membership", http.StatusInternalServerError)
		return
	}
	if memberCount > 0 {
		http.Error(w, "User is already a member of this group", http.StatusConflict)
		return
	}

	// Add user to the group
	_, err = sq.GetDB().Exec("INSERT INTO group_members (group_id, user_id, role) VALUES (?, ?, 'member')", req.GroupID, userID)
	if err != nil {
		http.Error(w, "Failed to join group", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Successfully joined group"})
}
