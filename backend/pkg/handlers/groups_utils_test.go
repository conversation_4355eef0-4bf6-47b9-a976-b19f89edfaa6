package handlers

import (
	"database/sql"
	"net/http"
	"testing"

	db "imson/pkg/db/sqlite"
	_ "github.com/mattn/go-sqlite3"
)

func init() {
	// Setup mock database for utility testing
	mockDB, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		panic(err)
	}
	db.DBConn = mockDB

	// Create minimal test schema
	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			nickname TEXT NOT NULL,
			first_name TEXT NOT NULL,
			last_name TEXT NOT NULL,
			email TEXT UNIQUE NOT NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS groups (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_private BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	_, err = mockDB.Exec(`
		CREATE TABLE IF NOT EXISTS group_members (
			id INTEGER PRIMARY KEY,
			group_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			role TEXT DEFAULT 'member'
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert test data
	_, err = mockDB.Exec(`
		INSERT INTO users (id, nickname, first_name, last_name, email)
		VALUES 
			('user123', 'john_doe', 'John', 'Doe', '<EMAIL>'),
			('user456', 'jane_doe', 'Jane', 'Doe', '<EMAIL>')
	`)
	if err != nil {
		panic(err)
	}

	_, err = mockDB.Exec(`
		INSERT INTO groups (id, name, description, creator_id, is_private)
		VALUES 
			(1, 'Tech Group', 'Technology discussions', 'user123', FALSE),
			(2, 'Private Group', 'Private discussions', 'user456', TRUE)
	`)
	if err != nil {
		panic(err)
	}

	_, err = mockDB.Exec(`
		INSERT INTO group_members (group_id, user_id, role)
		VALUES 
			(1, 'user123', 'creator'),
			(1, 'user456', 'member'),
			(2, 'user456', 'creator')
	`)
	if err != nil {
		panic(err)
	}
}

// TestValidateGroupID tests the validateGroupID utility function
func TestValidateGroupID(t *testing.T) {
	tests := []struct {
		name        string
		url         string
		expectError bool
		expectedID  int
	}{
		{"Valid group ID", "/api/groups/preview?group_id=1", false, 1},
		{"Missing group ID", "/api/groups/preview", true, 0},
		{"Invalid group ID", "/api/groups/preview?group_id=invalid", true, 0},
		{"Zero group ID", "/api/groups/preview?group_id=0", false, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", tt.url, nil)
			groupID, err := validateGroupID(req)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if groupID != tt.expectedID {
				t.Errorf("Expected group ID %d, got %d", tt.expectedID, groupID)
			}
		})
	}
}

// TestCheckUserMembership tests the checkUserMembership utility function
func TestCheckUserMembership(t *testing.T) {
	tests := []struct {
		name         string
		groupID      int
		userID       string
		expectedRole string
	}{
		{"Creator membership", 1, "user123", "creator"},
		{"Member membership", 1, "user456", "member"},
		{"Non-member", 1, "nonexistent", ""},
		{"Invalid group", 999, "user123", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			role := checkUserMembership(tt.groupID, tt.userID)
			if role != tt.expectedRole {
				t.Errorf("Expected role '%s', got '%s'", tt.expectedRole, role)
			}
		})
	}
}

// TestIsUserMemberOfGroup tests the isUserMemberOfGroup utility function
func TestIsUserMemberOfGroup(t *testing.T) {
	tests := []struct {
		name           string
		groupID        int
		userID         string
		expectedMember bool
	}{
		{"User is member", 1, "user123", true},
		{"User is member", 1, "user456", true},
		{"User is not member", 1, "nonexistent", false},
		{"Invalid group", 999, "user123", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isMember := isUserMemberOfGroup(tt.groupID, tt.userID)
			if isMember != tt.expectedMember {
				t.Errorf("Expected membership %t, got %t", tt.expectedMember, isMember)
			}
		})
	}
}

// TestCheckGroupAccess tests the checkGroupAccess utility function
func TestCheckGroupAccess(t *testing.T) {
	tests := []struct {
		name              string
		groupID           int
		userID            string
		expectedCanAccess bool
		expectedIsMember  bool
		expectError       bool
	}{
		{"Public group - member", 1, "user123", true, true, false},
		{"Public group - non-member", 1, "nonexistent", true, false, false},
		{"Private group - member", 2, "user456", true, true, false},
		{"Private group - non-member", 2, "user123", false, false, false},
		{"Invalid group", 999, "user123", false, false, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			canAccess, isMember, err := checkGroupAccess(tt.groupID, tt.userID)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if canAccess != tt.expectedCanAccess {
				t.Errorf("Expected canAccess %t, got %t", tt.expectedCanAccess, canAccess)
			}
			if isMember != tt.expectedIsMember {
				t.Errorf("Expected isMember %t, got %t", tt.expectedIsMember, isMember)
			}
		})
	}
}

// TestGetGroupBasicInfo tests the getGroupBasicInfo utility function
func TestGetGroupBasicInfo(t *testing.T) {
	tests := []struct {
		name        string
		groupID     int
		expectError bool
		expectedID  int
	}{
		{"Valid group", 1, false, 1},
		{"Another valid group", 2, false, 2},
		{"Invalid group", 999, true, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			group, err := getGroupBasicInfo(tt.groupID)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if !tt.expectError && group != nil && group.ID != tt.expectedID {
				t.Errorf("Expected group ID %d, got %d", tt.expectedID, group.ID)
			}
		})
	}
}

// TestHandleGroupError tests the handleGroupError utility function
func TestHandleGroupError(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedCalled bool
	}{
		{"No error", nil, 0, false},
		{"SQL no rows error", sql.ErrNoRows, 404, true},
		{"Generic error", sql.ErrConnDone, 500, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			called := false
			mockWriter := &mockResponseWriter{
				writeHeaderFunc: func(status int) {
					called = true
					if status != tt.expectedStatus {
						t.Errorf("Expected status %d, got %d", tt.expectedStatus, status)
					}
				},
			}

			result := handleGroupError(mockWriter, tt.err, "test operation")
			if result != tt.expectedCalled {
				t.Errorf("Expected called %t, got %t", tt.expectedCalled, result)
			}
			if called != tt.expectedCalled {
				t.Errorf("Expected writeHeader called %t, got %t", tt.expectedCalled, called)
			}
		})
	}
}

// Mock ResponseWriter for testing
type mockResponseWriter struct {
	writeHeaderFunc func(int)
}

func (m *mockResponseWriter) Header() http.Header {
	return make(http.Header)
}

func (m *mockResponseWriter) Write([]byte) (int, error) {
	return 0, nil
}

func (m *mockResponseWriter) WriteHeader(status int) {
	if m.writeHeaderFunc != nil {
		m.writeHeaderFunc(status)
	}
}
