package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
)

type RemoveUserRequest struct {
	GroupID int    `json:"group_id"`
	UserID  string `json:"user_id"`
}

func RemoveUserFromGroupHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	cookie, err := r.<PERSON>("session_id")
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}
	adminID, err := GetUserIDFromSession(cookie.Value)
	if err != nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	var req RemoveUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Check if the requesting user is an admin or creator of the group
	var role string
	err = sq.GetDB().QueryRow("SELECT role FROM group_members WHERE group_id = ? AND user_id = ?", req.GroupID, adminID).Scan(&role)
	if err != nil {
		http.Error(w, "Error checking permissions", http.StatusInternalServerError)
		return
	}
	if role != "admin" && role != "creator" {
		http.Error(w, "Only admins or the group creator can remove users", http.StatusForbidden)
		return
	}

	// Remove the user
	_, err = sq.GetDB().Exec("DELETE FROM group_members WHERE group_id = ? AND user_id = ?", req.GroupID, req.UserID)
	if err != nil {
		http.Error(w, "Failed to remove user from group", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "User successfully removed from group"})
}
