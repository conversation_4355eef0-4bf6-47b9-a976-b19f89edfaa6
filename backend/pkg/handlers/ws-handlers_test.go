package handlers

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

type mockClient struct {
	SendCh chan []byte
}

func setupMockClient(userID string) (*mockClient, func()) {
	mc := &mockClient{SendCh: make(chan []byte, 1)}
	clientsMutex.Lock()
	clients[userID] = &Client{
		ID:     userID,
		SendCh: mc.SendCh,
	}
	clientsMutex.Unlock()
	cleanup := func() {
		clientsMutex.Lock()
		delete(clients, userID)
		clientsMutex.Unlock()
	}
	return mc, cleanup
}

func TestSendNotificationToUser_Success(t *testing.T) {
	userID := "123"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	notification := map[string]any{
		"type": "test_type",
		"foo":  "bar",
	}

	SendNotificationToUser(userID, notification)

	select {
	case msg := <-mc.SendCh:
		var wsMsg map[string]any
		err := json.Unmarshal(msg, &wsMsg)
		if err != nil {
			t.Fatalf("Failed to unmarshal message: %v", err)
		}
		assert.Equal(t, "test_type", wsMsg["type"])
		assert.Equal(t, notification, wsMsg["data"])
	default:
		t.Fatal("Expected message on SendCh")
	}
}

// TestSendNotificationToUser_JSONMarshalError simulates a marshal error by passing a non-marshalable value.
func TestSendNotificationToUser_JSONMarshalError(t *testing.T) {
	userID := "999"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	// Channels cannot be marshaled to JSON, so this will cause json.Marshal to fail.
	notification := map[string]interface{}{
		"type": "bad_type",
		"foo":  make(chan int),
	}

	// Should not panic or send anything
	SendNotificationToUser(userID, notification)

	select {
	case <-mc.SendCh:
		t.Fatal("Did not expect message on SendCh due to marshal error")
	default:
		// Expected: nothing sent
	}
}

// TestSendNotificationToUser_SendChannelFull simulates the case where the send channel is full.
func TestSendNotificationToUser_SendChannelFull(t *testing.T) {
	userID := "321"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	notification := map[string]interface{}{
		"type": "test_type",
		"foo":  "bar",
	}

	
	mc.SendCh <- []byte("dummy")

	// Should not block or panic
	SendNotificationToUser(userID, notification)

	// Channel should still only have the dummy message
	select {
	case msg := <-mc.SendCh:
		assert.Equal(t, []byte("dummy"), msg)
	default:
		t.Fatal("Expected dummy message to remain in channel")
	}
}

func TestSendNotificationToUser_UserNotExists(t *testing.T) {
	userID := "notfound"
	notification := map[string]interface{}{"type": "test_type"}
	SendNotificationToUser(userID, notification)
}

func TestBroadcastFollowRequestNotification(t *testing.T) {
	userID := "456"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	BroadcastFollowRequestNotification(456, 123, "Alice", 789)

	select {
	case msg := <-mc.SendCh:
		var wsMsg map[string]interface{}
		json.Unmarshal(msg, &wsMsg)
		assert.Equal(t, "follow_request", wsMsg["type"])
		data := wsMsg["data"].(map[string]interface{})
		assert.Equal(t, "Alice wants to follow you", data["message"])
		assert.Equal(t, float64(123), data["sender_id"])
		assert.Equal(t, float64(789), data["request_id"])
		assert.NotEmpty(t, data["timestamp"])
	default:
		t.Fatal("Expected message on SendCh")
	}
}

func TestBroadcastFollowAcceptedNotification(t *testing.T) {
	userID := "789"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	BroadcastFollowAcceptedNotification(789, 456, "Bob")

	select {
	case msg := <-mc.SendCh:
		var wsMsg map[string]interface{}
		json.Unmarshal(msg, &wsMsg)
		assert.Equal(t, "follow_accepted", wsMsg["type"])
		data := wsMsg["data"].(map[string]interface{})
		assert.Equal(t, "Bob accepted your follow request", data["message"])
		assert.Equal(t, float64(456), data["accepter_id"])
		assert.NotEmpty(t, data["timestamp"])
	default:
		t.Fatal("Expected message on SendCh")
	}
}

func TestBroadcastFollowDeclinedNotification(t *testing.T) {
	userID := "101"
	mc, cleanup := setupMockClient(userID)
	defer cleanup()

	BroadcastFollowDeclinedNotification(101, 202, "Charlie")

	select {
	case msg := <-mc.SendCh:
		var wsMsg map[string]interface{}
		json.Unmarshal(msg, &wsMsg)
		assert.Equal(t, "follow_declined", wsMsg["type"])
		data := wsMsg["data"].(map[string]interface{})
		assert.Equal(t, "Charlie declined your follow request", data["message"])
		assert.Equal(t, float64(202), data["decliner_id"])
		assert.NotEmpty(t, data["timestamp"])
	default:
		t.Fatal("Expected message on SendCh")
	}
}
