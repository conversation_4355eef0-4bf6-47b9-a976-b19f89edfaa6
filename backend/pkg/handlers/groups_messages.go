package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"slices"
	"strconv"
	"time"

	m "imson/pkg/models"
)

// retrieves the group messages based on the group ID(this handler is evoked when the a user visits the page of the grouup chat)
func FetchGroupMessagesHandler(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	query := r.URL.Query()
	groupId := query.Get("id")

	group_messages, err := FetchGroupMessages(r, groupId, db)
	if err != nil {
		http.Error(w, "Failed to fetch group messages", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(group_messages)
}

// retrieves the messages of the group from the database based on the passed groupID
func FetchGroupMessages(r *http.Request, groupId string, db *sql.DB) ([]m.GroupMessage, error) {
	currentUserID, err := GetCurrentUserID(r, db)
	if err != nil {
		return nil, err
	}
	id, _ := strconv.Atoi(groupId)

	// Query only the required slice of messages directly from the database
	rows, _ := db.Query(`
		SELECT id, groupId, sender_id, content, created_at, read_status 
		FROM groups_messages 
		WHERE (sender_id = ? AND id = ?)
		ORDER BY created_at DESC`,
		currentUserID, id,
	)
	defer rows.Close()

	var messages []m.GroupMessage
	for rows.Next() {
		var message m.GroupMessage
		if err := rows.Scan(&message.ID, &message.GroupID, &message.SenderID, &message.Content, &message.CreatedAt, &message.ReadStatus); err != nil {
			fmt.Println("Error scanning group message:", err)
			return nil, err
		}
		messages = append(messages, message)
	}

	return messages, nil // finalGroupMessages(messages, limit, offset), nil
}

// responsible for handling a newly created group message and storing the message to the databbase
func SendGroupMessageHandler(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Id      int    `json:"id"`
		Content string `json:"content"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Content == "" {
		http.Error(w, "Group message content cannot be empty", http.StatusBadRequest)
		return
	}

	senderID, err := GetCurrentUserID(r, db)
	// Sender_id = senderID
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	now := time.Now()

	// storing the message in the database
	result, _ := db.Exec(
		`INSERT INTO groups_messages (groupId, sender_id, content, created_at) VALUES (?, ?, ?, ?)`,
		req.Id, senderID, req.Content, now,
	)

	messageID, err := result.LastInsertId()
	if err != nil {
		http.Error(w, "Failed to retrieve group message ID", http.StatusInternalServerError)
		return
	}
	userID, _ := GetCurrentUserID(r, db)

	message := m.GroupMessage{
		ID:            int(messageID),
		GroupID:       req.Id,
		SenderID:      senderID,
		Content:       req.Content,
		CreatedAt:     now.Format("2006-01-02 15:04:05 -0700 MST"),
		CurrentUserId: userID,
		ReadStatus:    0,
	}

	w.WriteHeader(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(message)
}

// LatestPrivateMessage fetches the most recent group message from the database based on a given group ID
func LatestGroupMessage(groupId int, senderId string, db *sql.DB) (m.GroupMessage, error) {
	var msg m.GroupMessage
	row := db.QueryRow(`
		SELECT id, groupId, sender_id, content, created_at, read_status
		FROM groups_messages
		WHERE sender_id = ?
		ORDER BY created_at DESC
		LIMIT 1
	`, senderId)

	err := row.Scan(&msg.ID, &msg.GroupID, &msg.SenderID, &msg.Content, &msg.CreatedAt, &msg.ReadStatus)
	if err != nil {
		return m.GroupMessage{}, err
	}
	return msg, nil
}

// function to fetch the members IDs in the group based on the group ID
func GetUsersInGroup(db *sql.DB, groupID int) ([]string, error) {
	var userIDs []string

	groupStr := strconv.Itoa(groupID)

	// Use SQL LIKE patterns to match different possible placements of groupID
	query := `
        SELECT id 
        FROM users 
        WHERE groups = ? 
           OR groups LIKE ? 
           OR groups LIKE ? 
           OR groups LIKE ?
    `

	rows, err := db.Query(query,
		groupStr,
		"%,"+groupStr,
		groupStr+",%",
		"%,"+groupStr+",%",
	)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		userIDs = append(userIDs, id)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("row error: %w", err)
	}
	if len(userIDs) == 0 {
		return nil, fmt.Errorf("no users found in group with ID %d", groupID)
	}
	return userIDs, nil
}

// compares the passed userID, checking if it exists in the groups members
func checkMembers(groupID int, userID string, DB *sql.DB) bool {
	members, err := GetUsersInGroup(DB, groupID)
	if err != nil {
		log.Println("Failed to fetch group members:", err)
		return false
	}
	return slices.Contains(members, userID)
}
