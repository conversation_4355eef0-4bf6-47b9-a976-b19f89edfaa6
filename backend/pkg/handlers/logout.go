package handlers

import "net/http"

var sessions map[string]struct{} // sessions stores active session data
// Logout handles user logout requests.
// It checks for a "session" cookie in the request,
// and if found, deletes the corresponding session from the sessions map,
// then invalidates the cookie by setting its MaxAge to -1 and sending it back to the client.
// Finally, it responds with HTTP 200 and a JSON message indicating successful logout.
func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	cookie, err := r.<PERSON>ie("session_id")
	if err == nil {
		delete(sessions, cookie.Value)
		cookie.MaxAge = -1
		http.SetCookie(w, cookie)
	}
	w.Write<PERSON>eader(http.StatusOK)
	w.Write([]byte(`{"message":"logout successful"}`))
}
