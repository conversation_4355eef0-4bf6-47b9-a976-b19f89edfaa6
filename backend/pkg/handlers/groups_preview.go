package handlers

import (
	"encoding/json"
	"net/http"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// GetGroupPreviewHandler returns a lightweight preview of group information
// Purpose: Used for search results, invitations, pop-ups when hovering over group name
// Response: Minimal details - group name, description, member count, creator, membership status
func GetGroupPreviewHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	db := sq.GetDB()
	userID, err := GetCurrentUserID(r, db)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	groupID, err := validateGroupID(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Get basic group information
	group, err := getGroupBasicInfo(groupID)
	if handleGroupError(w, err, "get group preview") {
		return
	}

	// Build preview response
	preview := m.GroupPreview{
		ID:           group.ID,
		Name:         group.Name,
		Description:  group.About,
		MemberCount:  group.Cardinality,
		IsPrivate:    group.IsPrivate,
		CreatorName:  getCreatorName(groupID),
		IsUserMember: checkUserMembership(groupID, userID) != "",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(preview)
}
