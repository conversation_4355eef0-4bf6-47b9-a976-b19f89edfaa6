package handlers

import (
	"encoding/json"
	"log"
	"strconv"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	u "imson/pkg/utils"
)

// SendNotificationToUser sends a notification to a specific user via WebSocket
func SendNotificationToUser(userID string, notification map[string]interface{}) {
	clientsMutex.RLock()
	client, exists := clients[userID]
	clientsMutex.RUnlock()

	if exists {
		// Wrap notification in WebSocket message format
		wsMessage := map[string]interface{}{
			"type": notification["type"],
			"data": notification,
		}

		notificationJSON, err := json.Marshal(wsMessage)
		if err != nil {
			return
		}

		select {
		case client.SendCh <- notificationJSON:
			// Notification sent successfully
		default:
			// Client send channel is full, skip notification
		}
	}
}

// BroadcastFollowRequestNotification sends a follow request notification to the target user
func BroadcastFollowRequestNotification(targetUserID, senderID int, senderName string, requestID int) {
	notification := map[string]interface{}{
		"type":       "follow_request",
		"message":    senderName + " wants to follow you",
		"sender_id":  senderID,
		"request_id": requestID,
		"timestamp":  getCurrentTimestamp(),
	}

	SendNotificationToUser(strconv.Itoa(targetUserID), notification)
}

// BroadcastFollowAcceptedNotification sends acceptance notification to the original requester
func BroadcastFollowAcceptedNotification(requesterID, accepterID int, accepterName string) {
	notification := map[string]interface{}{
		"type":        "follow_accepted",
		"message":     accepterName + " accepted your follow request",
		"accepter_id": accepterID,
		"timestamp":   getCurrentTimestamp(),
	}

	SendNotificationToUser(strconv.Itoa(requesterID), notification)
}

// BroadcastFollowDeclinedNotification sends decline notification to the original requester
func BroadcastFollowDeclinedNotification(requesterID, declinerID int, declinerName string) {
	notification := map[string]interface{}{
		"type":        "follow_declined",
		"message":     declinerName + " declined your follow request",
		"decliner_id": declinerID,
		"timestamp":   getCurrentTimestamp(),
	}

	SendNotificationToUser(strconv.Itoa(requesterID), notification)
}

// handlers for private messages
func handlePrivateMsg(data json.RawMessage) {
	var payload struct {
		Content string `json:"content"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse private message payload:", err)
		return
	}

	message, err := LatestPrivateMessage()
	if err != nil {
		log.Println("Error fetching latest private message:", err)
	}

	defer clientsMutex.RUnlock()

	if receiverClient, ok := clients[message.ReceiverID]; ok {
		messageCopy := message
		messageCopy.CurrentUserId = message.ReceiverID
		msg := m.WSMessage{
			Type: "private_msg",
			Data: u.MustMarshal(messageCopy),
		}
		msgBytes, _ := json.Marshal(msg)
		receiverClient.SendCh <- msgBytes
	}

	if senderClient, ok := clients[message.SenderID]; ok && message.SenderID != message.ReceiverID {
		messageCopy := message
		messageCopy.CurrentUserId = message.SenderID
		msg := m.WSMessage{
			Type: "private_msg",
			Data: u.MustMarshal(messageCopy),
		}
		msgBytes, _ := json.Marshal(msg)
		senderClient.SendCh <- msgBytes
	}
}

// this fucntion broadcasts posts to all the clients through the websocket.
func handleNewPostBroadcast(data json.RawMessage) {
	var payload struct {
		Title    string `json:"title"`
		Content  string `json:"content"`
		Category string `json:"category"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse post broadcast payload:", err)
		return
	}

	postID := PostID_posting
	post, err := FetchLatestPostFromDb(int(postID))
	if err != nil {
		log.Println("Error fetching latest post:", err)
		return
	}

	msg := m.WSMessage{
		Type: "new_post",
		Data: u.MustMarshal(post),
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		log.Println("Failed to marshal post message:", err)
		return
	}

	clientsMutex.RLock()
	for _, client := range clients {
		client.SendCh <- msgBytes
	}
	clientsMutex.RUnlock()
}

// this function handles and sends a post like through the websocket
func handlePostLike(data json.RawMessage) {
	var payload struct {
		PostID string `json:"post_id"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse like payload:", err)
		return
	}

	postID, _ := strconv.Atoi(payload.PostID)
	likes, _ := GetPostLikes(postID)

	msg := m.WSMessage{
		Type: "like_post",
		Data: u.MustMarshal(m.LikePostObject{
			PostID: postID,
			Likes:  likes,
		}),
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		log.Println("Failed to marshal like post object:", err)
		return
	}

	clientsMutex.RLock()
	for _, client := range clients {
		client.SendCh <- msgBytes
	}
	clientsMutex.RUnlock()
}

// handlers for group messages
func handleGroupMessages(data json.RawMessage) {
	var payload struct {
		GroupId int    `json:"groupId"`
		Content string `json:"content"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse group message payload:", err)
		return
	}

	message, err := LatestGroupMessage(payload.GroupId, Sender_id, DB)
	if err != nil {
		log.Println("Error fetching latest group message:", err)
	}

	clientsMutex.RLock()
	defer clientsMutex.RUnlock()
	for userID, client := range clients {
		if checkMembers(message.GroupID, userID, DB) {
			messageCopy := message
			messageCopy.CurrentUserId = userID

			msg := m.WSMessage{
				Type: "group_mssg",
				Data: u.MustMarshal(messageCopy),
			}

			msgBytes, err := json.Marshal(msg)
			if err != nil {
				log.Println("Failed to marshal group message:", err)
				continue
			}
			client.SendCh <- msgBytes
		}
	}
}

// handleNotification handles real-time notification broadcasting
func handleNotification(data json.RawMessage) {
	var payload struct {
		UserID       string      `json:"user_id"`
		Notification interface{} `json:"notification"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse notification payload:", err)
		return
	}

	// Send notification to specific user
	SendNotificationToUser(payload.UserID, map[string]interface{}{
		"type": "notification",
		"data": payload.Notification,
	})
}

// handleEventNotification handles event-related notifications
func handleEventNotification(data json.RawMessage) {
	var payload struct {
		EventID   int    `json:"event_id"`
		GroupID   int    `json:"group_id"`
		EventType string `json:"event_type"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse event notification payload:", err)
		return
	}

	// Get event details
	var eventTitle, creatorID, creatorName, groupName string
	err := sq.GetDB().QueryRow(`
		SELECT e.title, e.creator_id, u.first_name || ' ' || u.last_name, g.name
		FROM events e
		JOIN users u ON e.creator_id = u.id
		JOIN groups g ON e.group_id = g.id
		WHERE e.id = ?`,
		payload.EventID).Scan(&eventTitle, &creatorID, &creatorName, &groupName)
	if err != nil {
		log.Printf("Error getting event details: %v", err)
		return
	}

	// Get all group members except the creator
	rows, err := sq.GetDB().Query(`
		SELECT user_id FROM group_members 
		WHERE group_id = ? AND user_id != ?`,
		payload.GroupID, creatorID)
	if err != nil {
		log.Printf("Error getting group members: %v", err)
		return
	}
	defer rows.Close()

	eventData := m.EventNotificationData{
		EventID:     payload.EventID,
		EventTitle:  eventTitle,
		GroupID:     payload.GroupID,
		GroupName:   groupName,
		CreatorID:   creatorID,
		CreatorName: creatorName,
	}

	// Send notification to each member
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			continue
		}

		SendNotificationToUser(memberID, map[string]interface{}{
			"type": "event_created",
			"data": eventData,
		})
	}
}

// getCurrentTimestamp returns current timestamp in ISO format
func getCurrentTimestamp() string {
	return time.Now().UTC().Format(time.RFC3339)
}

// handleJoinGroupPage handles when a user joins a group page (for presence tracking)
func handleJoinGroupPage(data json.RawMessage) {
	var payload struct {
		GroupID int    `json:"group_id"`
		UserID  string `json:"user_id"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse join group page payload:", err)
		return
	}

	// Broadcast to other group members that this user is now viewing the group
	broadcastGroupPresence(payload.GroupID, payload.UserID, "joined_page")
}

// handleLeaveGroupPage handles when a user leaves a group page
func handleLeaveGroupPage(data json.RawMessage) {
	var payload struct {
		GroupID int    `json:"group_id"`
		UserID  string `json:"user_id"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse leave group page payload:", err)
		return
	}

	// Broadcast to other group members that this user left the group page
	broadcastGroupPresence(payload.GroupID, payload.UserID, "left_page")
}

// handleGroupTyping handles typing indicators in group chat
func handleGroupTyping(data json.RawMessage) {
	var payload struct {
		GroupID  int    `json:"group_id"`
		UserID   string `json:"user_id"`
		IsTyping bool   `json:"is_typing"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse group typing payload:", err)
		return
	}

	// Broadcast typing indicator to other group members
	broadcastGroupTyping(payload.GroupID, payload.UserID, payload.IsTyping)
}

// broadcastGroupPresence broadcasts user presence changes to group members
func broadcastGroupPresence(groupID int, userID string, action string) {
	// Get all group members except the user who triggered the action
	rows, err := sq.GetDB().Query(`
		SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?`,
		groupID, userID)
	if err != nil {
		log.Printf("Error getting group members for presence broadcast: %v", err)
		return
	}
	defer rows.Close()

	// Create presence message
	presenceMessage := map[string]interface{}{
		"type": "group_presence",
		"data": map[string]interface{}{
			"group_id":  groupID,
			"user_id":   userID,
			"action":    action,
			"timestamp": getCurrentTimestamp(),
		},
	}

	messageJSON, err := json.Marshal(presenceMessage)
	if err != nil {
		log.Printf("Error marshaling presence message: %v", err)
		return
	}

	// Send to all other group members
	clientsMutex.RLock()
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			continue
		}

		if client, exists := clients[memberID]; exists {
			select {
			case client.SendCh <- messageJSON:
				// Message sent successfully
			default:
				// Client send channel is full, skip
			}
		}
	}
	clientsMutex.RUnlock()
}

// handleNewReply broadcasts new replies to all clients through websocket
func handleNewReply(data json.RawMessage) {
	var payload struct {
		CommentID int    `json:"comment_id"`
		Content   string `json:"content"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse reply broadcast payload:", err)
		return
	}

	// Get the latest reply for this comment
	replies, err := FetchRepliesByCommentID(payload.CommentID)
	if err != nil || len(replies) == 0 {
		log.Println("Error fetching latest reply:", err)
		return
	}

	// Get the most recent reply (last in the slice since ordered by created_at ASC)
	latestReply := replies[len(replies)-1]

	msg := m.WSMessage{
		Type: "new_reply",
		Data: u.MustMarshal(latestReply),
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		log.Println("Failed to marshal reply message:", err)
		return
	}

	clientsMutex.RLock()
	for _, client := range clients {
		client.SendCh <- msgBytes
	}
	clientsMutex.RUnlock()
}

// broadcastGroupTyping broadcasts typing indicators to group members
func broadcastGroupTyping(groupID int, userID string, isTyping bool) {
	// Get all group members except the user who is typing
	rows, err := sq.GetDB().Query(`
		SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?`,
		groupID, userID)
	if err != nil {
		log.Printf("Error getting group members for typing broadcast: %v", err)
		return
	}
	defer rows.Close()

	// Create typing message
	typingMessage := map[string]interface{}{
		"type": "group_typing",
		"data": map[string]interface{}{
			"group_id":  groupID,
			"user_id":   userID,
			"is_typing": isTyping,
			"timestamp": getCurrentTimestamp(),
		},
	}

	messageJSON, err := json.Marshal(typingMessage)
	if err != nil {
		log.Printf("Error marshaling typing message: %v", err)
		return
	}

	// Send to all other group members
	clientsMutex.RLock()
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			continue
		}

		if client, exists := clients[memberID]; exists {
			select {
			case client.SendCh <- messageJSON:
				// Message sent successfully
			default:
				// Client send channel is full, skip
			}
		}
	}
	clientsMutex.RUnlock()
}

// handleReplyLike handles and sends reply likes through websocket
func handleReplyLike(data json.RawMessage) {
	var payload struct {
		ReplyID string `json:"reply_id"`
	}

	if err := json.Unmarshal(data, &payload); err != nil {
		log.Println("Failed to parse reply like payload:", err)
		return
	}

	replyID, _ := strconv.Atoi(payload.ReplyID)
	likes, _ := GetReplyLikes(replyID)

	msg := m.WSMessage{
		Type: "like_reply",
		Data: u.MustMarshal(m.LikeReplyObject{
			ReplyID: replyID,
			Likes:   likes,
		}),
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		log.Println("Failed to marshal reply like object:", err)
		return
	}

	clientsMutex.RLock()
	for _, client := range clients {
		client.SendCh <- msgBytes
	}
	clientsMutex.RUnlock()
}
