package services

import (
	"database/sql"
	"errors"

	db "imson/pkg/db/sqlite"

	"golang.org/x/crypto/bcrypt"
)

// AuthenticateUser verifies a user's email and password.
// It queries the database for the user with the given email,
// retrieves the stored hashed password, and compares it with the provided password.
// Returns the user ID as a string.

func AuthenticateUser(email, password string) (string, error) {
	var userID string
	var hash string

	err := db.DBConn.QueryRow("SELECT id, password FROM users WHERE email = ?", email).Scan(&userID, &hash)
	if err == sql.ErrNoRows {
		return "", errors.New("invalid email or password")
	} else if err != nil {
		return "", err
	}

	if bcrypt.CompareHashAndPassword([]byte(hash), []byte(password)) != nil {
		return "", errors.New("invalid email or password")
	}

	return userID, nil
}

func HashPassword(raw string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(raw), bcrypt.DefaultCost)
	return string(bytes), err
}
