package services

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
)

// NotificationService handles all notification operations
type NotificationService struct {
	db *sql.DB
}

// NewNotificationService creates a new notification service
func NewNotificationService() *NotificationService {
	return &NotificationService{
		db: sq.GetDB(),
	}
}

// CreateNotification creates a new notification
func (ns *NotificationService) CreateNotification(userID, notificationType, title, message string, data interface{}) (*m.Notification, error) {
	var dataJSON []byte
	var err error
	
	if data != nil {
		dataJSON, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal notification data: %v", err)
		}
	}

	result, err := ns.db.Exec(`
		INSERT INTO notifications (user_id, type, title, message, data, created_at)
		VALUES (?, ?, ?, ?, ?, ?)`,
		userID, notificationType, title, message, dataJSON, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get notification ID: %v", err)
	}

	notification := &m.Notification{
		ID:        int(id),
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		Data:      dataJSON,
		IsRead:    false,
		CreatedAt: time.Now(),
	}

	return notification, nil
}

// GetUserNotifications retrieves all notifications for a user
func (ns *NotificationService) GetUserNotifications(userID string, limit, offset int) ([]m.Notification, error) {
	query := `
		SELECT id, user_id, type, title, message, data, is_read, created_at
		FROM notifications
		WHERE user_id = ?
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?`

	rows, err := ns.db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get notifications: %v", err)
	}
	defer rows.Close()

	var notifications []m.Notification
	for rows.Next() {
		var notification m.Notification
		var data sql.NullString

		err := rows.Scan(
			&notification.ID,
			&notification.UserID,
			&notification.Type,
			&notification.Title,
			&notification.Message,
			&data,
			&notification.IsRead,
			&notification.CreatedAt,
		)
		if err != nil {
			log.Printf("Error scanning notification: %v", err)
			continue
		}

		if data.Valid {
			notification.Data = json.RawMessage(data.String)
		}

		notifications = append(notifications, notification)
	}

	return notifications, nil
}

// GetUnreadNotificationCount gets the count of unread notifications for a user
func (ns *NotificationService) GetUnreadNotificationCount(userID string) (int, error) {
	var count int
	err := ns.db.QueryRow(`
		SELECT COUNT(*) FROM notifications 
		WHERE user_id = ? AND is_read = FALSE`,
		userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get unread notification count: %v", err)
	}
	return count, nil
}

// MarkNotificationsAsRead marks notifications as read
func (ns *NotificationService) MarkNotificationsAsRead(userID string, notificationIDs []int) error {
	if len(notificationIDs) == 0 {
		return nil
	}

	// Build the query with placeholders
	query := `UPDATE notifications SET is_read = TRUE WHERE user_id = ? AND id IN (`
	args := []interface{}{userID}
	
	for i, id := range notificationIDs {
		if i > 0 {
			query += ","
		}
		query += "?"
		args = append(args, id)
	}
	query += ")"

	_, err := ns.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to mark notifications as read: %v", err)
	}

	return nil
}

// MarkAllNotificationsAsRead marks all notifications as read for a user
func (ns *NotificationService) MarkAllNotificationsAsRead(userID string) error {
	_, err := ns.db.Exec(`
		UPDATE notifications SET is_read = TRUE 
		WHERE user_id = ? AND is_read = FALSE`,
		userID)
	if err != nil {
		return fmt.Errorf("failed to mark all notifications as read: %v", err)
	}
	return nil
}

// DeleteNotification deletes a notification
func (ns *NotificationService) DeleteNotification(userID string, notificationID int) error {
	result, err := ns.db.Exec(`
		DELETE FROM notifications 
		WHERE id = ? AND user_id = ?`,
		notificationID, userID)
	if err != nil {
		return fmt.Errorf("failed to delete notification: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected rows: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("notification not found or not owned by user")
	}

	return nil
}

// Specific notification creation methods

// CreateFollowRequestNotification creates a follow request notification
func (ns *NotificationService) CreateFollowRequestNotification(targetUserID, requesterID, requesterName string, requestID int) error {
	data := m.FollowRequestNotificationData{
		RequesterID:   requesterID,
		RequesterName: requesterName,
		RequestID:     requestID,
	}

	_, err := ns.CreateNotification(
		targetUserID,
		"follow_request",
		"New Follow Request",
		fmt.Sprintf("%s wants to follow you", requesterName),
		data,
	)
	return err
}

// CreateGroupInvitationNotification creates a group invitation notification
func (ns *NotificationService) CreateGroupInvitationNotification(inviteeID, inviterID, inviterName string, groupID int, groupName string, invitationID int) error {
	data := m.GroupInvitationNotificationData{
		GroupID:      groupID,
		GroupName:    groupName,
		InviterID:    inviterID,
		InviterName:  inviterName,
		InvitationID: invitationID,
	}

	_, err := ns.CreateNotification(
		inviteeID,
		"group_invitation",
		"Group Invitation",
		fmt.Sprintf("%s invited you to join %s", inviterName, groupName),
		data,
	)
	return err
}

// CreateGroupJoinRequestNotification creates a group join request notification
func (ns *NotificationService) CreateGroupJoinRequestNotification(creatorID, requesterID, requesterName string, groupID int, groupName string, requestID int) error {
	data := m.GroupJoinRequestNotificationData{
		GroupID:       groupID,
		GroupName:     groupName,
		RequesterID:   requesterID,
		RequesterName: requesterName,
		RequestID:     requestID,
	}

	_, err := ns.CreateNotification(
		creatorID,
		"group_join_request",
		"Group Join Request",
		fmt.Sprintf("%s wants to join %s", requesterName, groupName),
		data,
	)
	return err
}

// CreateEventNotification creates an event notification for all group members
func (ns *NotificationService) CreateEventNotification(groupID int, eventID int, eventTitle, creatorID, creatorName string) error {
	// Get group name
	var groupName string
	err := ns.db.QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&groupName)
	if err != nil {
		return fmt.Errorf("failed to get group name: %v", err)
	}

	// Get all group members except the creator
	rows, err := ns.db.Query(`
		SELECT user_id FROM group_members 
		WHERE group_id = ? AND user_id != ?`,
		groupID, creatorID)
	if err != nil {
		return fmt.Errorf("failed to get group members: %v", err)
	}
	defer rows.Close()

	data := m.EventNotificationData{
		EventID:     eventID,
		EventTitle:  eventTitle,
		GroupID:     groupID,
		GroupName:   groupName,
		CreatorID:   creatorID,
		CreatorName: creatorName,
	}

	// Create notification for each member
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			log.Printf("Error scanning member ID: %v", err)
			continue
		}

		_, err := ns.CreateNotification(
			memberID,
			"event_created",
			"New Event",
			fmt.Sprintf("New event '%s' created in %s by %s", eventTitle, groupName, creatorName),
			data,
		)
		if err != nil {
			log.Printf("Failed to create event notification for user %s: %v", memberID, err)
		}
	}

	return nil
}

// CreateGroupMessageNotification creates a notification for new group messages
func (ns *NotificationService) CreateGroupMessageNotification(groupID int, senderID, senderName, messageContent string) error {
	// Get group name
	var groupName string
	err := ns.db.QueryRow("SELECT name FROM groups WHERE id = ?", groupID).Scan(&groupName)
	if err != nil {
		return fmt.Errorf("failed to get group name: %v", err)
	}

	// Get all group members except the sender
	rows, err := ns.db.Query(`
		SELECT user_id FROM group_members 
		WHERE group_id = ? AND user_id != ?`,
		groupID, senderID)
	if err != nil {
		return fmt.Errorf("failed to get group members: %v", err)
	}
	defer rows.Close()

	// Truncate message for preview
	messagePreview := messageContent
	if len(messagePreview) > 50 {
		messagePreview = messagePreview[:50] + "..."
	}

	data := m.GroupMessageNotificationData{
		GroupID:        groupID,
		GroupName:      groupName,
		SenderID:       senderID,
		SenderName:     senderName,
		MessagePreview: messagePreview,
	}

	// Create notification for each member
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			log.Printf("Error scanning member ID: %v", err)
			continue
		}

		_, err := ns.CreateNotification(
			memberID,
			"group_message",
			"New Group Message",
			fmt.Sprintf("%s sent a message in %s", senderName, groupName),
			data,
		)
		if err != nil {
			log.Printf("Failed to create group message notification for user %s: %v", memberID, err)
		}
	}

	return nil
}