package services

import (
	"database/sql"

	sq "imson/pkg/db/sqlite"
)

// ValidateReplyPermission checks if a user has permission to reply to a comment
// This includes checking if the comment exists and if the user can access it
func ValidateReplyPermission(userID string, commentID int) error {
	var exists bool
	err := sq.GetDB().QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM comments c
			JOIN posts p ON c.post_id = p.id
			WHERE c.id = ? AND (
				p.visibility = 'public' OR 
				p.user_id = ? OR
				EXISTS(SELECT 1 FROM followers f WHERE f.following_id = p.user_id AND f.follower_id = ? AND f.status = 'accepted')
			)
		)`, commentID, userID, userID).Scan(&exists)
	if err != nil {
		return err
	}

	if !exists {
		return sql.ErrNoRows
	}

	return nil
}

// GetReplyAuthorID returns the user ID of the reply author
func GetReplyAuthorID(replyID int) (string, error) {
	var authorID string
	err := sq.GetDB().QueryRow("SELECT user_id FROM replies WHERE id = ?", replyID).Scan(&authorID)
	return authorID, err
}

// GetCommentAuthorID returns the user ID of the comment author
// func GetCommentAuthorID(commentID int) (string, error) {
// 	var authorID string
// 	err := sq.GetDB().QueryRow("SELECT user_id FROM comments WHERE id = ?", commentID).Scan(&authorID)
// 	return authorID, err
// }

// CountRepliesForComment returns the number of replies for a specific comment
// func CountRepliesForComment(commentID int) (int, error) {
// 	var count int
// 	err := sq.GetDB().QueryRow("SELECT COUNT(*) FROM replies WHERE comment_id = ?", commentID).Scan(&count)
// 	return count, err
// }
