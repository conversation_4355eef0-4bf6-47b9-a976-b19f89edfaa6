package services

import (
	"database/sql"
	"fmt"

	db "imson/pkg/db/sqlite"
	"imson/pkg/models"
)

// IsPrivateProfile checks if the user with the given userID has a private profile.
// It returns true if the profile is private, false otherwise.
// If the user does not exist, it returns sql.ErrNoRows error.
func IsPrivateProfile(userID int) (bool, error) {
	var isPrivate bool
	err := db.GetDB().QueryRow("SELECT is_private FROM users WHERE id = ?", userID).Scan(&isPrivate)
	if err == sql.ErrNoRows {
		return false, sql.ErrNoRows
	}
	return isPrivate, err
}

// CreateFollowRequest creates a follow request from follower<PERSON> to followingID with the given status.
// It inserts a new record into the followers table and returns any error encountered.
func CreateFollowRequest(followerID, followingID int, status string) error {
	// Check if a follow request already exists
	var existingStatus string
	err := db.GetDB().QueryRow(`
		SELECT status FROM followers 
		WHERE follower_id = ? AND following_id = ?
	`, followerID, followingID).Scan(&existingStatus)

	if err == nil {
		// Request already exists
		if existingStatus == "pending" {
			return fmt.Errorf("follow request already pending")
		} else if existingStatus == "accepted" {
			return fmt.Errorf("already following this user")
		} else if existingStatus == "declined" {
			// Update the declined request to pending
			_, err = db.GetDB().Exec(`
				UPDATE followers SET status = ?, created_at = CURRENT_TIMESTAMP
				WHERE follower_id = ? AND following_id = ?
			`, status, followerID, followingID)
			return err
		}
	} else if err != sql.ErrNoRows {
		return err
	}

	// Create new follow request
	_, err = db.GetDB().Exec(`
		INSERT INTO followers (follower_id, following_id, status)
		VALUES (?, ?, ?)
	`, followerID, followingID, status)
	return err
}

// UpdateFollowRequestStatus updates the status of a follow request
func UpdateFollowRequestStatus(requestID, userID int, status string) error {
	result, err := db.GetDB().Exec(`
		UPDATE followers 
		SET status = ? 
		WHERE id = ? AND following_id = ? AND status = 'pending'
	`, status, requestID, userID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("follow request not found or not pending")
	}

	return nil
}

// GetPendingFollowRequests returns all pending follow requests for a user
func GetPendingFollowRequests(userID int) ([]models.FollowRequest, error) {
	rows, err := db.GetDB().Query(`
		SELECT f.id, f.follower_id, u.nickname, u.first_name, u.last_name, 
		       u.avatar_url, f.created_at
		FROM followers f
		JOIN users u ON f.follower_id = u.id
		WHERE f.following_id = ? AND f.status = 'pending'
		ORDER BY f.created_at DESC
	`, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var requests []models.FollowRequest
	for rows.Next() {
		var req models.FollowRequest
		var avatarURL sql.NullString

		err := rows.Scan(
			&req.ID, &req.FollowerID, &req.Nickname,
			&req.FirstName, &req.LastName, &avatarURL, &req.CreatedAt,
		)
		if err != nil {
			return nil, err
		}

		if avatarURL.Valid {
			req.AvatarURL = avatarURL.String
		}

		requests = append(requests, req)
	}

	return requests, nil
}

// GetFollowerInfoByRequestID returns follower information for a specific request
func GetFollowerInfoByRequestID(requestID int) (*models.FollowerInfo, error) {
	var info models.FollowerInfo
	err := db.GetDB().QueryRow(`
		SELECT follower_id, following_id 
		FROM followers 
		WHERE id = ?
	`, requestID).Scan(&info.FollowerID, &info.FollowingID)
	if err != nil {
		return nil, err
	}

	return &info, nil
}

// SendNotificationToUser sends a WebSocket notification to a specific user
func SendNotificationToUser(userID int, data map[string]interface{}) {
	fmt.Printf("Sending notification to user %d: %+v\n", userID, data)
}
