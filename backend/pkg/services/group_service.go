package services

import (
	"database/sql"
	"log"

	sq "imson/pkg/db/sqlite"
	m "imson/pkg/models"
	wsstatus "imson/pkg/websocket"
)

// GroupService handles complex group operations
type GroupService struct{}

// NewGroupService creates a new group service instance
func NewGroupService() *GroupService {
	return &GroupService{}
}

// GetGroupMembersWithStatus returns group members with online status for the sidebar
func (gs *GroupService) GetGroupMembersWithStatus(groupID int) []map[string]interface{} {
	var members []map[string]interface{}

	rows, err := sq.GetDB().Query(`
		SELECT gm.id, gm.group_id, gm.user_id, gm.role, gm.joined_at,
			   u.nickname, u.first_name, u.last_name, COALESCE(u.avatar_url, '') as avatar_url
		FROM group_members gm
		JOIN users u ON gm.user_id = u.id
		WHERE gm.group_id = ?
		ORDER BY CASE gm.role WHEN 'creator' THEN 1 WHEN 'admin' THEN 2 ELSE 3 END, gm.joined_at ASC`,
		groupID)
	if err != nil {
		log.Printf("Error getting group members: %v", err)
		return members
	}
	defer rows.Close()

	// Get WebSocket status manager
	statusManager := wsstatus.GetStatusManager()

	for rows.Next() {
		var member m.GroupMember
		err := rows.Scan(&member.ID, &member.GroupID, &member.UserID, &member.Role, &member.JoinedAt,
			&member.Nickname, &member.FirstName, &member.LastName, &member.AvatarURL)
		if err != nil {
			log.Printf("Error scanning member: %v", err)
			continue
		}

		// Get real-time online status from WebSocket manager
		statusDetails := statusManager.GetUserStatusWithDetails(member.UserID)

		memberWithStatus := map[string]interface{}{
			"id":         member.ID,
			"group_id":   member.GroupID,
			"user_id":    member.UserID,
			"role":       member.Role,
			"joined_at":  member.JoinedAt,
			"nickname":   member.Nickname,
			"first_name": member.FirstName,
			"last_name":  member.LastName,
			"avatar_url": member.AvatarURL,
			"is_online":  statusDetails["is_online"],
			"status":     statusDetails["status"],
			"last_seen":  statusDetails["last_seen"],
		}
		members = append(members, memberWithStatus)
	}

	return members
}

// GetRecentGroupMessages returns recent messages for the chat interface
func (gs *GroupService) GetRecentGroupMessages(groupID int, limit int) []m.GroupMessage {
	var messages []m.GroupMessage

	rows, err := sq.GetDB().Query(`
		SELECT gm.id, gm.group_id, gm.sender_id, gm.content, gm.created_at,
			   u.nickname as sender_nickname, u.first_name || ' ' || u.last_name as sender_name
		FROM group_messages gm
		JOIN users u ON gm.sender_id = u.id
		WHERE gm.group_id = ?
		ORDER BY gm.created_at DESC
		LIMIT ?`, groupID, limit)
	if err != nil {
		log.Printf("Error getting recent messages: %v", err)
		return messages
	}
	defer rows.Close()

	var tempMessages []m.GroupMessage
	for rows.Next() {
		var message m.GroupMessage
		err := rows.Scan(&message.ID, &message.GroupID, &message.SenderID, &message.Content,
			&message.CreatedAt)
		if err != nil {
			log.Printf("Error scanning message: %v", err)
			continue
		}
		tempMessages = append(tempMessages, message)
	}

	// Reverse to get chronological order (oldest first)
	for i := len(tempMessages) - 1; i >= 0; i-- {
		messages = append(messages, tempMessages[i])
	}

	return messages
}

// GetGroupActivityFeed returns recent activities in the group
func (gs *GroupService) GetGroupActivityFeed(groupID int, limit int) []map[string]interface{} {
	var activities []map[string]interface{}

	// Get combined activity feed (messages and events)
	rows, err := sq.GetDB().Query(`
		SELECT type, related_id, user_id, created_at, content, user_name, user_avatar
		FROM (
			SELECT 'message' as type, gm.id as related_id, gm.sender_id as user_id, gm.created_at, gm.content,
				   u.first_name || ' ' || u.last_name as user_name, COALESCE(u.avatar_url, '') as user_avatar
			FROM group_messages gm
			JOIN users u ON gm.sender_id = u.id
			WHERE gm.group_id = ?
			UNION ALL
			SELECT 'event' as type, e.id as related_id, e.creator_id as user_id, e.created_at, 
				   'created event: ' || e.title as content,
				   u.first_name || ' ' || u.last_name as user_name, COALESCE(u.avatar_url, '') as user_avatar
			FROM events e
			JOIN users u ON e.creator_id = u.id
			WHERE e.group_id = ?
		) activity
		ORDER BY created_at DESC
		LIMIT ?`, groupID, groupID, limit)
	if err != nil {
		log.Printf("Error getting activity feed: %v", err)
		return activities
	}
	defer rows.Close()

	for rows.Next() {
		var activity map[string]interface{} = make(map[string]interface{})
		var actType, content, userName, userAvatar, timestamp string
		var relatedID, userID string

		err := rows.Scan(&actType, &relatedID, &userID, &timestamp, &content, &userName, &userAvatar)
		if err != nil {
			log.Printf("Error scanning activity: %v", err)
			continue
		}

		activity["type"] = actType
		activity["related_id"] = relatedID
		activity["user_id"] = userID
		activity["timestamp"] = timestamp
		activity["content"] = content
		activity["user_name"] = userName
		activity["user_avatar"] = userAvatar
		activity["group_id"] = groupID

		activities = append(activities, activity)
	}

	return activities
}

// CheckGroupInvitation checks if a user has a pending invitation to a group
func (gs *GroupService) CheckGroupInvitation(groupID int, userID string) bool {
	var count int
	err := sq.GetDB().QueryRow(`
		SELECT COUNT(*) FROM group_invitations 
		WHERE group_id = ? AND invitee_id = ? AND status = 'pending'`,
		groupID, userID).Scan(&count)
	if err != nil {
		if err != sql.ErrNoRows {
			log.Printf("Error checking for invitation: %v", err)
		}
		return false
	}

	return count > 0
}

// GetGroupOnlineCount returns the number of online members in a group
func (gs *GroupService) GetGroupOnlineCount(groupID int) int {
	// Get all group member IDs
	var userIDs []string
	rows, err := sq.GetDB().Query(`
		SELECT user_id FROM group_members WHERE group_id = ?`, groupID)
	if err != nil {
		log.Printf("Error getting group members for online count: %v", err)
		return 0
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err != nil {
			log.Printf("Error scanning user ID: %v", err)
			continue
		}
		userIDs = append(userIDs, userID)
	}

	// Get online count from WebSocket status manager
	statusManager := wsstatus.GetStatusManager()
	return statusManager.GetOnlineCount(userIDs)
}

// GetGroupMemberIDs returns all member IDs for a group (useful for WebSocket operations)
func (gs *GroupService) GetGroupMemberIDs(groupID int) []string {
	var userIDs []string
	rows, err := sq.GetDB().Query(`
		SELECT user_id FROM group_members WHERE group_id = ?`, groupID)
	if err != nil {
		log.Printf("Error getting group member IDs: %v", err)
		return userIDs
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err != nil {
			log.Printf("Error scanning user ID: %v", err)
			continue
		}
		userIDs = append(userIDs, userID)
	}

	return userIDs
}
