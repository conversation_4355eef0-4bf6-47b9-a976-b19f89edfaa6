package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"
)

// this function joins the categories with a comma into one string before storing it into the database.
func JoinCategories(categories []string) string {
	return strings.Join(categories, ",")
}

// this function martials data that is sent into the web socket
func MustMarshal(v interface{}) json.RawMessage {
	data, err := json.Marshal(v)
	if err != nil {
		log.Printf("Failed to marshal data: %v", err)
		return nil
	}
	return data
}

// SplitAndTrim takes a comma-separated string and returns a slice of trimmed strings.
func SplitAndTrim(input string) []string {
	if input == "" {
		return []string{}
	}

	parts := strings.Split(input, ",")
	var result []string
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

func CalculateAge(dob string) (int, error) {
	// Parse input date string
	birthDate, err := time.Parse("2006-01-02", dob)
	if err != nil {
		return 0, fmt.Errorf("invalid date format: %v", err)
	}

	now := time.Now()

	age := now.Year() - birthDate.Year()

	// Check if birthday has occurred this year
	if now.Month() < birthDate.Month() || (now.Month() == birthDate.Month() && now.Day() < birthDate.Day()) {
		age--
	}

	return age, nil
}
