package utils

import (
	"bytes"
	"encoding/json"
	"log"
	"reflect"
	"strings"
	"testing"

	m "imson/pkg/models"
)

// this function test tests the join categories function
func TestJoinCategories(t *testing.T) {
	type args struct {
		categories []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Empty list",
			args: args{categories: []string{}},
			want: "",
		},
		{
			name: "Single category",
			args: args{categories: []string{"Science"}},
			want: "Science",
		},
		{
			name: "Two categories",
			args: args{categories: []string{"Math", "Physics"}},
			want: "Math,Physics",
		},
		{
			name: "Multiple categories",
			args: args{categories: []string{"Art", "Biology", "Chemistry"}},
			want: "Art,Biology,Chemistry",
		},
		{
			name: "Category with empty string",
			args: args{categories: []string{"", "Tech"}},
			want: ",Tech",
		},
		{
			name: "All empty strings",
			args: args{categories: []string{"", "", ""}},
			want: ",,",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := JoinCategories(tt.args.categories); got != tt.want {
				t.Errorf("JoinCategories() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMustMarshal(t *testing.T) {
	type args struct {
		v interface{}
	}
	tests := []struct {
		name string
		args args
		want json.RawMessage
	}{
		{
			name: "Empty data",
			args: args{},
			want: json.RawMessage{110, 117, 108, 108},
		},
		{
			name: "Relevant post",
			args: args{
				m.Post{
					ID:             0,
					UserID:         "98u$&*HWHD(h9h9d8hqeudh99&$v",
					Title:          "Future Resonance",
					Content:        "I like the texhnology of that will come in the creation of my absurd thoughts and imaagination",
					Likes:          4000,
					AuthorNickname: "Genius",
				},
			},
			want: json.RawMessage{
				123, 34, 105, 100, 34, 58, 48, 44, 34, 117, 115, 101, 114, 73,
				100, 34, 58, 34, 57, 56, 117, 36, 92, 117, 48, 48, 50, 54, 42, 72, 87, 72, 68, 40, 104,
				57, 104, 57, 100, 56, 104, 113, 101, 117, 100, 104, 57, 57, 92, 117, 48, 48, 50, 54, 36,
				118, 34, 44, 34, 116, 105, 116, 108, 101, 34, 58, 34, 70, 117, 116, 117, 114, 101, 32, 82,
				101, 115, 111, 110, 97, 110, 99, 101, 34, 44, 34, 99, 111, 110, 116, 101, 110, 116, 34, 58,
				34, 73, 32, 108, 105, 107, 101, 32, 116, 104, 101, 32, 116, 101, 120, 104, 110, 111, 108, 111,
				103, 121, 32, 111, 102, 32, 116, 104, 97, 116, 32, 119, 105, 108, 108, 32, 99, 111, 109, 101, 32,
				105, 110, 32, 116, 104, 101, 32, 99, 114, 101, 97, 116, 105, 111, 110, 32, 111, 102, 32, 109, 121,
				32, 97, 98, 115, 117, 114, 100, 32, 116, 104, 111, 117, 103, 104, 116, 115, 32, 97, 110, 100, 32, 105,
				109, 97, 97, 103, 105, 110, 97, 116, 105, 111, 110, 34, 44, 34, 108, 105, 107, 101, 115, 34, 58, 52, 48,
				48, 48, 44, 34, 99, 97, 116, 101, 103, 111, 114, 121, 34, 58, 34, 34, 44, 34, 99, 111, 109, 109, 101, 110,
				116, 67, 111, 117, 110, 116, 34, 58, 48, 44, 34, 99, 114, 101, 97, 116, 101, 100, 65, 116, 34, 58, 34, 48,
				48, 48, 49, 45, 48, 49, 45, 48, 49, 84, 48, 48, 58, 48, 48, 58, 48, 48, 90, 34, 44, 34, 97, 117, 116, 104,
				111, 114, 78, 105, 99, 107, 110, 97, 109, 101, 34, 58, 34, 71, 101, 110, 105, 117, 115, 34, 44, 34, 97, 117,
				116, 104, 111, 114, 70, 105, 114, 115, 116, 78, 97, 109, 101, 34, 58, 34, 34, 44, 34, 97, 117, 116, 104, 111,
				114, 76, 97, 115, 116, 78, 97, 109, 101, 34, 58, 34, 34, 44, 34, 97, 117, 116, 104, 111, 114, 71, 101, 110, 100,
				101, 114, 34, 58, 34, 34, 125,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MustMarshal(tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MustMarshal() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSplitAndTrim(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "empty string",
			args: args{input: ""},
			want: []string{},
		},
		{
			name: "single word no commas",
			args: args{input: "golang"},
			want: []string{"golang"},
		},
		{
			name: "comma separated values with no spaces",
			args: args{input: "a,b,c"},
			want: []string{"a", "b", "c"},
		},
		{
			name: "comma separated values with spaces",
			args: args{input: "  a, b ,  c "},
			want: []string{"a", "b", "c"},
		},
		{
			name: "multiple spaces and empty entries",
			args: args{input: " , ,  a , , b ,, c ,"},
			want: []string{"a", "b", "c"},
		},
		{
			name: "trailing comma",
			args: args{input: "x,y,z,"},
			want: []string{"x", "y", "z"},
		},
		{
			name: "leading comma",
			args: args{input: ",x,y,z"},
			want: []string{"x", "y", "z"},
		},
		{
			name: "value with inner spaces preserved",
			args: args{input: "  New York , Los Angeles , Chicago  "},
			want: []string{"New York", "Los Angeles", "Chicago"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SplitAndTrim(tt.args.input); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SplitAndTrim() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMustMarshal_Success(t *testing.T) {
	input := map[string]string{"hello": "world"}
	result := MustMarshal(input)

	expected, _ := json.Marshal(input)
	if !bytes.Equal(result, expected) {
		t.Errorf("expected %s, got %s", string(expected), string(result))
	}
}

func TestMustMarshal_Failure(t *testing.T) {
	// Create a type that cannot be marshaled (has a function field)
	type Bad struct {
		Fn func()
	}

	// Capture log output
	var logBuf bytes.Buffer
	log.SetOutput(&logBuf)
	defer log.SetOutput(nil) // reset after test

	result := MustMarshal(Bad{Fn: func() {}})

	if result != nil {
		t.Errorf("expected nil, got: %v", result)
	}

	logOutput := logBuf.String()
	if !strings.Contains(logOutput, "Failed to marshal data") {
		t.Errorf("expected log to contain marshal error, got: %s", logOutput)
	}
}
