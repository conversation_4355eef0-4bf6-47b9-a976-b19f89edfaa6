package websocket

import (
	"sync"
)

// OnlineStatusManager manages user online status for WebSocket connections
type OnlineStatusManager struct {
	onlineUsers map[string]bool
	userStatus  map[string]string // "online", "away", "busy", "offline"
	mutex       sync.RWMutex
}

var (
	// Global instance for online status management
	StatusManager *OnlineStatusManager
	once          sync.Once
)

// GetStatusManager returns the singleton instance of OnlineStatusManager
func GetStatusManager() *OnlineStatusManager {
	once.Do(func() {
		StatusManager = &OnlineStatusManager{
			onlineUsers: make(map[string]bool),
			userStatus:  make(map[string]string),
		}
	})
	return StatusManager
}

// SetUserOnline marks a user as online
func (osm *OnlineStatusManager) SetUserOnline(userID string) {
	osm.mutex.Lock()
	defer osm.mutex.Unlock()
	osm.onlineUsers[userID] = true
	osm.userStatus[userID] = "online"
}

// SetUserOffline marks a user as offline
func (osm *OnlineStatusManager) SetUserOffline(userID string) {
	osm.mutex.Lock()
	defer osm.mutex.Unlock()
	osm.onlineUsers[userID] = false
	osm.userStatus[userID] = "offline"
}

// SetUserStatus sets a user's status (online, away, busy, offline)
func (osm *OnlineStatusManager) SetUserStatus(userID, status string) {
	osm.mutex.Lock()
	defer osm.mutex.Unlock()
	osm.userStatus[userID] = status
	if status == "offline" {
		osm.onlineUsers[userID] = false
	} else {
		osm.onlineUsers[userID] = true
	}
}

// IsUserOnline checks if a user is currently online
func (osm *OnlineStatusManager) IsUserOnline(userID string) bool {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	return osm.onlineUsers[userID]
}

// GetUserStatus returns the user's current status
func (osm *OnlineStatusManager) GetUserStatus(userID string) string {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	if status, exists := osm.userStatus[userID]; exists {
		return status
	}
	return "offline"
}

// GetOnlineUsersInGroup returns online status for a list of user IDs
func (osm *OnlineStatusManager) GetOnlineUsersInGroup(userIDs []string) map[string]bool {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	
	result := make(map[string]bool)
	for _, userID := range userIDs {
		result[userID] = osm.onlineUsers[userID]
	}
	return result
}

// GetOnlineCount returns the count of online users from a list
func (osm *OnlineStatusManager) GetOnlineCount(userIDs []string) int {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	
	count := 0
	for _, userID := range userIDs {
		if osm.onlineUsers[userID] {
			count++
		}
	}
	return count
}

// GetAllOnlineUsers returns all currently online users
func (osm *OnlineStatusManager) GetAllOnlineUsers() []string {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	
	var onlineUsers []string
	for userID, isOnline := range osm.onlineUsers {
		if isOnline {
			onlineUsers = append(onlineUsers, userID)
		}
	}
	return onlineUsers
}

// RemoveUser completely removes a user from tracking
func (osm *OnlineStatusManager) RemoveUser(userID string) {
	osm.mutex.Lock()
	defer osm.mutex.Unlock()
	delete(osm.onlineUsers, userID)
	delete(osm.userStatus, userID)
}

// GetUserStatusWithDetails returns detailed status information
func (osm *OnlineStatusManager) GetUserStatusWithDetails(userID string) map[string]interface{} {
	osm.mutex.RLock()
	defer osm.mutex.RUnlock()
	
	isOnline := osm.onlineUsers[userID]
	status := osm.userStatus[userID]
	if status == "" {
		status = "offline"
	}
	
	return map[string]interface{}{
		"is_online": isOnline,
		"status":    status,
		"last_seen": "", // Could be enhanced with timestamp tracking
	}
}
