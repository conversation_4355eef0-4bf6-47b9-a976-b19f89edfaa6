package models

import (
	"database/sql"
	"encoding/json"
	"time"
)

type RegisterRequest struct {
	Nickname    string `json:"nickname"`
	FirstName   string `json:"firstName"`
	LastName    string `json:"lastName"`
	Age         int    `json:"age"`
	Gender      string `json:"gender"`
	Email       string `json:"email"`
	Password    string `json:"password"`
	DateOfBirth string `json:"date_of_birth"`
	AvatarURL   string `json:"avatar_url"`
	AboutMe     string `json:"about_me"`
}

type User struct {
	ID                   string       `json:"id"`
	Nickname             string       `json:"nickname"`
	FirstName            string       `json:"firstName"`
	LastName             string       `json:"lastName"`
	Age                  int          `json:"age"`
	Email                string       `json:"email"`
	Password             string       `json:"-"`
	Online               int          `json:"online_status"`
	UnreadCount          int          `json:"unread_messages"`
	LastMessage          sql.NullTime `json:"last_message_time"` // this might not be useful in the future
	DateOfBirth          string       `json:"date_of_birth"`
	AvatarURL            string       `json:"avatar_url,omitempty"`
	CoverURL             string       `json:"cover_url,omitempty"`
	AboutMe              string       `json:"about_me,omitempty"`
	Private              bool         `json:"private"`
	EmailNotifications   bool         `json:"email_notifications"`
	PushNotifications    bool         `json:"push_notifications"`
	ShowOnlineStatus     bool         `json:"show_online_status"`
	AllowMessagesFromAll bool         `json:"allow_messages_from_all"`
	TwoFactorEnabled     bool         `json:"two_factor_enabled"`
	PostsCount           int          `json:"posts_count,omitempty"`
	CreatedAt            string       `json:"created_at,omitempty"`
}

type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"userId"`
	CreatedAt time.Time `json:"createdAt"`
	ExpiresAt time.Time `json:"expiresAt"`
}

type WSMessage struct {
	Type string          `json:"type"`
	Data json.RawMessage `json:"data"`
}

type FollowRequest struct {
	ID         int       `json:"id"`
	FollowerID int       `json:"follower_id"`
	Nickname   string    `json:"nickname"`
	FirstName  string    `json:"first_name"`
	LastName   string    `json:"last_name"`
	AvatarURL  string    `json:"avatar_url,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
}

type FollowerInfo struct {
	FollowerID  int `json:"follower_id"`
	FollowingID int `json:"following_id"`
}

type Message struct {
	ID            int       `json:"id"`
	SenderID      string    `json:"senderId"`
	ReceiverID    string    `json:"receiverId"`
	Content       string    `json:"content"`
	Timestamp     time.Time `json:"timestamp"`
	CurrentUserId string    `json:"currentUserId"`
	ReadStatus    int       `json:"read"`
}

// Profile represents user profile data
type Profile struct {
	ID          int          `json:"id"`
	Username    string       `json:"username"`
	Email       string       `json:"email"`
	Bio         string       `json:"bio,omitempty"`
	AvatarURL   string       `json:"avatar_url,omitempty"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Followers   int          `json:"followers_count"`
	Following   int          `json:"following_count"`
	PostsCount  int          `json:"posts_count"`
	IsFollowing bool         `json:"is_following"`
	IsPrivate   bool         `json:"is_private"`
	Location    string       `json:"location,omitempty"`
	Website     string       `json:"website,omitempty"`
	SocialLinks []SocialLink `json:"social_links,omitempty"`
}

// SocialLink represents a social media link
type SocialLink struct {
	Platform string `json:"platform"`
	URL      string `json:"url"`
}
type Post struct {
	ID              int       `json:"id"`
	UserID          string    `json:"userId"`
	Title           string    `json:"title,omitempty"`
	Content         string    `json:"content"`
	Image           string    `json:"image,omitempty"`
	Visibility      string    `json:"visibility"`
	Likes           int       `json:"likes"`
	Category        string    `json:"category,omitempty"`
	CommentCount    int       `json:"commentCount"`
	CreatedAt       time.Time `json:"createdAt"`
	AuthorNickname  string    `json:"authorNickname"`
	AuthorFirstName string    `json:"authorFirstName"`
	AuthorLastName  string    `json:"authorLastName"`
	AuthorGender    string    `json:"authorGender,omitempty"`
	AuthorAvatar    string    `json:"authorAvatar,omitempty"`
}

type LikePostObject struct {
	PostID int `json:"post_id"`
	Likes  int `json:"likes"`
}

type Reply struct {
	ID              int       `json:"id"`
	CommentID       int       `json:"commentId"`
	UserID          string    `json:"userId"`
	Content         string    `json:"content"`
	Likes           int       `json:"likes"`
	CreatedAt       time.Time `json:"createdAt"`
	AuthorNickname  string    `json:"authorNickname"`
	AuthorFirstName string    `json:"authorFirstName"`
	AuthorLastName  string    `json:"authorLastName"`
	AuthorGender    string    `json:"authorGender"`
}

type CreateReplyRequest struct {
	CommentID int    `json:"commentId"`
	Content   string `json:"content"`
}

type LikeReplyObject struct {
	ReplyID int `json:"reply_id"`
	Likes   int `json:"likes"`
}
type Comment struct {
	ID              int       `json:"id"`
	PostID          string    `json:"post_id"`
	UserID          string    `json:"user_id"`
	Content         string    `json:"content"`
	Likes           int       `json:"likes"`
	CreatedAt       time.Time `json:"created_at"`
	AuthorNickname  string    `json:"author_nickname"`
	AuthorFirstName string    `json:"author_first_name"`
	AuthorLastName  string    `json:"author_last_name"`
	AuthorGender    string    `json:"author_gender"`
	IsLikedByUser   bool      `json:"is_liked_by_user"`
}

type CreateCommentRequest struct {
	PostID  string `json:"post_id"`
	Content string `json:"content"`
}

type LikeCommentObject struct {
	CommentID int `json:"comment_id"`
	Likes     int `json:"likes"`
}

type Group struct {
	ID            int    `json:"group_id"`
	Name          string `json:"name"`
	CreatorID     string `json:"creator_id"`
	Cardinality   int    `json:"members"`
	Administrator string `json:"admin"`
	IsPrivate     bool   `json:"is_private"`
	About         string `json:"about"`
	Members       []string
	Topics        []string  `json:"topics"`
	CreatedAt     time.Time `json:"created_at"`
	UpdateAt      time.Time `json:"updated_at"`
}

type GroupMessage struct {
	ID            int    `json:"id"`
	GroupID       int    `json:"groupId"`
	SenderID      string `json:"senderId"`
	Content       string `json:"content"`
	CreatedAt     string `json:"createdAt"`
	CurrentUserId string `json:"currentUserId"`
	ReadStatus    int    `json:"read"`
}
