package models

import (
	"encoding/json"
	"time"
)

// Notification represents a user notification
type Notification struct {
	ID        int             `json:"id"`
	UserID    string          `json:"user_id"`
	Type      string          `json:"type"`
	Title     string          `json:"title"`
	Message   string          `json:"message"`
	Data      json.RawMessage `json:"data,omitempty"`
	IsRead    bool            `json:"is_read"`
	CreatedAt time.Time       `json:"created_at"`
}

// GroupMember represents a member of a group
type GroupMember struct {
	ID       int       `json:"id"`
	GroupID  int       `json:"group_id"`
	UserID   string    `json:"user_id"`
	Role     string    `json:"role"`
	JoinedAt time.Time `json:"joined_at"`
	// User details for display
	Nickname  string `json:"nickname,omitempty"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	AvatarURL string `json:"avatar_url,omitempty"`
}

// GroupInvitation represents an invitation to join a group
type GroupInvitation struct {
	ID        int       `json:"id"`
	GroupID   int       `json:"group_id"`
	InviterID string    `json:"inviter_id"`
	InviteeID string    `json:"invitee_id"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	// Additional fields for display
	GroupName     string `json:"group_name,omitempty"`
	InviterName   string `json:"inviter_name,omitempty"`
	InviterAvatar string `json:"inviter_avatar,omitempty"`
}

// GroupJoinRequest represents a request to join a group
type GroupJoinRequest struct {
	ID        int       `json:"id"`
	GroupID   int       `json:"group_id"`
	UserID    string    `json:"user_id"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	// Additional fields for display
	UserNickname string `json:"user_nickname,omitempty"`
	UserName     string `json:"user_name,omitempty"`
	UserAvatar   string `json:"user_avatar,omitempty"`
}

// Event represents a group event
type Event struct {
	ID          int       `json:"id"`
	GroupID     int       `json:"group_id"`
	CreatorID   string    `json:"creator_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	EventDate   time.Time `json:"event_date"`
	CreatedAt   time.Time `json:"created_at"`
	// Additional fields
	GroupName     string `json:"group_name,omitempty"`
	CreatorName   string `json:"creator_name,omitempty"`
	ResponseCount int    `json:"response_count,omitempty"`
	UserResponse  string `json:"user_response,omitempty"`
}

// EventResponse represents a user's response to an event
type EventResponse struct {
	ID        int       `json:"id"`
	EventID   int       `json:"event_id"`
	UserID    string    `json:"user_id"`
	Response  string    `json:"response"`
	CreatedAt time.Time `json:"created_at"`
}

// Notification data structures for different types
type FollowRequestNotificationData struct {
	RequesterID   string `json:"requester_id"`
	RequesterName string `json:"requester_name"`
	RequestID     int    `json:"request_id"`
}

type GroupInvitationNotificationData struct {
	GroupID      int    `json:"group_id"`
	GroupName    string `json:"group_name"`
	InviterID    string `json:"inviter_id"`
	InviterName  string `json:"inviter_name"`
	InvitationID int    `json:"invitation_id"`
}

type GroupJoinRequestNotificationData struct {
	GroupID       int    `json:"group_id"`
	GroupName     string `json:"group_name"`
	RequesterID   string `json:"requester_id"`
	RequesterName string `json:"requester_name"`
	RequestID     int    `json:"request_id"`
}

type EventNotificationData struct {
	EventID     int    `json:"event_id"`
	EventTitle  string `json:"event_title"`
	GroupID     int    `json:"group_id"`
	GroupName   string `json:"group_name"`
	CreatorID   string `json:"creator_id"`
	CreatorName string `json:"creator_name"`
}

type GroupMessageNotificationData struct {
	GroupID        int    `json:"group_id"`
	GroupName      string `json:"group_name"`
	SenderID       string `json:"sender_id"`
	SenderName     string `json:"sender_name"`
	MessagePreview string `json:"message_preview"`
}

// Request/Response structures
type CreateGroupRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	IsPrivate   bool   `json:"is_private"`
}

type InviteToGroupRequest struct {
	GroupID int    `json:"group_id"`
	UserID  string `json:"user_id"`
}

type JoinGroupRequest struct {
	GroupID int `json:"group_id"`
}

type CreateEventRequest struct {
	GroupID     int       `json:"group_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	EventDate   time.Time `json:"event_date"`
}

type EventResponseRequest struct {
	EventID  int    `json:"event_id"`
	Response string `json:"response"`
}

type GroupMessageRequest struct {
	GroupID int    `json:"group_id"`
	Content string `json:"content"`
}

type MarkNotificationsReadRequest struct {
	NotificationIDs []int `json:"notification_ids"`
}

// GroupDetails represents detailed information about a group for profile/preview
type GroupDetails struct {
	Group
	CreatorName          string        `json:"creator_name,omitempty"`
	CreatorAvatar        string        `json:"creator_avatar,omitempty"`
	BannerURL            string        `json:"banner_url,omitempty"`
	Rules                []string      `json:"rules,omitempty"`
	MembershipRequirements string      `json:"membership_requirements,omitempty"`
	AdminList            []GroupMember `json:"admin_list,omitempty"`
	Members              []GroupMember `json:"members,omitempty"`
	RecentEvents         []Event       `json:"recent_events,omitempty"`
	IsUserMember         bool          `json:"is_user_member"`
	UserRole             string        `json:"user_role,omitempty"`
	CanJoin              bool          `json:"can_join"`
	CanEdit              bool          `json:"can_edit"`
}

// GroupPreview represents a lightweight preview of group information
type GroupPreview struct {
	ID            int    `json:"id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	MemberCount   int    `json:"member_count"`
	IsPrivate     bool   `json:"is_private"`
	CreatorName   string `json:"creator_name,omitempty"`
	IsUserMember  bool   `json:"is_user_member"`
}
