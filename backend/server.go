package main

import (
	"log"
	"net/http"
	"strings"

	db "imson/pkg/db/sqlite"
	h "imson/pkg/handlers"

	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/gorilla/mux"
)

func main() {
	db, err := db.Init("imson.db", "./pkg/db/migrations/sqlite")
	if err != nil {
		log.Fatalf("db: %v", err)
	}
	defer db.Close()

	// Initialize notification service after database is ready
	h.InitNotificationService()

	r := mux.NewRouter()
	api := r.PathPrefix("/api").Subrouter()

	// Authentication routes
	api.HandleFunc("/signup", withCORS(h.RegisterHandler))
	api.HandleFunc("/login", withCORS(h.LoginHandler))
	api.HandleFunc("/logout", h.LogoutHandler)
	api.HandleFunc("/follow", h.FollowUser)
	api.HandleFunc("/follow-requests", h.GetPendingFollowRequests)
	api.HandleFunc("/follow-requests/accept", h.AcceptFollowRequest)
	api.HandleFunc("/follow-requests/decline", h.DeclineFollowRequest)
	api.HandleFunc("/ws", h.WebSocketHandler)

	// API endpoints for messages
	http.HandleFunc("/api/messages/read", h.ReadMessagesHandler)
	http.HandleFunc("/api/messages/send/", func(w http.ResponseWriter, r *http.Request) {
		pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/messages/send/"), "/")
		recipent_id := pathParts[0]
		h.SendMessageHandler(w, r, recipent_id)
	})

	// Register the routes for fetching messages and user details for the chat section
	http.HandleFunc("/api/messages/", func(w http.ResponseWriter, r *http.Request) {
		pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/messages/"), "/")
		userId := pathParts[0]
		h.FetchMessagesHandler(w, r, userId)
	})

	// Profile routes
	api.HandleFunc("/profile/{id}", h.GetProfileHandler).Methods("GET")
	api.HandleFunc("/profile/{id}", h.UpdateProfileHandler).Methods("PUT")
	api.HandleFunc("/profile/{id}", h.DeleteProfileHandler).Methods("DELETE")
	api.HandleFunc("/profile/page/{id}", h.GetProfilePageHandler).Methods("GET")
	api.HandleFunc("/profile/card/{id}", withCORS(h.GetProfileCardSummaryHandler)).Methods("GET")
	api.HandleFunc("/user/current", withCORS(h.GetCurrentUserHandler)).Methods("GET")
	api.HandleFunc("/user/current", withCORS(h.UpdateCurrentUserHandler)).Methods("PUT")

	// New profile functionality routes
	api.HandleFunc("/user/upload/cover", withCORS(h.UploadCoverPhotoHandler)).Methods("POST")
	api.HandleFunc("/user/upload/avatar", withCORS(h.UploadAvatarHandler)).Methods("POST")
	api.HandleFunc("/user/{id}/photos", withCORS(h.GetUserPhotosHandler)).Methods("GET")
	api.HandleFunc("/user/{id}/friends", withCORS(h.GetUserFriendsHandler)).Methods("GET")
	api.HandleFunc("/user/settings", withCORS(h.GetUserSettingsHandler)).Methods("GET")
	api.HandleFunc("/user/settings/update", withCORS(h.UpdateUserSettingsHandler)).Methods("PUT")
	api.HandleFunc("/comments/create", h.CreateCommentHandler)
	api.HandleFunc("/comments/get", h.GetCommentsHandler)
	api.HandleFunc("/comments/like", h.LikeCommentHandler)
	api.HandleFunc("/ws", h.WebSocketHandler)
	api.HandleFunc("/posts/my", withCORS(h.GetUserPosts))
	api.HandleFunc("/posts", withCORS(h.GetPostsHandler))
	api.HandleFunc("/posts/analytics", withCORS(h.GetPostAnalyticsHandler))
	api.HandleFunc("/posts/export", withCORS(h.ExportPostsHandler))
	api.HandleFunc("/replies", h.CreateReplyHandler).Methods("POST")
	api.HandleFunc("/replies", h.GetRepliesHandler).Methods("GET")

	http.HandleFunc("/api/replies/", func(w http.ResponseWriter, r *http.Request) {
		if len(r.URL.Path) > len("/api/replies/") {
			pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/replies/"), "/")
			if len(pathParts) == 2 && pathParts[1] == "like" {
				replyID := pathParts[0]
				if r.Method == http.MethodPost {
					h.LikeReplyHandler(w, r, replyID)
				} else {
					http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				}
				return
			}
		}
		http.NotFound(w, r)
	})
	http.HandleFunc("/api/posts/", func(w http.ResponseWriter, r *http.Request) {
		if len(r.URL.Path) > len("/api/posts/") {
			pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/posts/"), "/")
			if len(pathParts) == 2 && pathParts[1] == "like" {
				postId := pathParts[0]
				if r.Method == http.MethodPost {
					h.LikePostHandler(w, r, postId)
				} else {
					http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				}
				return
			}
		}
		http.NotFound(w, r)
	})

	// groups API endpoints
	api.HandleFunc("/groups/create", func(w http.ResponseWriter, r *http.Request) {
		h.CreateGroup(w, r, db)
	})
	api.HandleFunc("/groups/messages/{id}", func(w http.ResponseWriter, r *http.Request) {
		h.FetchGroupMessagesHandler(w, r, db)
	})
	api.HandleFunc("/groups/message/create", func(w http.ResponseWriter, r *http.Request) {
		h.SendGroupMessageHandler(w, r, db)
	})
	api.HandleFunc("/groups/join-requests", h.GetGroupJoinRequestsHandler)
	api.HandleFunc("/groups/join-requests/accept", h.AcceptGroupJoinRequestHandler)
	api.HandleFunc("/groups/join-requests/decline", h.DeclineGroupJoinRequestHandler)

	// Group routes
	api.HandleFunc("/groups/join", h.JoinGroupHandler).Methods("POST")
	api.HandleFunc("/groups/leave", h.LeaveGroupHandler).Methods("POST")
	api.HandleFunc("/groups/remove", h.RemoveUserFromGroupHandler).Methods("POST")

	// Notification routes
	api.HandleFunc("/notifications", h.GetNotificationsHandler)
	api.HandleFunc("/notifications/count", h.GetUnreadNotificationCountHandler)
	api.HandleFunc("/notifications/read", h.MarkNotificationsReadHandler)
	api.HandleFunc("/notifications/delete", h.DeleteNotificationHandler)

	// Group details and page routes (new functionality)
	api.HandleFunc("/groups/preview", h.GetGroupPreviewHandler).Methods("GET")
	api.HandleFunc("/groups/profile", h.GetGroupProfileHandler).Methods("GET")
	api.HandleFunc("/groups/page", h.GetGroupPageHandler).Methods("GET")
	api.HandleFunc("/groups/members", h.GetGroupMembersHandler).Methods("GET")

	// Event routes
	api.HandleFunc("/events", h.CreateEventHandler).Methods("POST")
	api.HandleFunc("/events", h.GetGroupEventsHandler).Methods("GET")
	api.HandleFunc("/events/respond", h.RespondToEventHandler)
	api.HandleFunc("/events/responses", h.GetEventResponsesHandler)

	// Serve static files
	fs := http.FileServer(http.Dir("./static"))
	r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", fs))

	log.Println("→ back‑end listening on :33233")
	log.Fatal(http.ListenAndServe(":33233", r))
}

// CORS middleware
func withCORS(h http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Allow multiple origins for development
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:3001",
		}

		origin := r.Header.Get("Origin")
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				w.Header().Set("Access-Control-Allow-Origin", origin)
				break
			}
		}

		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Methods", "POST, GET, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		// Handle OPTIONS requests (preflight)
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		// Call the actual handler
		h(w, r)
	}
}
