TODO*

# Ignore all .log files
*.log

# ignore all binary media files during production
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.bmp
*.tiff
*.tif
*.webp
*.svg
*.ttf
*.woff
*.woff2
*.eot
*.otf
*.otf
*.ttf
*.ttc
*.pfb
*.pfa
*.pcf
*.pcf.Z
*.afm
*.pfm
*.afm
*.pfm
*.afm
*.afm
*.afm
*.afm
*.afm
*.afm
*.afm
*.afm

# Go
/bin/
/coverage.out

# Node
node_modules/
.next/
dist/
out/

# Editor + OS
.idea/
.vscode/
.DS_Store

# Env / secrets
.env
.env.local
.env.development
.env.production
.env.test
imson.db

# development files
dev/

*.db
*.out
testing

# Testing artifacts
cookies*.txt
test_*.db
backend/server
# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS & Editor settings
.DS_Store
.idea/
.vscode/
*.swp
